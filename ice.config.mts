import { defineConfig } from '@ice/app';
import qiankun from '@ali/ice-plugin-qiankun';
import { getPublicPathUrl } from '@alife/mp-dev-shared-utils';
import tracker from '@alife/ice-plugin-mp-tracker';
import dayjs from 'dayjs';
import access from '@alife/ice-plugin-access';

const BUILD_TIME = dayjs().format('YYYY-MM-DD HH:mm:ss');
export default defineConfig({
  outputDir: 'dist',
  ssg: false,
  server: {
    onDemand: true,
    format: 'esm',
  },
  externals:
    process.env.NODE_ENV === 'development'
      ? {}
      : {
          react: 'React',
          'react-dom': 'ReactDOM',
        },
  publicPath: getPublicPathUrl() || '__WAP_ASSETS_CDN__/',
  sourceMap: true,
  plugins: [
    qiankun({
      type: 'child',
    }),
    tracker(),
    access(),
  ],
  minify: process.env.NODE_ENV === 'production',
  compileDependencies: false,
  codeSplitting: false,
  alias: {
    '@': './src',
    '@components': './src/components',
    react: '/node_modules/react',
    'react-dom': '/node_modules/react-dom',
  },
  htmlGenerating: true,
  routes: {
    ignoreFiles: ['**/components/**', '**/service/**'],
  },
  define: {
    BUILD_TIME: JSON.stringify({ value: BUILD_TIME }),
  },
});
