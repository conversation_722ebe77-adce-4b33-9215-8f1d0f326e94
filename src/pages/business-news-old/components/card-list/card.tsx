import styled from 'styled-components';
import { DataDisplayItem, IDataRecord } from '../handle-data';
import { DataDisplay } from '../data-display';
import { PropsWithChildren, useMemo } from 'react';

const CardHeader = styled.div`
  padding: 0.8em 1.2em;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;
const Container = styled.div`
  background: #fff;
  border-radius: 1.2em;
  margin-bottom: 1.4em;

  .card-container {
    background: #fbfbfb;
    font-size: 0.9em;
  }
  .card-content {
    padding: 0.8em 1.2em;
  }

  &.main-card {
    > .card-header {
      background: linear-gradient(90deg, #d6e4ff 0%, #f1f6ff 100%);
      border-radius: 1.2em 1.2em 0 0;
      margin-bottom: 8px;
    }
  }
`;

const CardTitle = styled.div`
  font-size: 1.6em;
  font-weight: 500;
`;
interface IProps {
  data?: IDataRecord;
  title?: string;
  isMainCard?: boolean;
}
const SubCard = styled.div`
  background: #f4f6f9;
  border-radius: 0.8em;
  padding: 0.8em;
  width: 100%;
`;

export default function DataCard(props: PropsWithChildren<IProps>) {
  const { data, title, isMainCard = true } = props;
  const children = useMemo(() => {
    if (!data?.children) {
      return null;
    }
    // @ts-ignore DataRecord类型
    if (data?.children?.[0]?.label) {
      return data.children.map((item) => {
        return <DataCard data={item} isMainCard={false} />;
      });
    }
    const expandChildren = (data.children as DataDisplayItem[]).map((item) => {
      return {
        ...item,
        expandChildren: item.expandChildrenData?.length ? (
          <SubCard>
            <DataDisplay items={item.expandChildrenData} />
          </SubCard>
        ) : undefined,
      };
    });
    return <DataDisplay items={expandChildren} />;
  }, [data]);
  if (!children && !props.children) return null;
  return (
    <Container className={`card-container ${isMainCard ? 'main-card' : ''}`}>
      <CardHeader className="card-header">
        <CardTitle>{title || data?.label}</CardTitle>
        {isMainCard && (
          <img
            style={{ width: '3.9em', height: '3.9em' }}
            src="https://img.alicdn.com/imgextra/i3/O1CN0130oqbC1c7mfUBRkwR_!!6000000003554-2-tps-156-156.png"
          />
        )}
      </CardHeader>
      {props.children ? props.children : <div className="card-content">{children}</div>}
    </Container>
  );
}
