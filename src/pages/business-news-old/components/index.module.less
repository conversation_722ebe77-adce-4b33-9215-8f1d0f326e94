.container {
  border-radius: 2.4em;
  position: relative;
  margin-bottom: 1.4em;
}

.wrap {
  background: #fff;
  margin: 2px;
  border-radius: 2.4em;
  width: 100%;
}

.title {
  font-size: 1.6em;
  font-weight: 500;
  line-height: 2.2em;
}

.cardWrap {
  padding: 1.4em;
  img {
    max-width: 90%;
  }
}

.carousel_content {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 1.4em;
}

.picture {
  border: 1px solid #ccc;
  width: 4.2em;
  height: 4.2em;
  line-height: 4.2em;
  text-align: center;
  font-weight: bolder;
  color: #f00;
  border-radius: 2.4em;
  margin-bottom: 2.8em;
  box-shadow: 0 10px 20px 0 rgba(186, 187, 190, 0.5);
}

.shopTitle {
  font-size: 1.8em;
  font-weight: bolder;
  margin-bottom: 2.8em;
}

.shopSubTitle {
  color: #666;
  line-height: 2.4em;
}

.shopImg {
  width: 80vw;
}
blockquote {
  margin-block-start: 0;
  margin-block-end: 0;
  margin-inline-start: 0;
  margin-inline-end: 0;
}

@font-face {
  font-family: 'AlibabaSans102-Bold';
  src: url('https://gw.alipayobjects.com/os/bmw-prod/ee04b4d3-f4a5-4e9e-8787-1350ecf98b96.ttf')
    format('truetype');
}

.card_content_bg {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.7em;
  font-weight: 700;
  color: #fff;
  font-family: 'AlibabaSans102-Bold';
  background-size: 4em;
  background-repeat: no-repeat;
  margin-right: 7px;
  background-image: url('https://img.alicdn.com/imgextra/i1/O1CN01VOuAfN1UIl3r1PE5U_!!6000000002495-2-tps-112-112.png');
  width: 4em;
  height: 4em;
}

.result_state_msg {
  display: inline-block;
  color: #ff350f;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-weight: 500;
}
.card_content_toDoBtn {
  height: 3em;
  width: 8em;
  background-image: linear-gradient(90deg, #ff5e33 0%, #f73 100%);
  border-radius: 1.7em;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2em;
  font-weight: 500;
  color: #fff;
}

.result_subTitle {
  color: #666;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
