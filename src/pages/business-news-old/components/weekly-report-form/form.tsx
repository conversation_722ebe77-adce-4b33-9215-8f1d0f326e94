import React, { useEffect, useState, useRef } from 'react';
import { Button, DatePicker, Form, Layout, message, Space } from 'antd';
import { isContentVisuallyEmpty } from '@/common/utils';
import './index.less';
import MpRichTextEditor from '@/components/mp-rich-text-editor';
import BdTextTpl from '@/components/bd-text-tpl';
import { sendGroupMsg } from '@/services/qw';
import { IfButtonShow } from '@/components/server-controller/useAction';
import { ActionButtonType } from '@/constants';
import BusinessNews from '../../index';
import dayjs from 'dayjs';
import StoreDetails from '../shop-detail';
import { NewBusinessNewsType, ITraceData } from '../const';
import { downloadShopDetail, getSummary } from './utils';
import WeeklyReportHiddenFields from '../weekly-report-hidden-field';
import { getFieldsMap } from '../handle-data/datas';
import ShopSelect from '@/components/shop-select';
import { useDataState } from '../store/useDataState';
import { Mo<PERSON>leSPMKey, PageSPMKey, trace, traceClick } from '@/utils/trace';

const { RangePicker } = DatePicker;
const { Sider, Content } = Layout;
const MAX_SHOP_COUNT = 1000;

const DateRangePresets: RangePicker['presets'] = [
  { label: '近一周', value: [dayjs().subtract(7, 'day'), dayjs().subtract(1, 'day')] },
  { label: '近一个月', value: [dayjs().subtract(30, 'day'), dayjs().subtract(1, 'day')] },
  { label: '近三个月', value: [dayjs().subtract(90, 'day'), dayjs().subtract(1, 'day')] },
  { label: '近一年', value: [dayjs().subtract(365, 'day'), dayjs().subtract(1, 'day')] },
];
export interface IShop {
  id: string;
  name: string;
}
export interface IDefaultShop {
  shop: IShop;
}
interface IProps {
  merchantName: string;
  defaultShop?: IDefaultShop;
  closeDrawer?: () => void;
  hasOptGroup?: boolean;
  style?: React.CSSProperties;
}

const WeeklyReportForm: React.FC<IProps> = ({
  merchantName,
  defaultShop,
  closeDrawer,
  hasOptGroup,
  style = {},
}) => {
  const { shopIdList, dateRange, pid, form, aiSummary, hiddenFields, shopList } = useDataState();

  const [hiddenHistory, setHiddenHistory] = useState<NewBusinessNewsType[]>([]);
  const templateRef = useRef<any>();
  const defaultDateRange = [dayjs().subtract(7, 'day'), dayjs().subtract(1, 'day')];
  const [summaryTemplate, setSummaryTemplate] = useState('');
  const summary = aiSummary.content || summaryTemplate;
  const [shopData, setShopData] = useState([]);
  const [shopFields, setShopFields] = useState([]);
  const [businessNewsType, setBusinessNewsType] = useState<NewBusinessNewsType>(
    NewBusinessNewsType.FOOD,
  );
  const startDate = dateRange?.[0];
  const endDate = dateRange?.[1];
  const [businessError, setBusinessError] = useState<any>(null);

  useEffect(() => {
    const { id, name } = defaultShop?.shop || {};
    if (id && name) {
      form.setFieldValue('shopList', [{ label: name, value: id }]);
    }
    form.setFieldValue('dateRange', defaultDateRange);
    setSummaryTemplate('');
  }, [defaultShop?.shop?.id]);

  async function sendReport(url: string) {
    if (!url) {
      return;
    }
    const { sendShopDetail } = form.getFieldsValue();
    const result = await templateRef?.current?.saveTemplate();
    const _startDate = startDate?.valueOf();
    const _endDate = endDate?.valueOf();
    let shopDetailUrl;
    if (sendShopDetail) {
      shopDetailUrl = await downloadShopDetail();
    }
    const res = await sendGroupMsg({
      merchantNewsReachModelList: [
        {
          pid,
          merchantNewsPageUrl: url,
          shopDetailListPageUrl: shopDetailUrl,
        },
      ],
      materialId: result?.materialId,
      from: 'MERCHANT_NEWS',
      startDate: _startDate,
      endDate: _endDate,
      shopDetailListPageUrl: shopDetailUrl,
    });
    const failureReason = res?.sendFailureList?.[0]?.failureReason;
    if (failureReason) {
      message.error(failureReason);
    } else {
      closeDrawer?.();
      message.success('推送成功');
    }
  }

  const handleDateChange = (dateRange: any) => {
    if (dateRange?.length) {
      if (dayjs(dateRange[1]).diff(dayjs(dateRange[0]), 'months') > 15) {
        message.warning('所选时间区间超过15个月，无法展示对比数据');
      }
      setSummaryTemplate('');
    }
  };

  // 门店接口加载完获取选择的门店
  const handleShopLoadFinish = (value: any[] = []) => {
    if (!defaultShop?.shop?.id) {
      form.setFieldValue('shopList', value);
    }
  };

  const disabledDate = (current: any) => {
    return (
      current.isBefore(dayjs().subtract(15, 'months'), 'days') ||
      current.isAfter(dayjs().subtract(1, 'days'), 'days')
    );
  };

  const handleHiddenFields = (value: any, type?: NewBusinessNewsType) => {
    if (type) {
      setHiddenHistory((pre) => [...pre, type]);
      form.setFieldValue('hiddenFields', [...(hiddenFields || []), ...value]);
    } else {
      form.setFieldValue('hiddenFields', value);
    }
  };

  const handleBlur = (value: any) => {
    const updateSummary = aiSummary.content ? aiSummary.updateAiSummaryText : setSummaryTemplate;
    trace('business-news-summary-update', aiSummary.content ? 'ai' : 'manual');
    if (isContentVisuallyEmpty(value)) {
      updateSummary('');
    } else {
      updateSummary(value);
    }
  };
  function handleShopFields(list: any[]): Array<{ title: string; key: string }> {
    const newFields = [];

    list.forEach((item) => {
      if (item.children) {
        newFields.push(...handleShopFields(item.children));
        if (item.label === '客资线索') {
          newFields.push(
            ...getFieldsMap(
              ['分区域客资成本', '分区域客资量占比', '分城市客资成本', '分城市客资量占比'],
              false,
            ),
          );
        }
      } else {
        newFields.push(item);
      }
    });
    return newFields;
  }
  const traceData: ITraceData = (businessData, detailData) => {
    const { data, businessNewType, formData } = businessData;
    const { fields } = detailData;
    setShopFields(fields);
    setShopData(detailData.shopData || []);
    setSummaryTemplate(
      getSummary(data, businessNewType as NewBusinessNewsType, { ...formData, shopList }),
    );
    setBusinessNewsType(businessNewType as NewBusinessNewsType);
  };
  const renderLeftContent = () => {
    return (
      <Content
        className="drawer-content"
        style={{
          marginRight: 12,
          height: '100%',
          overflowY: 'auto',
          width: '50%',
          padding: '5px 12px 12px 0',
        }}
      >
        <Form.Item required label="商户" className="merchant-name">
          {merchantName}
        </Form.Item>

        <Form.Item
          required
          // className="shop-select"
          label="选择门店"
          name="shopList"
          rules={[
            {
              validator: (rule, value, callback) => {
                if (value?.length) {
                  const _shopIdList = (value || []).map((item) => item.value);
                  if (_shopIdList.length > MAX_SHOP_COUNT) {
                    callback(`已选门店不得超过${MAX_SHOP_COUNT}家`);
                  } else {
                    callback();
                  }
                } else {
                  callback('至少选择一个门店');
                }
              },
            },
          ]}
        >
          <ShopSelect
            pid={pid}
            onLoadFinish={handleShopLoadFinish}
            dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
          />
        </Form.Item>
        <div id="weekly-form-portal" />
        <Form.Item
          required
          label="选择时间"
          name="dateRange"
          rules={[
            {
              validator(rule, value, callback) {
                if (!value) {
                  return callback('请选择时间');
                }
                callback();
              },
            },
          ]}
          initialValue={defaultDateRange}
        >
          <RangePicker
            separator="~"
            presets={DateRangePresets}
            showTime={false}
            disabledDate={disabledDate}
            onChange={(val) => handleDateChange(val || [])}
          />
        </Form.Item>
        <Form.Item name="hiddenFields" hidden initialValue={[]} />
        <IfButtonShow buttonType={ActionButtonType.一键发送企微群} ext={hasOptGroup}>
          <Form.Item label="企微群话术">
            <BdTextTpl ref={templateRef} shopList={shopList} />
          </Form.Item>
        </IfButtonShow>

        {summary && !businessError ? (
          <MpRichTextEditor
            value={summary}
            onBlur={handleBlur}
            loading={aiSummary.loading}
            fromAi={!!aiSummary.content}
            extraButton={
              aiSummary.error || (aiSummary.enable && !aiSummary.content) ? (
                <Space align="center" style={{ marginLeft: 12 }}>
                  {aiSummary.error ? <span style={{ color: 'red' }}>智能分析失败</span> : null}
                  <Button
                    onClick={() => {
                      traceClick(PageSPMKey.首页, ModuleSPMKey['喜报.AI智能分析'], {
                        pid,
                      });
                      aiSummary.createAiSummary({ reAnalysis: true });
                    }}
                    type="primary"
                    size="small"
                  >
                    {aiSummary.error ? '重新分析' : 'AI分析'}
                  </Button>
                </Space>
              ) : null
            }
          />
        ) : null}
        <WeeklyReportHiddenFields
          fields={shopFields}
          onHiddenFieldsChange={handleHiddenFields}
          hiddenFields={hiddenFields || []}
          businessNewsType={businessNewsType}
          hiddenHistory={hiddenHistory}
          pid={pid}
        />
        {shopIdList.length > 1 && !businessError ? (
          <StoreDetails
            form={form}
            pid={pid}
            fields={handleShopFields(shopFields)}
            shopData={shopData}
            isFood={businessNewsType === NewBusinessNewsType.FOOD}
          />
        ) : null}
      </Content>
    );
  };
  return (
    <Form
      className="create-weekly-report-drawer"
      labelCol={{
        style: {
          width: 100,
        },
      }}
      style={{ height: '100%' }}
      form={form}
    >
      <Layout style={{ width: '100%', ...style }}>
        {renderLeftContent()}
        <Sider
          className="drawer-sider"
          width="calc(50% - 12px)"
          style={{ background: 'unset', overflowX: 'hidden', minWidth: 380 }}
        >
          <BusinessNews
            form={form}
            summaryTemplate={summary}
            hasOptGroup={hasOptGroup}
            sendReport={sendReport}
            merchantName={merchantName}
            traceData={traceData}
            traceError={setBusinessError}
          />
        </Sider>
      </Layout>
    </Form>
  );
};

export default WeeklyReportForm;
