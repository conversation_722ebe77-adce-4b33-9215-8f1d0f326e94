/**
 * 管理者作战图相关类型定义
 */

// FilterOptions 已迁移至 src/types/index.ts（battle-map 区域）

/**
 * 用户信息接口
 */
export interface UserInfo {
  userId: string;
  displayName: string;
}

/**
 * 服务商信息接口
 */
export interface AgentInfo {
  agentId: string;
  agentName: string;
  shopCount?: number;
}

/**
 * 各阶段门店业务数据通用结构
 */
export interface StageShopBizData {
  layerQuestionMarkTips: string; // 小问号提示文案
  diagnosticResult: string; // 诊断描述文本
  shopNum: number; // 门店数量
  shopNumCycleRatio: number; // 环比变化值（小数）
  shopNumCycleRatioDesc: string; // 环比变化显示文案
  shopNumRatio: number; // 占比数值（小数）
  shopNumRatioDesc: string; // 占比显示文案
  shopNumRatioCycleRatio: number; // 占比环比变化（小数）
  shopNumRatioCycleRatioDesc: string; // 占比环比变化显示文案
  shopBizDataDistributions: Array<{
    label: string;
    value: number;
    desc: string;
  }>;
}

/**
 * 仪表盘接口数据
 */
export interface DashboardDataProps {
  diagnosticResult: string; // 总体诊断结果（汇总描述）

  newStageShopBizData: StageShopBizData;
  barrierStageShopBizData: StageShopBizData;
  growthStageShopBizData: StageShopBizData;
  matureStageShopBizData: StageShopBizData;
}

/**
 * 处理的仪表盘使用数据
 */
export interface MergedCardData {
  phase: string;
  cardTitle: string;
  cardDesc: string;
  chartTitle: string;
  chartDesc: string;
  type: string;

  tips: string;
  diagnosticResult: string;
  shopNum: number;
  shopNumCycleRatioDesc: string; // 如 "5.6%"
  shopNumRatioDesc: string; // 如 "10%"
  shopNumRatioCycleRatioDesc: string; // 如 "15%"

  chartsData: Array<{
    label: string;
    value: number;
    desc: string;
  }>;
}
