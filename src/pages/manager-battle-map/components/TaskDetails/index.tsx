/**
 * 任务明细
 */

import React, { useState, useEffect } from 'react';
import { Button, Table, Pagination, Spin, Select, DatePicker, Tabs } from 'antd';
import styled from 'styled-components';
import { useRequest } from 'ahooks';
import BatchTaskDrawer from './components/batch-task-drawer';
import TaskDistributeForm from './components/task-distribute-form';
import { queryTaskSummary, queryTaskDetail, queryTaskEnums } from '@/services';
import { useFilterOptions } from '@/context/FilterContext';

const { RangePicker } = DatePicker;

const PageContainer = styled.div`
  padding: 24px;
  background: #fff;
  min-height: 100vh;
`;

const HeaderSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
`;

const TabContainer = styled.div`
  display: flex;
  align-items: center;
`;

const BatchButton = styled(Button)`
  margin-left: auto;
`;

const TableContainer = styled.div`
  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
    white-space: nowrap;
  }
`;

const PaginationContainer = styled.div`
  margin-top: 16px;
  display: flex;
`;

const FilterContainer = styled.div`
  display: flex;
  margin-bottom: 16px;
`;

const FilterSection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;

  @media (min-width: 1200px) {
    grid-template-columns: repeat(3, 1fr) auto;
  }

  @media (max-width: 1199px) and (min-width: 768px) {
    grid-template-columns: repeat(1, 1fr) auto;
  }

  @media (max-width: 767px) {
    grid-template-columns: 1fr;
  }
`;

const FilterItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 0;
`;

const FilterLabel = styled.span`
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  white-space: nowrap;
  flex-shrink: 0;
`;

const FilterSelect = styled(Select)`
  flex: 1;
  min-width: 120px;
`;

const FilterDatePicker = styled(RangePicker)`
  flex: 1;
  min-width: 200px;
`;

const TaskSummary: React.FC = () => {
  const [activeTab, setActiveTab] = useState('summary');
  const [batchDrawerVisible, setBatchDrawerVisible] = useState(false);
  const [distributeFormVisible, setDistributeFormVisible] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const { filterOptions } = useFilterOptions();

  const { data: enumData } = useRequest(() => queryTaskEnums({ scene: 'TASK_DETAILS' }), {
    onError: (error) => {
      // eslint-disable-next-line no-console
      console.error('获取任务枚举失败:', error);
    },
  });

  const [taskFilters, setTaskFilters] = useState({
    taskType: undefined,
    taskSource: undefined,
    taskPublishDateRange: undefined,
    taskFollowUpDateRange: undefined,
  });

  // 获取任务汇总数据
  const {
    data: summaryData,
    loading: summaryLoading,
    run: runSummaryQuery,
  } = useRequest(
    () =>
      queryTaskSummary({
        ...filterOptions,
        taskType: taskFilters.taskType,
        taskSource: taskFilters.taskSource,
        taskPublishStartDate: taskFilters.taskPublishDateRange?.[0]?.format('YYYY-MM-DD'),
        taskPublishEndDate: taskFilters.taskPublishDateRange?.[1]?.format('YYYY-MM-DD'),
        taskFollowUpStartDate: taskFilters.taskFollowUpDateRange?.[0]?.format('YYYY-MM-DD'),
        taskFollowUpEndDate: taskFilters.taskFollowUpDateRange?.[1]?.format('YYYY-MM-DD'),
      }),
    {
      manual: true, // 只手动调用
      onError: (error) => {
        // eslint-disable-next-line no-console
        console.error('获取任务汇总失败:', error);
      },
    },
  );

  // 获取任务明细数据
  const {
    data: detailData,
    loading: detailLoading,
    refresh: refreshDetailQuery,
  } = useRequest(
    () =>
      queryTaskDetail({
        ...filterOptions,
        taskType: taskFilters.taskType,
        taskSource: taskFilters.taskSource,
        taskPublishStartDate: taskFilters.taskPublishDateRange?.[0]?.format('YYYY-MM-DD'),
        taskPublishEndDate: taskFilters.taskPublishDateRange?.[1]?.format('YYYY-MM-DD'),
        taskFollowUpStartDate: taskFilters.taskFollowUpDateRange?.[0]?.format('YYYY-MM-DD'),
        taskFollowUpEndDate: taskFilters.taskFollowUpDateRange?.[1]?.format('YYYY-MM-DD'),
        page: {
          pageNo: currentPage,
          pageSize,
        },
      }),
    {
      manual: true,
      refreshDeps: [currentPage, pageSize, activeTab],
      onError: (error) => {
        // eslint-disable-next-line no-console
        console.error('获取任务明细失败:', error);
      },
    },
  );

  const handleBatchTask = () => {
    setBatchDrawerVisible(true);
  };

  const handleBatchDrawerClose = () => {
    setBatchDrawerVisible(false);
  };

  const handleDistributeTask = () => {
    setDistributeFormVisible(true);
  };

  const handleDistributeFormClose = () => {
    setDistributeFormVisible(false);
  };

  const handleDistributeSuccess = () => {
    // 任务下发成功后刷新列表数据
    runSummaryQuery();
    refreshDetailQuery();
  };

  const handleTaskDistributed = () => {
    // 任务下发成功后刷新任务下发列表
    setRefreshTrigger((prev) => prev + 1);
  };

  // 分页变化时只调当前 tab 的接口
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  };

  const handleTaskFilterChange = (key: string, value: any) => {
    setTaskFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // 查询按钮
  const handleQuery = () => {
    if (activeTab === 'summary') {
      runSummaryQuery();
    } else if (currentPage !== 1) {
      // 如果已经在第1页，直接刷新数据
      setCurrentPage(1);
    } else {
      refreshDetailQuery();
    }
  };

  useEffect(() => {
    if (activeTab === 'summary') {
      runSummaryQuery();
    } else if (activeTab === 'detail') {
      refreshDetailQuery();
    }
  }, [activeTab, filterOptions, currentPage, pageSize]);

  const summaryColumns = [
    {
      title: '团队',
      dataIndex: 'displayName',
      key: 'displayName',
      width: 200,
      render: (text: string) => text || '-',
    },
    {
      title: '任务下发量',
      dataIndex: 'numOfTask',
      key: 'numOfTask',
      width: 120,
    },
    {
      title: '任务完成量',
      dataIndex: 'numOfFinishTask',
      key: 'numOfFinishTask',
      width: 120,
    },
    {
      title: '任务完成率',
      dataIndex: 'ratioOfFinishTask',
      key: 'ratioOfFinishTask',
      width: 120,
      render: (value: string) => value || '-',
    },
    {
      title: '任务目标完成量',
      dataIndex: 'numOfFinishTaskObject',
      key: 'numOfFinishTaskObject',
      width: 140,
      render: (value: string) => value || '-',
    },
    {
      title: '任务目标完成率',
      dataIndex: 'ratioOfFinishTaskObject',
      key: 'ratioOfFinishTaskObject',
      width: 140,
      render: (value: string) => value || '-',
    },
    {
      title: '主管催办量',
      dataIndex: 'numOfFollowUpDueTask',
      key: 'numOfFollowUpDueTask',
      width: 120,
    },
    {
      title: '主管催办完成量',
      dataIndex: 'numOfFinishFollowUpDueTask',
      key: 'numOfFinishFollowUpDueTask',
      width: 150,
      render: (value: string) => value || '-',
    },
    {
      title: '主管催办完成率',
      dataIndex: 'ratioOfFinishFollowUpDueTask',
      key: 'ratioOfFinishFollowUpDueTask',
      width: 150,
      render: (value: string) => value || '-',
    },
    {
      title: '系统推荐量',
      dataIndex: 'numOfRecTask',
      key: 'numOfRecTask',
      width: 120,
    },
    {
      title: '系统推荐完成量',
      dataIndex: 'numOfFinishRecTask',
      key: 'numOfFinishRecTask',
      width: 150,
      render: (value: string) => value || '-',
    },
    {
      title: '系统推荐完成率',
      dataIndex: 'ratioOfFinishRecTask',
      key: 'ratioOfFinishRecTask',
      width: 150,
      render: (value: string) => value || '-',
    },
    {
      title: '平均完成时效',
      dataIndex: 'timeRangeOfTask',
      key: 'timeRangeOfTask',
      width: 140,
      render: (value: string) => value || '-',
    },
    {
      title: '任务目标平均完成时效',
      dataIndex: 'timeRangeOfTaskObject',
      key: 'timeRangeOfTaskObject',
      width: 200,
      render: (value: string) => value || '-',
    },
  ];

  const detailColumns = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 200,
    },
    {
      title: '任务来源',
      dataIndex: 'taskSource',
      key: 'taskSource',
      width: 120,
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      width: 120,
    },
    {
      title: '任务目标状态',
      dataIndex: 'taskObjectStatus',
      key: 'taskObjectStatus',
      width: 150,
      render: (text: string) => text || '-',
    },
    {
      title: '是否催办',
      dataIndex: 'hasFollowUpDue',
      key: 'hasFollowUpDue',
      width: 100,
      render: (value: boolean) => (value ? '是' : '否'),
    },
    {
      title: '商户名称',
      dataIndex: 'merchantName',
      key: 'merchantName',
      render: (text: string) => text || '-',
    },
    {
      title: '商户ID',
      dataIndex: 'merchantId',
      key: 'merchantId',
      width: 150,
      render: (text: string) => text || '-',
    },
    {
      title: '门店名称',
      dataIndex: 'shopName',
      key: 'shopName',
      render: (text: string) => text || '-',
    },
    {
      title: '门店ID',
      dataIndex: 'shopId',
      key: 'shopId',
      width: 150,
      render: (text: string) => text || '-',
    },
    {
      title: '运维小二',
      dataIndex: 'employeeName',
      key: 'employeeName',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '三级部门',
      dataIndex: 'job3Name',
      key: 'job3Name',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '四级部门',
      dataIndex: 'job4Name',
      key: 'job4Name',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '服务商',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 150,
      render: (text: string) => text || '-',
    },
    {
      title: '下发日期',
      dataIndex: 'taskPublishDate',
      key: 'taskPublishDate',
      width: 120,
      render: (timestamp: number) => {
        if (!timestamp) return '-';
        return new Date(timestamp).toLocaleDateString('zh-CN');
      },
    },
    {
      title: '完成日期',
      dataIndex: 'taskFinishDate',
      key: 'taskFinishDate',
      width: 120,
      render: (timestamp: number) => {
        if (!timestamp) return '-';
        return new Date(timestamp).toLocaleDateString('zh-CN');
      },
    },
    {
      title: '目标完成日期',
      dataIndex: 'taskObjectFinishDate',
      key: 'taskObjectFinishDate',
      width: 140,
      render: (timestamp: number) => {
        if (!timestamp) return '-';
        return new Date(timestamp).toLocaleDateString('zh-CN');
      },
    },
    {
      title: '完成时效',
      dataIndex: 'timeRangeOfTask',
      key: 'timeRangeOfTask',
      width: 100,
      render: (value: string) => value || '-',
    },
  ];

  return (
    <PageContainer>
      <HeaderSection>
        <TabContainer>
          <Tabs
            type="card"
            activeKey={activeTab}
            onChange={(key) => {
              setActiveTab(key);
              setTaskFilters({
                taskType: undefined,
                taskSource: undefined,
                taskPublishDateRange: undefined,
                taskFollowUpDateRange: undefined,
              });
            }}
            items={[
              {
                key: 'summary',
                label: '任务汇总',
              },
              {
                key: 'detail',
                label: '任务明细',
              },
            ]}
          />
        </TabContainer>
        <BatchButton type="primary" onClick={handleBatchTask}>
          批量下发任务
        </BatchButton>
      </HeaderSection>

      <FilterContainer>
        <FilterSection>
          <FilterItem>
            <FilterLabel>任务名称:</FilterLabel>
            <FilterSelect
              placeholder="请选择"
              allowClear
              value={taskFilters.taskType}
              onChange={(value) => handleTaskFilterChange('taskType', value)}
              options={
                enumData?.taskTypes?.map((item: any) => ({
                  value: item.code,
                  label: item.desc,
                })) || []
              }
            />
          </FilterItem>

          <FilterItem>
            <FilterLabel>任务来源:</FilterLabel>
            <FilterSelect
              placeholder="请选择"
              allowClear
              value={taskFilters.taskSource}
              onChange={(value) => handleTaskFilterChange('taskSource', value)}
              options={
                enumData?.taskSources?.map((item: any) => ({
                  value: item.code,
                  label: item.desc,
                })) || []
              }
            />
          </FilterItem>

          <FilterItem>
            <FilterLabel>下发日期:</FilterLabel>
            <FilterDatePicker
              placeholder={['开始日期', '结束日期']}
              value={taskFilters.taskPublishDateRange}
              onChange={(value) => handleTaskFilterChange('taskPublishDateRange', value)}
            />
          </FilterItem>

          <FilterItem>
            <FilterLabel>催办日期:</FilterLabel>
            <FilterDatePicker
              placeholder={['催办开始日期', '催办结束日期']}
              value={taskFilters.taskFollowUpDateRange}
              onChange={(value) => handleTaskFilterChange('taskFollowUpDateRange', value)}
            />
          </FilterItem>

          <Button
            type="primary"
            onClick={handleQuery}
            style={{
              gridColumn: '-1',
              justifySelf: 'end',
              alignSelf: 'start',
            }}
          >
            查询
          </Button>
        </FilterSection>
      </FilterContainer>

      <TableContainer>
        <Spin spinning={activeTab === 'summary' ? summaryLoading : detailLoading}>
          <Table
            columns={activeTab === 'summary' ? summaryColumns : detailColumns}
            dataSource={activeTab === 'summary' ? summaryData?.dataList : detailData?.dataList}
            pagination={false}
            scroll={{ x: 3000 }}
            expandable={
              activeTab === 'summary'
                ? {
                    defaultExpandAllRows: false,
                    expandRowByClick: false,
                  }
                : undefined
            }
            rowKey={(record, index) => (record as any).key || index}
          />
        </Spin>
      </TableContainer>

      {activeTab === 'detail' && (
        <PaginationContainer>
          <Pagination
            current={currentPage}
            total={detailData?.pageInfo?.totalCount || 0}
            pageSize={pageSize}
            showSizeChanger
            showQuickJumper
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
          />
        </PaginationContainer>
      )}

      <BatchTaskDrawer
        visible={batchDrawerVisible}
        onClose={handleBatchDrawerClose}
        onDistributeTask={handleDistributeTask}
        refreshTrigger={refreshTrigger}
      />

      <TaskDistributeForm
        visible={distributeFormVisible}
        onClose={handleDistributeFormClose}
        onSuccess={handleDistributeSuccess}
        onTaskDistributed={handleTaskDistributed}
      />
    </PageContainer>
  );
};

export default TaskSummary;
