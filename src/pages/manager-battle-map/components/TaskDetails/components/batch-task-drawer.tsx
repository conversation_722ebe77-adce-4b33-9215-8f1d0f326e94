import React, { useState, useEffect } from 'react';
import { Drawer, Button, Table, Pagination, Space, Spin } from 'antd';
import { useRequest } from 'ahooks';
import styled from 'styled-components';
import { querySupervisorTask, resultDownload } from '@/services';
import { downloadUtil } from '@/common/utils';

const DrawerContent = styled.div`
  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
  }
`;

const ActionSection = styled.div`
  margin-bottom: 16px;
`;

interface IBatchTaskDrawerProps {
  visible: boolean;
  onClose: () => void;
  onDistributeTask: () => void;
  refreshTrigger?: number;
}

const BatchTaskDrawer: React.FC<IBatchTaskDrawerProps> = ({
  visible,
  onClose,
  onDistributeTask,
  refreshTrigger,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 获取任务下发列表数据
  const {
    data: taskData,
    loading,
    refresh: refreshTaskList,
  } = useRequest(
    () =>
      querySupervisorTask({
        pageNo: currentPage,
        pageSize,
      }),
    {
      refreshDeps: [currentPage, pageSize],
      ready: visible,
    },
  );

  const columns = [
    {
      title: '下发时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 150,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      align: 'center',
      width: 150,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      align: 'center',
      width: 200,
    },
    {
      title: '下发状态',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      width: 120,
      align: 'center',
      render: (status: string) => {
        const statusMap: Record<string, string> = {
          INCOMPLETE: '处理中',
          COMPLETED: '下发成功',
        };
        return <span>{statusMap[status] || status}</span>;
      },
    },
    {
      title: '下发成功数',
      dataIndex: 'successCount',
      key: 'successCount',
      width: 120,
      render: (text: any, record: any) => `${record?.succTotal || 0}/${record?.total || 0}`,
    },
    {
      title: '明细下载',
      dataIndex: 'downloadUrl',
      key: 'downloadUrl',
      width: 120,
      render: (_: any, record: any) =>
        record?.taskStatus === 'COMPLETED' && record?.resultFileKey ? (
          <a
            href="#"
            onClick={async (e) => {
              e.preventDefault();
              const url = (await resultDownload({ resultFileKey: record.resultFileKey }))
                ?.downloadUrl;
              if (url) downloadUtil(url);
            }}
            style={{ color: '#1890ff' }}
          >
            下载明细
          </a>
        ) : null,
    },
  ];

  const handleDistributeTask = () => {
    onDistributeTask();
  };

  // 触发刷新
  useEffect(() => {
    if (visible && refreshTrigger !== undefined && refreshTrigger > 0) {
      refreshTaskList();
    }
  }, [refreshTrigger, visible, refreshTaskList]);

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  };

  return (
    <>
      <Drawer title="任务下发" placement="right" width={1000} open={visible} onClose={onClose}>
        <DrawerContent>
          <ActionSection>
            <Space>
              <Button type="primary" onClick={handleDistributeTask}>
                下发任务
              </Button>
            </Space>
          </ActionSection>

          <Spin spinning={loading}>
            <Table
              columns={columns}
              dataSource={
                taskData?.dataList
                  ?.slice((currentPage - 1) * pageSize, currentPage * pageSize)
                  ?.map((item, index) => ({
                    ...item,
                    key: item.taskNo || index.toString(),
                  })) || []
              }
              pagination={false}
              scroll={{ x: 800 }}
            />
          </Spin>

          <div style={{ marginTop: 16, display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              current={currentPage}
              total={taskData?.pageInfo?.totalCount || 0}
              pageSize={pageSize}
              showSizeChanger
              showQuickJumper
              onChange={handlePageChange}
              onShowSizeChange={handlePageChange}
            />
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};

export default BatchTaskDrawer;
