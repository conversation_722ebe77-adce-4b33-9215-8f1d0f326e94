/**
 * 绩效组件类型定义
 */
import type { FilterOptions } from '@/types';

export interface PerformanceData {
  indicatorCode: string;
  indicatorName: string;
  indicatorValue: string;
  unit: string;
  targetValue: string;
  targetCompletionRate: string;
  monthOnMonthRate: string;
  weekOnWeekRate: string;
}

export interface PerformanceQueryParams extends FilterOptions {
  gatewaySource?: string;
  queryDate?: string;
  pageSource: string;
  viewOperatorId?: string;
}

export interface PerformanceResponse {
  result: boolean;
  code: string;
  message: string | null;
  version: string;
  timestamp: string;
  success: boolean;
  msgInfo: string;
  msgCode: string;
  traceId: string;
  data: {
    result: boolean;
    traceId: string;
    code: string;
    data: {
      dataList: PerformanceData[];
    };
    success: boolean;
    message: string | null;
    msgInfo: string;
    version: string;
    msgCode: string;
    timestamp: string;
  };
}

export interface PerformanceMetricCardProps {
  data: PerformanceData;
  isMainCard?: boolean;
  isSelected?: boolean;
  onClick?: () => void;
  queryDate: string;
  cardGroup: 'income' | 'other';
}

export interface PerformanceChartProps {
  // data: PerformanceData[];
  selectedIndicator?: string;
  filterOptions?: FilterOptions;
  queryDate?: string;
  pageSource: string;
  loading: boolean;
  chartData: BarChartData[];
  fetchChartData: Function;
  pageInfo: {
    totalCount: number;
    pageNo: number;
    pageSize: number;
    hasMore: boolean;
  };
}

// 图表数据查询相关类型
export interface PerformanceChartQueryParams extends FilterOptions {
  queryDate: string;
  pageSource: string;
  page?: {
    pageNo: number;
    pageSize: number;
  };
  jumpSource?: string;
  selectedIndicator?: string;
  commonOperatorInfo?: {
    operatorId: string;
    operatorName: string;
  };
}

export interface BarChartData {
  indicatorName: string;
  entityType: string;
  entityName: string;
  targetCompletionRate: string;
  indicatorCode: string;
  entityId: string;
  class: string;
  currentValue: string;
  monthOnMonthRate: string | null;
  weekOnWeekRate: string | null;
  colorValue?: string;
}

export interface PageInfo {
  totalCount: number;
  pageNo: number;
  pageSize: number;
  hasMore: boolean;
}

export interface PerformanceChartResponse {
  result: boolean;
  code: string;
  message: string | null;
  version: string;
  timestamp: string;
  success: boolean;
  msgInfo: string;
  msgCode: string;
  traceId: string;
  data: {
    result: boolean;
    traceId: string;
    code: string;
    data: {
      dataList: BarChartData[];
      pageInfo: PageInfo;
      disclaimer: string;
    };
    success: boolean;
    message: string | null;
    msgInfo: string;
    version: string;
    msgCode: string;
    timestamp: string;
  };
}
