/**
 * 任务效果相关类型定义
 */
import type { FilterOptions } from '@/types';

// 门店漏斗数据
export interface ShopFunnel {
  totalOemStores: number; // 运维门店总数（有商户运维关系的商户下门店总数）
  recommendedStores: number; // 推荐门店数（有任务推荐的门店总数）
  recommendedStoresRatio: number; // 推荐门店占比（百分比）
  recommendedCompletedStores: number; // 推荐完成门店数（有推荐任务完成的门店总数）
  completedStoresRatio: number; // 完成门店占比（百分比）
}

// 推荐任务漏斗数据
export interface TaskFunnel {
  recommendedTasks: number; // 推荐任务数（推荐的所有任务数量）
  tasksToBeCompleted: number; // 应完成任务数（截止到今天推荐过期的任务总数）
  tasksToBeCompletedRatio: number; // 应完成任务占比（百分比）
  completedTasks: number; // 任务完成数（截止到今天已完成的任务总数）
  taskCompletionRate: number; // 任务完成率（百分比）
  targetCompletedTasks: number; // 目标完成数（截止到今天目标完成的任务总数）
  taskTargetCompletionRate: number; // 任务目标完成率（百分比）
}

// 任务效果数据（完整响应，包含漏斗和效果对比）
export interface TaskEffectData {
  shopFunnel?: ShopFunnel;
  taskFunnel?: TaskFunnel;
  voucherQuantityPerStore?: TaskEffectComparisonDTO; // 店均日均凭证量效果对比
  exposurePvPerStore?: TaskEffectComparisonDTO; // 店均日均曝光PV效果对比
  ctcvrPerStore?: TaskEffectComparisonDTO; // 店均日均CTCVR效果对比
}

// 效果对比数据（单个指标）
export interface TaskEffectComparisonDTO {
  beforeValue: number; // 完成前数值
  afterValue: number; // 完成后数值
  growthRate: number; // 完成后增长率（百分比，如：23表示23%）
  marketGrowthRate: number; // 大盘增长率（百分比）
  industryGrowthRate: number; // 行业增长率（百分比）
}

// 任务效果查询参数（对应接口文档的 OptTaskEffectRequest）
export interface TaskEffectQueryParams extends FilterOptions {
  pageSource: string; // 页面来源，支持：AGENT_PAGE（小二页面）、MANAGER_PAGE（管理者页面）
  viewOperatorId?: string; // 查看人ID，当需要查看他人数据时传入
  startDate: string; // 查询开始日期，格式：yyyyMMdd，如：20251002
  endDate: string; // 查询结束日期，格式：yyyyMMdd，如：20251010
  industryType?: string; // 行业类型，如：美食（可选参数）
}

// API 响应类型（对应接口文档的 ResultDTO<OptTaskEffectDTO>）
export interface TaskEffectResponse {
  success: boolean;
  model: TaskEffectData; // 对应接口文档的 OptTaskEffectDTO
  errorCode?: string;
  errorMsg?: string;
}

// 行业增长率数据（对应接口文档的 IndustryGrowthRateDTO）
export interface IndustryGrowthRateDTO {
  voucherQuantityIndustryGrowthRate: number; // 店均日均凭证量行业增长率
  exposurePvIndustryGrowthRate: number; // 店均日均曝光PV行业增长率
  ctcvrIndustryGrowthRate: number; // 店均日均CTCVR行业增长率
}

// 行业增长率响应类型（对应接口文档的 ResultDTO<IndustryGrowthRateDTO>）
export interface IndustryGrowthRateResponse {
  success: boolean;
  model: IndustryGrowthRateDTO;
  errorCode?: string;
  errorMsg?: string;
}

// 行业类型列表数据（对应接口文档的 IndustryTypeListDTO）
export interface IndustryTypeListDTO {
  industryTypes: string[]; // 行业类型列表
}

// 行业类型列表响应类型（对应接口文档的 ResultDTO<IndustryTypeListDTO>）
export interface IndustryTypeListResponse {
  success: boolean;
  model: IndustryTypeListDTO;
  errorCode?: string;
  errorMsg?: string;
}
