/**
 * 任务效果相关API服务
 */

import { gdRequest } from '@/services/request';
import type {
  TaskEffectQueryParams,
  TaskEffectResponse,
  IndustryGrowthRateResponse,
  IndustryTypeListResponse,
} from '../types/taskEffect';

/**
 * 查询任务效果数据（包括门店漏斗、任务漏斗和效果对比数据）
 * HSF：com.amap.sales.operation.client.FireMapsFacade#queryTaskEffect
 * @param params 查询参数
 * @returns 任务效果数据响应
 */
export const queryTaskEffect = async (
  params: TaskEffectQueryParams,
): Promise<TaskEffectResponse['model']> => {
  try {
    const response = await gdRequest('amap-sales-operation.FireMapsFacade.queryTaskEffect', params);
    return response.model || response;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('查询任务效果数据失败:', error);
    throw error;
  }
};

/**
 * 查询行业增长率数据
 * HSF：com.amap.sales.operation.client.FireMapsFacade#queryIndustryGrowthRate
 * @param params 查询参数（与queryTaskEffect相同）
 * @returns 行业增长率数据响应
 */
export const queryIndustryGrowthRate = async (
  params: TaskEffectQueryParams,
): Promise<IndustryGrowthRateResponse['model']> => {
  try {
    const response = await gdRequest(
      'amap-sales-operation.FireMapsFacade.queryIndustryGrowthRate',
      params,
    );
    return response.model || response;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('查询行业增长率数据失败:', error);
    throw error;
  }
};

/**
 * 查询可查询的行业类型列表
 * HSF：com.amap.sales.operation.client.FireMapsFacade#queryIndustryTypes
 * @param params 查询参数（与queryTaskEffect相同）
 * @returns 行业类型列表响应
 */
export const queryIndustryTypes = async (
  params: TaskEffectQueryParams,
): Promise<IndustryTypeListResponse['model']> => {
  try {
    const response = await gdRequest(
      'amap-sales-operation.FireMapsFacade.queryIndustryTypes',
      params,
    );
    return response.model || response;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('查询行业类型列表失败:', error);
    throw error;
  }
};
