/**
 * 绩效相关API服务
 */

import request, { gdRequest } from '@/services/request';
import type {
  PerformanceQueryParams,
  PerformanceResponse,
  PerformanceChartQueryParams,
  PerformanceChartResponse,
} from '../types';

/**
 * 查询绩效目标数据
 * @param params 查询参数
 * @returns 绩效数据响应
 */
export const queryPerformanceTarget = async (
  params: PerformanceQueryParams,
): Promise<PerformanceResponse['data']['data']> => {
  try {
    const response = await gdRequest(
      'amap-sales-operation.FireMapsFacade.queryPerformanceTargets',
      params,
    );
    return response;
  } catch (error) {
    console.error('查询绩效目标数据失败:', error);
    throw error;
  }
};

/**
 * 查询绩效图表数据
 * @param params 查询参数
 * @returns 绩效图表数据响应
 */
export const queryPerformanceDataComparison = async (
  params: PerformanceChartQueryParams,
): Promise<PerformanceChartResponse['data']['data']> => {
  try {
    const response = await gdRequest(
      'amap-sales-operation.FireMapsFacade.queryPerformanceDataBar',
      params,
    );

    return response;
  } catch (error) {
    console.error('查询绩效图表数据失败:', error);
    throw error;
  }
};

/**
 * 模拟绩效数据 - 用于开发调试
 * @param params 查询参数
 * @returns 模拟的绩效数据
 */
export const mockPerformanceData = async (
  params: PerformanceQueryParams,
): Promise<PerformanceResponse['data']['data']> => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return {
    dataList: [
      {
        indicatorCode: 'TOTAL_INCOME',
        indicatorName: '总收入',
        indicatorValue: '2645.34',
        unit: '万',
        targetValue: '251',
        targetCompletionRate: '82.5%',
        monthOnMonthRate: '^10%',
        weekOnWeekRate: '3%',
      },
      {
        indicatorCode: 'AD_REVENUE',
        indicatorName: '广告收入',
        indicatorValue: '1234.34',
        unit: '万',
        targetValue: '180',
        targetCompletionRate: '25%',
        monthOnMonthRate: '^10%',
        weekOnWeekRate: '^3%',
      },
      {
        indicatorCode: 'ANNUAL_FEE_REVENUE',
        indicatorName: '年费收入',
        indicatorValue: '373.29',
        unit: '万',
        targetValue: '120',
        targetCompletionRate: '35%',
        monthOnMonthRate: '^10%',
        weekOnWeekRate: '^3%',
      },
      {
        indicatorCode: 'CPS_REVENUE',
        indicatorName: 'CPS收入',
        indicatorValue: '522.72',
        unit: '万',
        targetValue: '50',
        targetCompletionRate: '105%',
        monthOnMonthRate: '^10%',
        weekOnWeekRate: '^3%',
      },
      {
        indicatorCode: 'INTELLIGENT_BODY_REVENUE',
        indicatorName: '智能体收入',
        indicatorValue: '522.72',
        unit: '万',
        targetValue: '10',
        targetCompletionRate: '0%',
        monthOnMonthRate: '^10%',
        weekOnWeekRate: '^3%',
      },
      {
        indicatorCode: 'FIRST_CHARGE_RATE',
        indicatorName: '首充率',
        indicatorValue: '1234.34',
        unit: '万',
        targetValue: '90',
        targetCompletionRate: '95%',
        monthOnMonthRate: '^10%',
        weekOnWeekRate: '^3%',
      },
      {
        indicatorCode: 'RETENTION_RATE',
        indicatorName: '留存率',
        indicatorValue: '234.34',
        unit: '万',
        targetValue: '80',
        targetCompletionRate: '98%',
        monthOnMonthRate: '^10%',
        weekOnWeekRate: '^3%',
      },
      {
        indicatorCode: 'GMV',
        indicatorName: 'GMV',
        indicatorValue: '34.34',
        unit: '万',
        targetValue: '1000',
        targetCompletionRate: '45%',
        monthOnMonthRate: '^10%',
        weekOnWeekRate: '3%',
      },
    ],
  };
};

/**
 * 模拟绩效图表数据 - 用于开发调试
 * @param params 查询参数
 * @returns 模拟的绩效图表数据
 */
export const mockPerformanceChartData = async (
  params: PerformanceChartQueryParams,
): Promise<PerformanceChartResponse['data']['data']> => {
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 800));

  // 根据选中的指标生成不同的模拟数据
  const generateMockData = (indicator: string) => {
    const baseData = [
      { teamCode: 'TEAM001', teamName: '北京销售团队', staffId: 'STAFF001', staffName: '张三' },
      { teamCode: 'TEAM002', teamName: '上海销售团队', staffId: 'STAFF002', staffName: '李四' },
      { teamCode: 'TEAM003', teamName: '广州销售团队', staffId: 'STAFF003', staffName: '王五' },
      { teamCode: 'TEAM004', teamName: '深圳销售团队', staffId: 'STAFF004', staffName: '赵六' },
      { teamCode: 'TEAM005', teamName: '杭州销售团队', staffId: 'STAFF005', staffName: '钱七' },
      { teamCode: 'TEAM006', teamName: '成都销售团队', staffId: 'STAFF006', staffName: '孙八' },
      { teamCode: 'TEAM007', teamName: '武汉销售团队', staffId: 'STAFF007', staffName: '周九' },
      { teamCode: 'TEAM008', teamName: '西安销售团队', staffId: 'STAFF008', staffName: '吴十' },
    ];

    // 根据不同指标生成不同的数值范围
    const valueRanges = {
      TOTAL_INCOME: [200, 400],
      AD_REVENUE: [150, 300],
      ANNUAL_FEE_REVENUE: [80, 200],
      CPS_REVENUE: [50, 150],
      INTELLIGENT_BODY_REVENUE: [10, 50],
      FIRST_CHARGE_RATE: [60, 95],
      RETENTION_RATE: [70, 90],
      GMV: [100, 250],
    };

    const [min, max] = valueRanges[indicator] || [100, 300];

    return baseData.map((item) => ({
      ...item,
      currentValue: (Math.random() * (max - min) + min).toFixed(2),
    }));
  };

  return {
    dataList: generateMockData(params.selectedIndicator),
    pageInfo: {
      totalCount: 15,
      pageNo: 1,
      pageSize: 10,
      hasMore: true,
    },
    disclaimer: '绩效数据最终以绩效组下发为准，此数据仅供参考',
  };
};
