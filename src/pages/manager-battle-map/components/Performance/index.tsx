/**
 * 绩效组件
 */

import React, { useEffect, useState, useRef } from 'react';
import { Card, Form, DatePicker, Flex, Tabs } from 'antd';
import dayjs from 'dayjs';
import PerformanceMetrics from './components/PerformanceMetrics';
import PerformanceChart from './components/PerformanceChart';
import TaskEffect from './components/TaskEffect';
import {
  queryPerformanceDataComparison,
  queryPerformanceTarget,
} from './services/performanceService';
import { queryTaskEffect } from './services/taskEffectService';
import type { TaskEffectQueryParams } from './types/taskEffect';
import type {
  BarChartData,
  PerformanceChartQueryParams,
  PerformanceData,
  PerformanceQueryParams,
} from './types';
import type { FilterOptions } from '@/types';
import styles from './index.module.less';
import { useStore } from '@/context/global-store';
import { PageSPMKey, ModuleSPMKey, traceExp, traceClick } from '@/utils/trace';

// 设置初始日期为昨天
const initialDate = dayjs().subtract(1, 'day');
const defaultPageSize = 40;

interface PerformanceProps {
  pageSource: string;
  showChart?: boolean;
  filterOptions: FilterOptions;
  mergedWithNext?: boolean;
}

const Performance: React.FC<PerformanceProps> = ({
  pageSource,
  showChart = true,
  filterOptions,
  mergedWithNext = false,
}) => {
  const { viewer } = useStore() || {};
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState<string>('performance');
  const [selectedIndicator, setSelectedIndicator] = useState<string>('TOTAL_INCOME'); // 默认选中总收入
  const [loading, setLoading] = useState(false);
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const filterOptionsRef = useRef({});
  const [taskEffectHasData, setTaskEffectHasData] = useState<boolean | undefined>(undefined); // undefined表示未检查，true表示有数据，false表示无数据
  const taskEffectCheckedRef = useRef(false); // 用于标记是否已经检查过

  // 预检查任务效果数据是否有数据（只在初始化时检查一次）
  useEffect(() => {
    if (taskEffectCheckedRef.current) {
      // 已经检查过，不需要重复检查
      return;
    }

    const checkTaskEffectData = async () => {
      taskEffectCheckedRef.current = true;
      try {
        // 使用默认日期范围（过去30天到昨天）
        const startDate = dayjs().subtract(30, 'day').format('YYYYMMDD');
        const endDate = dayjs().subtract(1, 'day').format('YYYYMMDD');

        const params: TaskEffectQueryParams = {
          pageSource,
          viewOperatorId: viewer || undefined,
          startDate,
          endDate,
          industryType: '美食', // 使用默认行业类型
          ...filterOptions,
        };

        const data = await queryTaskEffect(params);
        const hasData =
          !!data?.shopFunnel ||
          !!data?.taskFunnel ||
          !!data?.voucherQuantityPerStore ||
          !!data?.exposurePvPerStore ||
          !!data?.ctcvrPerStore;
        setTaskEffectHasData(hasData);
      } catch (error) {
        // 如果请求失败，默认显示tab（避免因错误导致tab不显示）
        // eslint-disable-next-line no-console
        console.error('检查任务效果数据失败:', error);
        setTaskEffectHasData(true);
      }
    };

    checkTaskEffectData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageSource, viewer, filterOptions]);
  // 获取绩效数据
  const fetchPerformanceData = async (params?: Partial<PerformanceQueryParams>) => {
    setLoading(true);
    try {
      const defaultParams: PerformanceQueryParams = {
        pageSource,
        viewOperatorId: viewer || undefined,
        ...params,
        ...filterOptions,
      };

      const response = await queryPerformanceTarget(defaultParams);
      if (response?.dataList) {
        setPerformanceData(response?.dataList);
      }
    } catch (error) {
      console.error('获取绩效数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 时间筛选变化
  const onValuesChange = (values: any) => {
    // eslint-disable-next-line no-console
    console.log('时间筛选变化:', values, '全局筛选条件:', filterOptions);
    if (values.queryDate) {
      const formattedDate = values.queryDate.format('YYYYMMDD');
      // D 区埋点：绩效目标.日期选择
      traceClick(PageSPMKey.首页, ModuleSPMKey['绩效目标.日期选择'], {
        queryDate: formattedDate,
      });
      fetchPerformanceData({ queryDate: formattedDate });
      showChart &&
        fetchChartData({
          page: { pageNo: 1, pageSize: defaultPageSize },
          queryDate: formattedDate,
        });
    }
  };

  // 处理指标卡片点击（埋点已在 PerformanceMetrics 组件中处理）
  const handleIndicatorSelect = (indicatorCode: string) => {
    setSelectedIndicator(indicatorCode);
  };

  // C 区埋点：绩效目标曝光
  useEffect(() => {
    traceExp(PageSPMKey.首页, ModuleSPMKey['绩效目标'], {});
  }, []);

  // 初始化数据
  useEffect(() => {
    if (JSON.stringify(filterOptionsRef.current || {}) === JSON.stringify(filterOptions || {})) {
      return;
    }
    const queryDate = form.getFieldValue('queryDate');
    const formattedDate = queryDate ? queryDate.format('YYYYMMDD') : initialDate.format('YYYYMMDD');
    fetchPerformanceData({ queryDate: formattedDate });
    filterOptionsRef.current = filterOptions;
  }, [filterOptions, viewer]);

  const [chartLoading, setChartLoading] = useState(false);
  const [chartData, setChartData] = useState<BarChartData[]>([]);
  const [pageInfo, setPageInfo] = useState({
    totalCount: 0,
    pageNo: 1,
    pageSize: defaultPageSize,
    hasMore: false,
  });

  // 获取图表数据
  const fetchChartData = async (params?: Partial<PerformanceChartQueryParams>) => {
    if (params.page.pageNo === 1) {
      setChartLoading(true);
    }
    try {
      const defaultParams: PerformanceChartQueryParams = {
        pageSource,
        selectedIndicator,
        queryDate: params.queryDate,
        viewOperatorId: viewer || undefined,
        ...filterOptions,
        page: {
          ...params.page,
        },
      };

      const response = await queryPerformanceDataComparison(defaultParams);
      if (response) {
        if (params.page.pageNo === 1) {
          setChartData(response.dataList || []);
        } else {
          setChartData((prev) => [...prev, ...response.dataList]);
        }
        setPageInfo({
          totalCount: response.pageInfo.totalCount,
          pageNo: params.page.pageNo,
          pageSize: params.page.pageSize,
          hasMore: response.dataList?.length >= pageInfo.pageSize,
        });
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('获取绩效图表数据失败:', error);
    } finally {
      setChartLoading(false);
    }
  };

  // 初始化数据和监听指标变化
  useEffect(() => {
    if (!showChart) {
      return;
    }
    const queryDate = form.getFieldValue('queryDate');
    const formattedDate = queryDate ? queryDate.format('YYYYMMDD') : initialDate.format('YYYYMMDD');
    fetchChartData({ page: { pageNo: 1, pageSize: defaultPageSize }, queryDate: formattedDate });
  }, [selectedIndicator, filterOptions]);

  const tabItems = [
    {
      key: 'performance',
      label: '绩效目标',
      children: (
        <Flex gap={24} vertical>
          {/* 标题和日期选择器 */}
          <Flex justify="space-between" align="center" className={styles.performanceHeader}>
            <div className={styles.titleContainer}>
              <span className={styles.title}>绩效目标</span>
              <span className={styles.disclaimer}>
                绩效数据最终以绩效组下发为准，此数据仅供参考
              </span>
            </div>
            <Form onValuesChange={onValuesChange} form={form} layout="inline">
              <Form.Item
                name="queryDate"
                label=""
                initialValue={initialDate}
                style={{ marginInlineEnd: 0 }}
              >
                <DatePicker
                  allowClear={false}
                  style={{ width: 120 }}
                  format="YYYYMMDD"
                  placeholder="选择日期"
                  showToday={false}
                  disabledDate={(current) => {
                    if (current && current >= dayjs().startOf('day')) {
                      return true;
                    }
                    if (current && current < dayjs().subtract(180, 'day').startOf('day')) {
                      return true;
                    }
                    return false;
                  }}
                />
              </Form.Item>
            </Form>
          </Flex>

          {/* 绩效指标卡片 */}
          <PerformanceMetrics
            data={performanceData}
            selectedIndicator={selectedIndicator}
            onIndicatorSelect={handleIndicatorSelect}
            queryDate={
              form.getFieldValue('queryDate')?.format('YYYYMMDD') || initialDate.format('YYYYMMDD')
            }
          />

          {/* 绩效指标&完成度图表 */}
          {showChart && (
            <PerformanceChart
              pageSource={pageSource}
              filterOptions={filterOptions}
              queryDate={
                form.getFieldValue('queryDate')?.format('YYYYMMDD') ||
                initialDate.format('YYYYMMDD')
              }
              selectedIndicator={selectedIndicator}
              loading={chartLoading}
              chartData={chartData}
              pageInfo={pageInfo}
              fetchChartData={fetchChartData}
            />
          )}
        </Flex>
      ),
    },
    ...(taskEffectHasData === true
      ? [
          {
            key: 'taskEffect',
            label: '任务效果',
            children: (
              <TaskEffect
                pageSource={pageSource}
                filterOptions={filterOptions}
                queryDate={
                  form.getFieldValue('queryDate')?.format('YYYYMMDD') ||
                  initialDate.format('YYYYMMDD')
                }
              />
            ),
          },
        ]
      : []),
  ];

  return (
    <Card
      bodyStyle={{ padding: '16px 24px' }}
      loading={loading && activeTab === 'performance'}
      style={
        mergedWithNext
          ? {
              borderBottomLeftRadius: 0,
              borderBottomRightRadius: 0,
              border: 0,
            }
          : {}
      }
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        className={styles.tabs}
      />
    </Card>
  );
};

export default Performance;
