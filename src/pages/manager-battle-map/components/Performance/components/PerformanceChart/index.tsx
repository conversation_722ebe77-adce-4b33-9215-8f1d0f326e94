/**
 * 绩效指标&完成度图表组件
 */

import React, { useState, useMemo, useEffect } from 'react';
import { Button, Empty } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import BarChart from '@/components/charts/bar';
import type { PerformanceChartProps } from '../../types';

import styles from './index.module.less';

// 颜色映射常量
const EmergencyLevelOptionMap = {
  ORANGE: '按时序不可达标',
  BLUE: '按时序可达标',
};

const EmergencyLevelColorMap = {
  ORANGE: '#FA8C16',
  BLUE: '#1890FF',
};

const PerformanceChart: React.FC<PerformanceChartProps> = ({
  selectedIndicator,
  queryDate,
  loading,
  chartData,
  pageInfo,
  fetchChartData,
}) => {
  const [isScrolledToEnd, setIsScrolledToEnd] = useState(false);

  useEffect(() => {
    setIsScrolledToEnd(false);
  }, [selectedIndicator]);

  // 转换数据格式用于 BarChart 组件
  const chartDataFormatted = useMemo(() => {
    if (!chartData || chartData.length === 0) {
      return {
        xAxis: [],
        series: [],
      };
    }

    return {
      xAxis: chartData.map((item) => ({
        value: item.entityName,
        name: item.entityName,
      })),
      series: chartData.map((item) => {
        const colorValue = (item as any).colorValue || 'BLUE';
        const color = EmergencyLevelColorMap[colorValue] || EmergencyLevelColorMap.BLUE;
        return {
          ...item,
          name: item.entityName,
          value: parseFloat(item.currentValue) || 0,
          indicatorValue: parseFloat(item.currentValue) || 0,
          indicatorValuePercent: item.targetCompletionRate
            ? parseFloat(item.targetCompletionRate.replace('%', ''))
            : 0,
          mainShopName: item.entityName,
          pid: item.entityId || item.entityName,
          economicLevel: colorValue,
          itemStyle: {
            color,
          },
        };
      }),
    };
  }, [chartData]);

  // 加载更多数据
  const handleLoadMore = () => {
    const nextPage = pageInfo.pageNo + 1;
    fetchChartData({
      page: { pageNo: nextPage, pageSize: pageInfo.pageSize },
      queryDate,
    });
  };

  // 滚动事件处理
  const handleScroll = (params: any) => {
    setIsScrolledToEnd(params.end === 100);
  };

  // 判断是否为百分比指标
  const isPercent = selectedIndicator.includes('RATE') || selectedIndicator.includes('PERCENT');

  return (
    <div className={styles.chartCard}>
      <div className={styles.chartTitle}>绩效指标&完成度</div>
      <div className={styles.dataSourceChart}>
        {loading && (
          <div className={styles.loadingState}>
            <p>加载中...</p>
          </div>
        )}
        {!loading && chartDataFormatted.xAxis.length > 0 && (
          <div style={{ margin: '0 -24px' }}>
            <BarChart
              allowLegendClick={false}
              barWidth={30}
              legend={[
                { name: EmergencyLevelOptionMap.ORANGE, color: EmergencyLevelColorMap.ORANGE },
                { name: EmergencyLevelOptionMap.BLUE, color: EmergencyLevelColorMap.BLUE },
              ]}
              width="100%"
              updated={false}
              onScroll={handleScroll}
              isPercent={isPercent}
              data={chartDataFormatted}
              onItemClick={() => {
                // 处理点击事件，可以在这里添加具体的业务逻辑
              }}
              tooltip={{
                trigger: 'axis',
                renderMode: 'html',
                enterable: true,
                extraCssText: 'user-select: text;',
                showDelay: 0,
                formatter: (params) => {
                  const record = params?.[0]?.data;
                  const value = record.indicatorValue ? record.indicatorValue + record.unit : '-';
                  return `<div>
                    <div>名称: ${record.mainShopName}</div>
                    <div>指标值：${value}</div>
                    <div>完成率：${
                      record.indicatorValuePercent && record.indicatorValuePercent !== 0
                        ? `${record.indicatorValuePercent}%`
                        : '-'
                    }</div>
                  </div>`;
                },
              }}
            />
          </div>
        )}
        {!loading && chartDataFormatted.xAxis.length === 0 && (
          <div className={styles.emptyState}>
            <Empty />
          </div>
        )}
        {pageInfo.hasMore && isScrolledToEnd && (
          <div
            style={{
              position: 'absolute',
              right: 10,
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 1,
              background: 'rgba(255,255,255,0.8)',
              borderRadius: '50%',
            }}
          >
            <Button
              shape="circle"
              icon={<RightOutlined />}
              onClick={handleLoadMore}
              style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.15)' }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceChart;
