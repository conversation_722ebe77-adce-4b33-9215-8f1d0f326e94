/**
 * 绩效指标卡片组件
 */

import React from 'react';
import { Row, Col } from 'antd';
import PerformanceMetricCard from './PerformanceMetricCard';
import type { PerformanceData } from '../../types';
import { PageSPMKey, ModuleSPMKey, traceClick } from '@/utils/trace';

interface PerformanceMetricsProps {
  data: PerformanceData[];
  selectedIndicator: string;
  onIndicatorSelect: (indicatorCode: string) => void;
  queryDate: string;
}

const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({
  data,
  selectedIndicator,
  onIndicatorSelect,
  queryDate,
}) => {
  // 筛选收入指标（前5个主要指标）
  const incomeMetrics = data.filter((item) =>
    [
      'TOTAL_INCOME',
      'AD_REVENUE',
      'ANNUAL_FEE_REVENUE',
      'CPS_REVENUE',
      'INTELLIGENT_BODY_REVENUE',
    ].includes(item.indicatorCode),
  );

  // 筛选其他指标（后3个指标）
  const otherMetrics = data.filter((item) =>
    ['FIRST_CHARGE_RATE', 'RETENTION_RATE', 'GMV'].includes(item.indicatorCode),
  );

  // 处理指标卡片点击
  const handleIndicatorClick = (indicatorCode: string) => {
    // D 区埋点：绩效目标.指标卡片点击
    traceClick(PageSPMKey.首页, ModuleSPMKey['绩效目标.指标卡片点击'], {
      indicatorCode,
      queryDate,
    });
    onIndicatorSelect(indicatorCode);
  };

  return (
    <>
      {/* 收入指标行 */}
      <Row gutter={[16, 16]}>
        {incomeMetrics.map((item, index) => (
          <Col key={item.indicatorCode} span={index === 0 ? 8 : 4}>
            <PerformanceMetricCard
              data={item}
              isMainCard={index === 0}
              isSelected={selectedIndicator === item.indicatorCode}
              onClick={() => handleIndicatorClick(item.indicatorCode)}
              queryDate={queryDate}
              cardGroup="income"
            />
          </Col>
        ))}
      </Row>

      {/* 其他指标行 */}
      <Row gutter={[16, 16]}>
        {otherMetrics.map((item) => (
          <Col key={item.indicatorCode} span={8}>
            <PerformanceMetricCard
              data={item}
              isSelected={selectedIndicator === item.indicatorCode}
              onClick={() => handleIndicatorClick(item.indicatorCode)}
              queryDate={queryDate}
              cardGroup="other"
            />
          </Col>
        ))}
      </Row>
    </>
  );
};

export default PerformanceMetrics;
