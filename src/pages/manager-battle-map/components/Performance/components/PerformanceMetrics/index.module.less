.metricCard {
  position: relative;
  height: 100%; // 调整为更高的尺寸
  transition: all 0.3s ease;

  &.selectedCard {
    :global {
      .ant-pro-statistic-card {
        background-color: #e6f2ff !important;
        border-color: #1890ff !important;
      }
    }
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  :global {
    .ant-pro-statistic-card {
      border-radius: 8px;
      height: 100% !important; // 强制卡片填满容器
      display: flex;
      flex-direction: column;

      .ant-pro-card-header {
        border-bottom: none;
        flex-shrink: 0; // 防止头部被压缩
      }

      .ant-pro-card-body {
        flex: 1; // 让body占据剩余空间
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
}

.normalStatisticCard {
  :global {
    .ant-pro-statistic-card-statistic-value {
      font-weight: 600;
    }
  }
}

.cardTitle {
  display: flex;
  align-items: center;

  span {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    margin-right: 4px;
  }
}

.helpIcon {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.targetInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 12px;
  text-align: right;
}

.targetRow {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 2px;
}

.targetLabel {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  line-height: 22px;
}

.targetValue {
  color: #1890ff;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
}

.targetDetail {
  color: #333;
  font-size: 12px;
  line-height: 1.2;
}

.targetNumber {
  font-weight: 500;
}

.targetInfoBottom {
  width: 100%;
  margin-bottom: 8px;
}

.targetRowSingle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.targetLeft,
.targetRight {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unit {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-left: 4px;
}

.selectedText {
  color: #1890ff !important;
}

.trendContainer {
  width: 100%;
  flex: 1; // 让趋势容器占据剩余空间
  display: flex;
  flex-direction: column;
  justify-content: flex-end; // 趋势数据靠底部对齐
  margin-top: 4px;

  :global {
    // 进度条样式
    .ant-progress {
      margin-bottom: 8px; // 进度条和趋势数据之间的间距

      .ant-progress-bg {
        border-radius: 10px;
      }

      .ant-progress-outer {
        .ant-progress-inner {
          border-radius: 10px;
          background-color: #f0f0f0;
        }
      }
    }

    .ant-pro-statistic-card-statistic {
      text-align: left;

      .ant-pro-statistic-card-statistic-title {
        margin-bottom: 2px;
      }

      .ant-pro-statistic-card-statistic-content {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
}

.trendLabel {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  width: 42px;
}

.trendDataContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.trendItem {
  display: flex;
  flex-direction: row;
  align-items: center;

  flex-shrink: 0;

  // 当容器宽度不足时，让每个项目占据完整宽度
  @media (max-width: 120px) {
    flex-basis: 100%;
    margin-bottom: 2px;
  }
}

.trendValue {
  display: flex;
  align-items: center;
  font-size: 12px;
}
