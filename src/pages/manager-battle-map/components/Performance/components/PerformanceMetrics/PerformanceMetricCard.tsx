/**
 * 单个绩效指标卡片
 */

import React from 'react';
import { Typography, Space, Tooltip, Progress } from 'antd';
import { RiseOutlined, FallOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { StatisticCard } from '@ant-design/pro-components';
import type { PerformanceMetricCardProps } from '../../types';

import styles from './index.module.less';

const { Text } = Typography;

// 指标说明映射
const INDICATOR_TIPS = {
  TOTAL_INCOME: '与绩效口径一致，=广告收入+年费续签+CPS收入+智能体收入',
  AD_REVENUE: '与绩效口径一致，广告现金消耗',
  ANNUAL_FEE_REVENUE: '与绩效口径一致，年费续签收入',
  CPS_REVENUE: '与绩效口径一致，抽佣收入',
  INTELLIGENT_BODY_REVENUE: '与绩效口径一致，存量智能体收入',
  FIRST_CHARGE_RATE: '14天实充商户数/应充商户数(限定现金充值)',
  RETENTION_RATE:
    '以门店为维度，季度只进不出的方式考核在投门店留存，一个季度周期清零一次；举例：4月考核周期：1/2/3月有现金消耗的门店中，4月现金消耗大于等于100元的门店占比',
  GMV: '与绩效口径一致，核销GMV（除低频SKA、低频KA外全部展示）',
};

// 解析日期，获取日期天数
const getDayFromDate = (dateStr: string) => {
  // dateStr 格式: "20251009"
  if (dateStr && dateStr.length === 8) {
    return parseInt(dateStr.substring(6, 8), 10);
  }
  return 0;
};

const PerformanceMetricCard: React.FC<PerformanceMetricCardProps> = ({
  data,
  isMainCard = false,
  isSelected = false,
  onClick,
  queryDate,
  cardGroup,
}) => {
  const {
    indicatorName,
    indicatorValue,
    unit,
    targetValue,
    targetCompletionRate,
    monthOnMonthRate,
    weekOnWeekRate,
    indicatorCode,
  } = data;

  // 格式化数值显示
  const formatValue = (value: string) => {
    if (!value || value === '0.0') {
      return '0';
    }
    return value;
  };

  // 获取趋势图标和样式
  const getTrendIcon = (rate: string) => {
    if (!rate || rate === '0%') return null;

    // 解析数值，判断正负
    const cleanValue = rate.replace(/[^-\d.]/g, ''); // 移除非数字字符，保留负号和小数点
    const numericValue = parseFloat(cleanValue);
    const isNegative = numericValue < 0;

    // >=0 是红色，<0 是绿色
    const color = isNegative ? '#52C41A' : '#F5222D';

    return {
      icon: isNegative ? <FallOutlined /> : <RiseOutlined />,
      color,
      value: rate,
    };
  };

  // 判断周环比是否应该显示提示文案
  const getWeekTrendDisplay = () => {
    const day = getDayFromDate(queryDate);
    const threshold = cardGroup === 'income' ? 8 : 15;
    const tipText = cardGroup === 'income' ? '8日有数据' : '15日有数据';

    if (day < threshold) {
      return { type: 'tip', text: tipText };
    }

    // 日期满足条件，检查是否有数据
    if (!weekOnWeekRate || weekOnWeekRate === '0%') {
      return { type: 'empty', text: '--' };
    }

    // 有数据，返回趋势信息
    const trendInfo = getTrendIcon(weekOnWeekRate);
    return { type: 'trend', data: trendInfo };
  };

  const monthTrend = getTrendIcon(monthOnMonthRate);
  const weekTrendDisplay = getWeekTrendDisplay();
  const formattedValue = formatValue(indicatorValue);

  // 标题渲染
  const titleRender = () => {
    const tooltipContent = INDICATOR_TIPS[indicatorCode] || `${indicatorName}指标说明`;

    return (
      <div className={styles.cardTitle}>
        <span className={isSelected ? styles.selectedText : ''}>{indicatorName}</span>
        <Tooltip title={tooltipContent}>
          <QuestionCircleOutlined className={styles.helpIcon} />
        </Tooltip>
      </div>
    );
  };

  // 计算进度条百分比
  const getProgressPercent = () => {
    const completion = parseFloat(targetCompletionRate?.replace('%', '')) || 0;
    return Math.min(completion, 100); // 限制最大值为100%
  };

  // 趋势描述渲染
  const descriptionRender = () => {
    return (
      <Space direction="vertical" size={2} className={styles.trendContainer}>
        {/* 趋势数据 - 始终展示 */}
        <div className={styles.trendDataContainer}>
          {/* 月环比 */}
          <div className={styles.trendItem}>
            <div className={styles.trendLabel} style={{ fontSize: 12 }}>
              月环比
            </div>
            <div
              className={styles.trendValue}
              style={{
                color: monthTrend ? monthTrend.color : '#00000073',
                fontSize: 12,
              }}
            >
              {monthTrend ? (
                <>
                  {monthTrend.icon}
                  <span style={{ marginLeft: 4 }}>{monthTrend.value}%</span>
                </>
              ) : (
                '--'
              )}
            </div>
          </div>

          {/* 周环比 */}
          <div className={styles.trendItem}>
            <div className={styles.trendLabel} style={{ fontSize: 12 }}>
              周环比
            </div>
            <div
              className={styles.trendValue}
              style={{
                color:
                  weekTrendDisplay.type === 'trend' && weekTrendDisplay.data
                    ? weekTrendDisplay.data.color
                    : '#00000073',
                fontSize: 12,
              }}
            >
              {(weekTrendDisplay.type === 'empty' || weekTrendDisplay.type === 'tip') &&
                weekTrendDisplay.text}
              {weekTrendDisplay.type === 'trend' && weekTrendDisplay.data && (
                <>
                  {weekTrendDisplay.data.icon}
                  <span style={{ marginLeft: 4 }}>{weekTrendDisplay.data.value}%</span>
                </>
              )}
            </div>
          </div>
        </div>

        {/* 进度条 */}
        {isMainCard && (
          <Progress
            percent={getProgressPercent()}
            showInfo={false}
            strokeColor="#1890ff"
            trailColor="#ffffff"
            size="small"
          />
        )}

        {/* 目标完成度和目标信息 */}
        {isMainCard && (
          <div className={styles.targetInfoBottom}>
            <div className={styles.targetRowSingle}>
              <div className={styles.targetLeft}>
                <Text className={styles.targetLabel}>目标完成率</Text>
                <Text className={styles.targetValue}>
                  {targetCompletionRate ? `${targetCompletionRate}%` : '--'}
                </Text>
              </div>
              <div className={styles.targetRight}>
                <Text className={styles.targetLabel}>目标</Text>
                <Text className={styles.targetNumber}>
                  {targetValue ? targetValue + unit : '--'}
                </Text>
              </div>
            </div>
          </div>
        )}
      </Space>
    );
  };

  return (
    <div
      className={`${styles.metricCard} ${isSelected ? styles.selectedCard : ''}`}
      onClick={onClick}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <StatisticCard
        bordered
        hoverable
        title={titleRender()}
        statistic={{
          value: formattedValue,
          valueStyle: {
            color: isSelected ? '#1890ff' : '#000',
            fontWeight: 'bold',
          },
          suffix: (
            <span className={`${styles.unit} ${isSelected ? styles.selectedText : ''}`}>
              {unit}
            </span>
          ),
          description: descriptionRender(),
        }}
        extra={null}
        className={styles.normalStatisticCard}
        style={{
          // height: 180, // 调整为更高的尺寸
          background: '#fff',
        }}
        headStyle={{
          padding: '16px 16px 0',
          borderBottom: 'none',
          minHeight: 'auto',
        }}
        bodyStyle={{
          padding: '4px 16px 16px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          flex: 1,
        }}
      />
    </div>
  );
};

export default PerformanceMetricCard;
