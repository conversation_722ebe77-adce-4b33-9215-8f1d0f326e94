import React from 'react';
import { Flex, DatePicker } from 'antd';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import styled from 'styled-components';
import FunnelChart from './FunnelChart';
import type { TaskEffectData } from '../../types/taskEffect';

const FunnelContainer = styled(Flex)`
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  width: 100%;
  height: 100%;
  flex: 1;
  min-height: 0;
`;

const SectionHeader = styled(Flex)`
  /* margin-bottom 由父容器的 gap 控制 */
`;

const SectionTitle = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
`;

const FunnelSection = styled.div`
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const { RangePicker } = DatePicker;

interface TaskCompletionFunnelProps {
  data?: TaskEffectData;
  range: [Dayjs, Dayjs];
  onRangeChange: (value: [Dayjs, Dayjs] | null) => void;
}

const TaskCompletionFunnel: React.FC<TaskCompletionFunnelProps> = ({
  data,
  range,
  onRangeChange,
}) => {
  // 门店漏斗数据
  const shopFunnelData = data?.shopFunnel
    ? [
        {
          title: '运维门店总数',
          value: data.shopFunnel.totalOemStores?.toString() || '0',
          tip: '有商户运维关系的商户下门店总数',
          color: '#0B49D9',
        },
        {
          title: '推荐门店数',
          value: data.shopFunnel.recommendedStores?.toString() || '0',
          tip: '有任务推荐的门店总数',
          color: '#1A66FF',
        },
        {
          title: '推荐完成门店数',
          value: data.shopFunnel.recommendedCompletedStores?.toString() || '0',
          tip: '有推荐任务完成的门店总数',
          color: '#4287FF',
        },
      ]
    : [];

  const shopConversionRates = data?.shopFunnel
    ? [
        {
          rate: `${data.shopFunnel.recommendedStoresRatio?.toFixed(2) || '0'}%`,
          label: '推荐门店占比',
        },
        {
          rate: `${data.shopFunnel.completedStoresRatio?.toFixed(2) || '0'}%`,
          label: '完成门店占比',
        },
      ]
    : [];

  // 推荐任务漏斗数据
  const taskFunnelData = data?.taskFunnel
    ? [
        {
          title: '推荐任务数',
          value: data.taskFunnel.recommendedTasks?.toString() || '0',
          tip: '推荐的所有任务数量',
          color: '#0B49D9',
        },
        {
          title: '应完成任务数',
          value: data.taskFunnel.tasksToBeCompleted?.toString() || '0',
          tip: '截止到今天推荐过期的任务总数',
          color: '#1A66FF',
        },
        {
          title: '任务完成数',
          value: data.taskFunnel.completedTasks?.toString() || '0',
          tip: '截止到今天已完成的任务总数',
          color: '#4287FF',
        },
        {
          title: '目标完成数',
          value: data.taskFunnel.targetCompletedTasks?.toString() || '0',
          tip: '截止到今天目标完成的任务总数',
          color: '#6BA6FF',
        },
      ]
    : [];

  const taskConversionRates = data?.taskFunnel
    ? [
        {
          rate: `${data.taskFunnel.tasksToBeCompletedRatio?.toFixed(2) || '0'}%`,
          label: '应完成任务占比',
        },
        {
          rate: `${data.taskFunnel.taskCompletionRate?.toFixed(2) || '0'}%`,
          label: '任务完成率',
        },
        {
          rate: `${data.taskFunnel.taskTargetCompletionRate?.toFixed(2) || '0'}%`,
          label: '任务目标完成率',
        },
      ]
    : [];

  return (
    <FunnelContainer vertical gap={16}>
      <SectionHeader justify="space-between" align="center">
        <SectionTitle>任务完成数据</SectionTitle>
        <RangePicker
          size="small"
          value={range}
          onChange={onRangeChange}
          format="YYYY-MM-DD"
          presets={[
            { label: '昨日', value: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')] },
            {
              label: '最近一周',
              value: [dayjs().subtract(7, 'day'), dayjs().subtract(1, 'day')],
            },
            {
              label: '最近一月',
              value: [dayjs().subtract(30, 'day'), dayjs().subtract(1, 'day')],
            },
          ]}
          disabledDate={(current) => {
            const today = dayjs();
            // PRD要求：最大值：过去180天
            return (
              current.isAfter(today.subtract(1, 'day'), 'day') ||
              current.isBefore(today.subtract(180, 'day'), 'day')
            );
          }}
        />
      </SectionHeader>

      {/* 门店漏斗 */}
      <FunnelSection>
        <FunnelChart
          title="门店漏斗"
          titleTooltip="按当前运维关系的所有门店中，在所选周期内推荐结束的门店和完成任务的门店漏斗"
          data={shopFunnelData}
          conversionRates={shopConversionRates}
        />
      </FunnelSection>

      {/* 推荐任务漏斗 */}
      <FunnelSection>
        <FunnelChart
          title="推荐任务漏斗"
          titleTooltip="按当前运维关系的所有门店中，在所选周期内主管下发、主管催办、智能推荐任务的总数和完成漏斗"
          data={taskFunnelData}
          conversionRates={taskConversionRates}
        />
      </FunnelSection>
    </FunnelContainer>
  );
};

export default TaskCompletionFunnel;
