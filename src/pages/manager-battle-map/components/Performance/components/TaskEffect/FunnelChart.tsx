import React from 'react';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';

interface FunnelDataItem {
  title: string;
  value?: string;
  tip: string | React.ReactNode;
  color: string;
}

interface ConversionRateItem {
  rate: string;
  label: string;
}

interface FunnelChartProps {
  data: FunnelDataItem[];
  conversionRates: ConversionRateItem[];
  title?: string;
  titleTooltip?: string; // 标题旁边的问号Tooltip说明
}

const FunnelContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 20px;
  border: 1px solid #f0f0f0;
`;

const Tip = styled.span`
  color: #cdcfd5;
`;

const FunnelChartTitleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 16px;
  width: 100%;
`;

const FunnelChartTitle = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
`;

const QuestionIcon = styled(QuestionCircleOutlined)`
  color: rgba(0, 0, 0, 0.45);
  cursor: help;
  font-size: 14px;
`;

const FunnelContentWrapper = styled.div`
  display: flex;
  align-items: flex-start;
  width: 100%;
`;

const FunnelLeft = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  max-width: 70%;
  width: 100%;
`;

interface FunnelItemProps {
  backgroundColor?: string;
}

const FunnelItem = styled.div<FunnelItemProps>`
  position: relative;
  padding: 7px 14px;
  width: 100%;
  box-sizing: border-box;
  min-height: 64px;
  margin-top: 1px;
  background-color: ${(props) => props.backgroundColor || '#1890FF'};
`;

const FunnelTriangle = styled.div`
  position: absolute;
  right: -1px;
  top: -1px;
  bottom: -1px;
  width: 220px;
  background: #fff;
  clip-path: polygon(100% 0, 100% 100%, 0 100%);
  pointer-events: none;
`;

const FunnelContent = styled.div`
  position: relative;
  z-index: 1;
`;

const FunnelTitle = styled.div`
  font-size: 14px;
  color: #f5f5f5;
  line-height: 22px;
  margin-bottom: 4px;
`;

const FunnelData = styled.div`
  display: flex;
  gap: 10px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #fff;
`;

const ValueUnit = styled.span`
  margin-left: 4px;
`;

const ArrowRateSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  min-width: 200px;
  padding-top: 15px;
  position: relative;
  left: -55px;
  z-index: 2;
`;

const ArrowRateItem = styled.div`
  display: flex;
  align-items: center;
  gap: 18px;
  height: 75px;
`;

const ArrowWrapper = styled.div`
  display: flex;
  align-items: center;
  flex-shrink: 0;
`;

const RateItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
  justify-content: center;
`;

const RateValue = styled.div`
  font-size: 24px;
  font-weight: 500;
  line-height: 32px;
  color: rgba(0, 0, 0, 0.85);
`;

const RateLabel = styled.div`
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.45);
`;

const Empty = styled.div`
  padding: 40px;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
`;

const FunnelChart: React.FC<FunnelChartProps> = ({
  data,
  conversionRates,
  title,
  titleTooltip,
}) => {
  if (!data || data.length === 0) {
    return <Empty>暂无数据</Empty>;
  }

  return (
    <FunnelContainer>
      {title && (
        <FunnelChartTitleWrapper>
          <FunnelChartTitle>{title}</FunnelChartTitle>
          {titleTooltip && (
            <Tooltip title={titleTooltip}>
              <QuestionIcon />
            </Tooltip>
          )}
        </FunnelChartTitleWrapper>
      )}
      <FunnelContentWrapper>
        <FunnelLeft>
          {data.map((item) => (
            <FunnelItem key={item.title} backgroundColor={item.color || '#1890FF'}>
              <FunnelContent>
                <FunnelTitle>
                  {item.title} <Tip>{item.tip}</Tip>
                </FunnelTitle>
                <FunnelData>
                  {item.value && (
                    <span>
                      {item.value}
                      <ValueUnit>个</ValueUnit>
                    </span>
                  )}
                </FunnelData>
              </FunnelContent>
            </FunnelItem>
          ))}
          <FunnelTriangle />
        </FunnelLeft>

        <ArrowRateSection>
          {conversionRates.map((rate) => (
            <ArrowRateItem key={`${rate.rate}-${rate.label}`}>
              <ArrowWrapper>
                <svg fill="none" width="54" height="76.5" viewBox="0 0 54 76.51531982421875">
                  <g>
                    <path
                      d="M0.14689094000000003,72.479347L3.328428,69.297813Q3.3987541,69.227478,3.4906397,69.189423Q3.5825253,69.151367,3.6819818,69.151367Q3.7814379,69.151367,3.8733232,69.189423Q3.9652088,69.227486,4.0355349,69.297813Q4.0703566,69.332626,4.0977160999999995,69.373573Q4.1250753,69.41452,4.1439207,69.460022Q4.1627659999999995,69.505524,4.1723735,69.553825Q4.1819811,69.602127,4.1819811,69.651367Q4.1819808,69.700615,4.1723733,69.748917Q4.1627659999999995,69.797211,4.1439204,69.842712Q4.1250748999999995,69.888206,4.0977154,69.929153Q4.0703559,69.970093,4.0355341,70.004921L1.7071081,72.333344L53,72.333344L53,1L39.708984,1L39.708984,0L54,0L54,73.333344L1.7071081,73.333344L4.0355341,75.661766Q4.0703559,75.696587,4.0977151,75.737534Q4.1250746,75.778473,4.1439202,75.823975Q4.1627657,75.869469,4.1723733,75.91777Q4.1819806,75.966064,4.1819808,76.01532Q4.1819806,76.06456,4.1723735,76.112854Q4.1627659999999995,76.161163,4.1439207,76.206657Q4.1250753,76.252151,4.0977159,76.293106Q4.0703566,76.334045,4.0355349,76.368874Q3.9652088,76.439209,3.8733234,76.477264Q3.7814379,76.515327,3.6819818,76.515327Q3.5825253,76.51532,3.4906397,76.477257Q3.3987541,76.439194,3.328428,76.368874L0.14688163999999998,73.187332L0.14644688,73.186897Q0.11570308000000001,73.156158,0.09072733,73.12056Q0.06575157999999998,73.084976,0.047293660000000015,73.045609Q0.028835710000000014,73.006241,0.017449680000000023,72.964279Q0.006063639999999981,72.922318,0.002091349999999992,72.879021Q-0.0018810000000000215,72.835724,0.0016796000000000033,72.792397Q0.005240199999999973,72.749069,0.01622686000000001,72.707001Q0.027213509999999996,72.664932,0.045296369999999975,72.625389Q0.06337928999999998,72.585846,0.08801562000000002,72.550026Q0.11400926,72.51223,0.14644688,72.47979L0.14689094000000003,72.479347Z"
                      fillRule="evenodd"
                      fill="#000000"
                      fillOpacity="0.25"
                    />
                  </g>
                </svg>
              </ArrowWrapper>
              <RateItem>
                <RateValue>{rate.rate}</RateValue>
                <RateLabel>{rate.label}</RateLabel>
              </RateItem>
            </ArrowRateItem>
          ))}
        </ArrowRateSection>
      </FunnelContentWrapper>
    </FunnelContainer>
  );
};

export default FunnelChart;
