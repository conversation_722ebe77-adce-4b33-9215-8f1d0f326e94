# 【prd】1219-任务效果数据看板需求

# 一、背景

端到端任务2.0从11.17全量后，凭证量及续充率有明显提升，各个团队也通过PK赛、管理手段等各种路径，推动任务的完成，但目前管理层、小二无法自行查看效果的对比数据，全部的复盘数据都由人工线下输出，不利于快速识别问题，需要建设任务效果数据看板，提升快速识别数据问题的能力，也提升管理层、小二、商家对任务的正向感知。

[《【任务体系】任务效果数据看板（1219）》](https://alidocs.dingtalk.com/i/nodes/Gl6Pm2Db8DMGQKRatjkOqobGWxLq0Ee4)

预估收益：

1.  任务完成率提升5pp
    

过程关注指标：

1.  看板使用率60%（近30天登录过任务效果看板的人数/近30天有任务推荐的人数）含渠道和直营
    

# 二、方案

**任务范围：**

门店装修达标、货架有品、商品分达标、商家分等级V2、商家分等级V3、商家分等级V4、商家分等级V5、五星货架、五星保鲜任务（未上线）、BML任务价值测算优化

## 1、代运营数据看板

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/qN99NKOrUGeJ9Kv2/265320fa9dd7430b855c0baff13c9e8b1861.png)

| 字段 | 表 | 字段 |
| --- | --- | --- |
| 凭证量 | gd\_info\_cdm.dws\_gd\_info\_sale\_shop\_operate\_index\_nd | voucher\_cnt\_1d |
| 曝光 | gd\_info\_cdm.dws\_gd\_info\_poi\_ut\_action\_di | dpv\_poi\_pv |
| CTCVR | 计算值 | 凭证/曝光 |

### 1.1门店明细看板增加字段

数据范围：所有的门店；日更新；

筛选条件：

1.  【新增】任务完成日期选择框：日期范围筛选框~~日期筛选框，可选择多个日期，筛选字段【最后一次推荐任务完成日期】，最早只能选7天前~~
    
2.  【新增】门店是否被推荐过：筛选推荐任务数量大于1
    
3.  【已有】部门筛选：
    
4.  【已有】类目筛选：
    
5.  【已有】运维小二姓名：
    
6.  【新增】门店分层：非必填，可多选，筛选字段【当前门店分层】，新手期 瓶颈期 成长期 成熟期
    
7.  【已有】商户ID：
    
8.  【新增】门店名称：非必填，模糊搜索，筛选字段【门店名称】
    
9.  【已有】门店ID：
    
10.  【已有】行业：
    

| 商户ID | 门店ID | 门店名称 | 行业 | 类目 | 运维小二 | 部门 | 是否有推荐任务 | 推荐任务数量 | 推荐任务完成数量 | 推荐任务完成率 | **最近一次完成推荐任务的日期** | 完成任务时门店分层 | 当前门店分层 | 店日均凭证量 |  |  | 店日均曝光量PV |  |  | 店日均CTCVR |  |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  | 完成前 | 完成后 | 波动 | 完成前 | 完成后 | 波动 | 完成前 | 完成后 | 波动 |
| PID | X1 |  |  | 1-3 | 户运维关系 | 1-4级部门 | 是/否 | 当前门店，【推荐次数>=1且状态！=已失效】的任务数量 | 当前门店，【推荐次数>=1且状态=已完成】的任务数量 | 推荐任务完成数量/推荐任务数量\*100% | 最后一次完成推荐任务的日期 | 新手-瓶颈-成长-成熟<br>任务完成日当天所处生命周期<br>最新 | 当前所处的门店分层 | T为最后一次推荐任务完成日期<br>T-7至T-1的日均凭证量 | T为最后一次推荐任务完成日期<br>T至T+6的日均凭证量 | T为最后一次推荐任务完成日期<br>（完成后-完成前）/完成前\*100% | T为最后一次推荐任务完成日期<br>T-7至T-1的日均曝光量 | T为最后一次推荐任务完成日期<br>T至T+6的日均曝光量 | T为最后一次推荐任务完成日期<br>（完成后-完成前）/完成前\*100% | T为最后一次推荐任务完成日期<br>T-7至T-1的总凭证量/T-7至T-1的总曝光量 | T为最后一次推荐任务完成日期<br>T至T+6的总凭证量/T至T+6的总曝光量 | T为最后一次推荐任务完成日期<br>（完成后-完成前）/完成前\*100% |
| 商户Id | 门店Id | 门店名称 | 行业 | 1-4级tag | 运维小二名 | 二-五级 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 |

### 1.2 商户明细看板增加字段

数据范围：所有的商户；日更新；

筛选条件：

1.  【新增】任务完成日期选择框：日期范围筛选框~~日期筛选框，可选择多个日期，筛选字段【最后一次推荐任务完成日期】，最早只能选7天前~~
    
2.  【新增】门店是否被推荐过：筛选推荐任务数量大于1
    
3.  【已有】部门筛选：
    
4.  【已有】类目筛选：
    
5.  【已有】运维小二姓名：
    
6.  【新增】门店分层：非必填，可多选，筛选字段【当前门店分层】，新手期 瓶颈期 成长期 成熟期
    
7.  【已有】商户ID：
    
8.  【已有】行业：
    

| 商户ID | 商户名称 | 主店名称 | 行业 | 类目 | 运维小二 | 部门 | 门店数 | 有推荐任务的门店数 | 有推荐任务完成的门店数 | 有推荐任务完成的门店占比 | 推荐任务数量 | 推荐任务完成数量 | 推荐任务完成率 | **最近一次完成推荐任务的日期** | 门店分层 | 店均日均凭证量 |  |  | 店均日均曝光量PV |  |  | 店均日均CTCVR |  |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |  | 完成前 | 完成后 | 波动 | 完成前 | 完成后 | 波动 | 完成前 | 完成后 | 波动 |
| PID |  |  |  | 1-3<br>主店 | 户运维关系<br>主店 | 1-4级部门 | 户下门店总数<br>这个pid下有运维人店关系的 | 有推荐任务的门店总数 | 有推荐任务完成的门店总数 | 有推荐任务完成的门店占比 | 户下所有门店的推荐任务总数 | 户下所有门店的推荐任务完成总数 | 推荐任务完成数量/推荐任务数量\*100% | 户下门店的最大值 | 户下门店的最大生命周期 | 门店值求平均 | 门店值求平均 | （完成后-完成前）/完成前\*100% | 门店值求平均 | 门店值求平均 | （完成后-完成前）/完成前\*100% | 门店值求平均 | 门店值求平均 | （完成后-完成前）/完成前\*100% |
| 商户ID | 商户名称 | 主店名称 | 商户行业 | 一-四级atag | 运维小二姓名 | 二-五级部门 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 | 新增 |

### 1.3 小二任务效果看板\--需新增菜单

数据范围：所有的小二；运维关系中，有完成过推荐任务的所有门店数据

筛选条件：

1.  部门筛选：与现有保持一致
    
2.  运维小二姓名：非必填，模糊搜索，可多选，筛选字段【运维小二】
    

| 运维小二 | 部门 | 门店数 | 有推荐任务的门店数 | 有推荐任务完成的门店数 | 有推荐任务完成的门店占比 | 推荐任务数量 | 推荐任务完成数量 | 推荐任务完成率 | 店均日均凭证量 |  |  | 店均日均曝光量PV |  |  | 店均日均CTCVR |  |  | 大盘波动 |  |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
|  |  |  |  |  |  |  |  |  | 完成前 | 完成后 | 波动 | 完成前 | 完成后 | 波动 | 完成前 | 完成后 | 波动 | 日均凭证量波动 | 日均曝光量波动 | 日均CTCVR波动 |
| 户运维关系 | 1-4级部门 | 门店总数 | 有推荐任务的门店中总数 | 有推荐任务完成的门店总数 | 有推荐任务完成的门店占比 | 户下所有门店的推荐任务总数 | 户下所有门店的推荐任务完成总数 | 推荐任务完成数量/推荐任务数量\*100% | 门店值求平均 | 门店值求平均 | （完成后-完成前）/完成前\*100% | 门店值求平均 | 门店值求平均 | （完成后-完成前）/完成前\*100% | 门店值求平均 | 门店值求平均 | （完成后-完成前）/完成前\*100% | 未完成任务门店近30天（T-1至T-30）比前30天（T-31至T-60） |  |  |

### 1.4 权限管理

同现有权限

| **角色** | **数据范围** | **看数场景** | **权限申请** | **场景说明** $\color{#0089FF}{@梁辰(以忱)(以忱)}$ |
| --- | --- | --- | --- | --- |
| 直营/服务商一线小二 | 小二名下有人户/人店关系的门店或商户/小二自己<br>人户/人店关系指：新签 或 运维关系 | 小二自己看自己名下的明细数据 | 页面权限：默认展示<br>数据权限：不用申请 | 当小二名下的某个商家完成任务后，小二可以查看该商家完成任务前后x天的效果，若正向可同步商家；同时在推动同类型其他商家做任务时，也可参考历史效果进行任务推动j |
| 直营主管<br>服务商主账号 | 主管看下级小二，小二对应有人户/人店关系的门店或商户（不限制几级架构） | 对应小二的明细数据 | 页面权限：默认展示<br>数据权限：不用申请 | 主管定期查看所管理小二（及其门店）的达成效果数据，进行绩效管理的复盘与跟踪 |
| 横向运营 | 不区分行业<br>不区分直营/渠道<br>不区分商户通/sme/点亮 | 横向运营策略等人员查看 | 页面权限：需申请<br>数据权限：审批到一级主管（申请人的+1） | 阶段性汇报；周报复盘；<br>专题分析 |

## 2、小二工作台--汇总展示

小二有运维关系的未完成任务门店近30天（T-1至T-30）比前30天（T-31至T-60）

行业数据（atag\_level1）：行业下所有门店，近30天（T-1至T-30）比前30天（T-31至T-60）;仅展示当前小二名下门店的atag

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mPdnpEbd8xwrgqw9/img/9e7099f5-5cfa-41e7-8ba0-32f2dd13fad7.png)

## 3、管理者作战地图

按组织关系进行汇总

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mPdnpEbd8xwrgqw9/img/49201ba9-6c9a-49e9-86fc-fb4adc974af2.png)

# 三、详细需求

**1、代运营数据看板**

| 模块 | 需求 |
| --- | --- |
| 代运营数据看板-商户明细看板 | 1.  增加筛选条件：见方案部分<br>    <br>2.  增加字段：见方案部分 |
| 代运营数据看板-门店明细看板 | 1.  增加筛选条件：见方案部分<br>    <br>2.  增加字段：见方案部分 |
| 代运营数据看板-小二任务效果看板 | 1.  新增菜单，无需菜单权限，仅按组织结构进行数据权限控制<br>    <br>2.  增加筛选条件：见方案部分<br>    <br>3.  增加字段：见方案部分 |

## 2、小二工作台

| 模块 | 需求 |
| --- | --- |
| 代运营工作台-任务管理 | 1.  任务管理-绩效目标卡片，增加新的tab，【任务效果】<br>    <br>2.  任务完成数据：**期望是实时数据小时级**<br>    <br>    1.  日期筛选框：<br>        <br>        1.  筛选内容：自然日<br>            <br>        2.  默认值：当天到过去30天<br>            <br>        3.  最大值：过去180天<br>            <br>    2.  门店漏斗<br>        <br>        1.  问号：按当前运维关系的所有门店中，在所选周期内推荐结束的门店和完成任务的门店漏斗<br>            <br>        2.  运维门店总数：有商户运维关系的商户下门店总数<br>            <br>        3.  推荐门店数：在所选周期内有任务推荐（推荐开始或结束时间在周期内）的门店总数（仅门店维度任务）<br>            <br>        4.  推荐完成门店数：在所选周期内有推荐任务完成的门店总数（仅门店维度任务）<br>            <br>        5.  百分比：下层占上层数据的百分比<br>            <br>    3.  推荐任务漏斗<br>        <br>        1.  问号：按当前运维关系的所有门店中，在所选周期内主管下发、主管催办、智能推荐任务的总数和完成漏斗<br>            <br>        2.  推荐任务数：周期内推荐开始或结束时间在周期内任务总数<br>            <br>        3.  应完成任务数：推荐结束时间在周期内任务总数<br>            <br>        4.  任务完成数：被推荐且任务完成时间在周期内的任务数<br>            <br>        5.  目标完成数：被推荐且任务目标完成时间在周期内的任务数<br>            <br>        6.  百分比：下层占上层数据的百分比<br>            <br>3.  完成效果对比<br>    <br>    1.  问号：任务完成前后数据指完成前后7天的数据，大盘增长数据指无任务完成门店的近两月数据对比，行业增长率指全行业门店两月数据对比<br>        <br>    2.  更多商户数据：点击后跳转代运营数据看板-商户明细看板<br>        <br>        1.  筛选展示字段预填：跳转到对应页面后，按【 商户明细看板增加字段】默认预填字段<br>            <br>    3.  更多门店数据：点击后跳转代运营数据看板-门店明细看板<br>        <br>        1.  筛选展示字段预填：跳转到对应页面后，按【 门店明细看板增加字段】默认预填字段<br>            <br>    4.  店均日均凭证量：<br>        <br>        1.  问号：店均日均凭证量，顾客在高德产生凭证量，包括（在投-美食/休娱：有效凭证到店、计费客资、ocpc客资；在投-医疗/美业/教培：计费客资、OCPC客资、cpc客资、订单量；纯年费：订单量、订座量）<br>            <br>        2.  任务完成前：当前运维关系的商户下，【最近一次完成推荐任务的日期<T-7】的有推荐任务完成门店的任务完成前日均凭证量均值<br>            <br>        3.  任务完成后：当前运维关系的商户下，【最近一次完成推荐任务的日期<T-7】的有推荐任务完成门店的任务完成后日均凭证量均值<br>            <br>        4.  完成后增长：(任务完成后-任务完成前)/任务完成前\*100%，保留2位小数<br>            <br>        5.  大盘增长率：当前运维关系的商户下，无推荐任务完成的门店，（近30天-前30天）/前30天\*100%，保留2位小数<br>            <br>        6.  行业增长率：按atag1，全行业的门店，（近30天-前30天）/前30天\*100%，保留2位小数<br>            <br>        7.  行业筛选框：小二明下运维关系商户的所有atag1，默认选中一个（名称排序），统计范围如下<br>            <br>            1.  购物（atag1：621396）<br>                <br>            2.  教育培训（atag1：621620）<br>                <br>            3.  丽人（atag1：621764）<br>                <br>            4.  生活服务（atag1：621918）<br>                <br>            5.  休闲娱乐（atag1：622081）<br>                <br>            6.  医疗（atag1：622142）<br>                <br>            7.  美食（atag1：621122）<br>                <br>            8.  金融保险（atag1：621758）<br>                <br>            9.  运动健身（atag1：622290）<br>                <br>        8.  提示：行业数据禁止以任务形式同步至外部人员<br>            <br>    5.  店均日均曝光PV<br>        <br>        1.  问号：店均日均门店详情页的曝光量<br>            <br>        2.  任务完成前：逻辑同上<br>            <br>        3.  任务完成后：逻辑同上<br>            <br>        4.  完成后增长：逻辑同上<br>            <br>        5.  大盘增长率：逻辑同上<br>            <br>        6.  行业增长率：逻辑同上<br>            <br>        7.  行业筛选框：逻辑同上<br>            <br>        8.  提示：逻辑同上<br>            <br>    6.  店均日均CTCVR<br>        <br>        1.  问号：店均日均凭证量/店均日均曝光PV<br>            <br>        2.  任务完成前：逻辑同上<br>            <br>        3.  任务完成后：逻辑同上<br>            <br>        4.  完成后增长：逻辑同上<br>            <br>        5.  大盘增长率：逻辑同上<br>            <br>        6.  行业增长率：逻辑同上<br>            <br>        7.  行业筛选框：逻辑同上<br>            <br>        8.  提示：逻辑同上<br>            <br>4.  如果所有数据都为空，则不显示该tab |

## 3、管理者作战地图

| 模块 | 需求 |
| --- | --- |
| 代运营工作台-管理者作战地图 | 1.  管理者作战地图-绩效目标卡片，增加新的tab，【任务效果】，排在绩效目标、业务过程数据之后<br>    <br>2.  数据汇总方式：<br>    <br>    1.  直营：小二-主管-经理-…<br>        <br>    2.  渠道：渠道小二-服务商-渠道运维-区域经理-…<br>        <br>3.  任务完成数据：数据逻辑同小二工作台<br>    <br>4.  完成效果对比<br>    <br>    1.  问号：同小二工作台<br>        <br>    2.  更多小二数据：点击后跳转代运营数据看板-小二任务效果看板<br>        <br>    3.  更多商户数据：同小二工作台<br>        <br>    4.  更多门店数据：同小二工作台<br>        <br>    5.  完成效果对比：按小二维度进行平均汇总，其余逻辑同小二工作台 |

# 四、资源和排期