# 任务效果相关接口文档

## 接口概览

本文档描述了作战地图（FireMaps）中任务效果相关的三个接口，用于查询任务效果数据、行业增长率数据和行业类型列表。

---

## 1. 查询任务效果数据

### 接口信息

*   **接口名称**: `queryTaskEffect`
    
*   **接口路径**: `/fireMaps/queryTaskEffect`  
    
*   **HSF：**com.amap.sales.operation.client.FireMapsFacade#queryTaskEffect
    
*   **请求方式**: POST
    
*   **接口描述**: 查询任务效果数据，包括门店漏斗、任务漏斗、效果对比等数据
    

### 请求参数

**请求对象**: `OptTaskEffectRequest`

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
| --- | --- | --- | --- | --- |
| entityType | String | 是 | 实体类型，枚举值：STAFF（小二）、JOB（组织树）、COM（渠道商）、CHANNEL\_OPT（渠道小二） | "STAFF" |
| staffIds | List | 否 | 小二ID列表（entityType为STAFF时必填） | \["123456"\] |
| jobLevel | Integer | 否 | 组织树层级（entityType为JOB时必填） | 1 |
| jobIds | List | 否 | 组织树ID列表（entityType为JOB时必填） | \["*********"\] |
| channelCompanyIds | List | 否 | 渠道商ID列表（entityType为COM时必填） | \["company001"\] |
| channelOptStaffIds | List | 否 | 渠道小二ID列表（entityType为CHANNEL\_OPT时必填） | \["staff001"\] |
| commonOperatorInfo | Object | 是 | 操作人信息，包含operatorId | \- |

**请求示例**:

```json
{
  "entityType": "STAFF",
  "staffIds": ["123456"],
  "commonOperatorInfo": {
    "operatorId": "123456"
  }
}
```

### 响应参数

**响应对象**: `ResultDTO<OptTaskEffectDTO>`

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| success | Boolean | 是否成功 |
| code | String | 响应码 |
| message | String | 响应消息 |
| model | OptTaskEffectDTO | 任务效果数据 |

**OptTaskEffectDTO 结构**:

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| shopFunnel | ShopFunnelDTO | 门店漏斗数据 |
| taskFunnel | TaskFunnelDTO | 任务漏斗数据 |
| voucherQuantityPerStore | TaskEffectComparisonDTO | 店均日均凭证量效果对比 |
| exposurePvPerStore | TaskEffectComparisonDTO | 店均日均曝光PV效果对比 |
| ctcvrPerStore | TaskEffectComparisonDTO | 店均日均CTCVR效果对比 |

**ShopFunnelDTO 结构**:

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| totalOemStores | Long | 运维门店总数 |
| recommendedStores | Long | 推荐门店数 |
| recommendedCompletedStores | Long | 推荐完成门店数 |
| recommendedStoresRatio | BigDecimal | 推荐门店占比 |
| completedStoresRatio | BigDecimal | 完成门店占比 |

**TaskFunnelDTO 结构**:

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| recommendedTasks | Long | 推荐任务数 |
| tasksToBeCompleted | Long | 应完成任务数 |
| completedTasks | Long | 任务完成数 |
| targetCompletedTasks | Long | 目标完成数 |
| tasksToBeCompletedRatio | BigDecimal | 应完成任务占比 |
| taskCompletionRate | BigDecimal | 任务完成率 |
| taskTargetCompletionRate | BigDecimal | 任务目标完成率 |

**TaskEffectComparisonDTO 结构**:

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| beforeValue | BigDecimal | 完成前指标值 |
| afterValue | BigDecimal | 完成后指标值 |
| growthRate | BigDecimal | 增长率 |
| marketGrowthRate | BigDecimal | 大盘增长率 |

**响应示例**:

```json
{
  "success": true,
  "code": "SUCCESS",
  "message": "查询成功",
  "model": {
    "shopFunnel": {
      "totalOemStores": 12145,
      "recommendedStores": 2145,
      "recommendedCompletedStores": 214,
      "recommendedStoresRatio": 17.67,
      "completedStoresRatio": 1.76
    },
    "taskFunnel": {
      "recommendedTasks": 12145,
      "tasksToBeCompleted": 2145,
      "completedTasks": 214,
      "targetCompletedTasks": 214,
      "tasksToBeCompletedRatio": 17.67,
      "taskCompletionRate": 1.76,
      "taskTargetCompletionRate": 1.76
    },
    "voucherQuantityPerStore": {
      "beforeValue": 1.3,
      "afterValue": 1.5,
      "growthRate": 15.38,
      "marketGrowthRate": 13.0
    }
  }
}
```

### 业务说明

1.  **数据来源**: 根据开关 `AgentStatisticsSwitch.taskEffectMockDataEnabled` 决定使用mock数据还是真实数据
    
2.  **查询逻辑**:
    
    *   门店漏斗数据：查询场景 `TASK_PERFORMANCE_DATA_STORE_FUNNEL`
        
    *   任务漏斗数据：查询场景 `TASK_PERFORMANCE_DATA_FUNNEL`
        
    *   效果对比数据：查询场景 `TASK_PERFORMANCE_STORE_DATA_COMPLETE_COMPARISON`（每个指标类型单独查询）
        
3.  **参数要求**: 根据 `entityType` 的不同，需要传入对应的ID参数
    

---

## 2. 查询行业增长率数据

### 接口信息

*   **接口名称**: `queryIndustryGrowthRate`
    
*   **接口路径**: `/fireMaps/queryIndustryGrowthRate`
    
*   **HSF：**com.amap.sales.operation.client.FireMapsFacade#queryIndustryGrowthRate
    
*   **请求方式**: POST
    
*   **接口描述**: 查询各指标的行业增长率数据
    

### 请求参数

**请求对象**: `OptTaskEffectRequest`

参数同接口1（查询任务效果数据）

### 响应参数

**响应对象**: `ResultDTO<IndustryGrowthRateDTO>`

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| success | Boolean | 是否成功 |
| code | String | 响应码 |
| message | String | 响应消息 |
| model | IndustryGrowthRateDTO | 行业增长率数据 |

**IndustryGrowthRateDTO 结构**:

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| voucherQuantityIndustryGrowthRate | BigDecimal | 店均日均凭证量行业增长率 |
| exposurePvIndustryGrowthRate | BigDecimal | 店均日均曝光PV行业增长率 |
| ctcvrIndustryGrowthRate | BigDecimal | 店均日均CTCVR行业增长率 |

**响应示例**:

```json
{
  "success": true,
  "code": "SUCCESS",
  "message": "查询成功",
  "model": {
    "voucherQuantityIndustryGrowthRate": 13.0,
    "exposurePvIndustryGrowthRate": 15.5,
    "ctcvrIndustryGrowthRate": 12.3
  }
}
```

### 业务说明

1.  **数据来源**: 根据开关 `AgentStatisticsSwitch.taskEffectMockDataEnabled` 决定使用mock数据还是真实数据
    
2.  **查询逻辑**: 查询场景 `TASK_EFFECT_INDUSTRY_GROWTH_RATE`
    
3.  **数据说明**: 返回三个核心指标的行业增长率数据
    

---

## 3. 查询可查询的行业类型列表

### 接口信息

*   **接口名称**: `queryIndustryTypes`
    
*   **接口路径**: `/fireMaps/queryIndustryTypes`
    
*   **HSF:** com.amap.sales.operation.client.FireMapsFacade#queryIndustryTypes
    
*   **请求方式**: POST
    
*   **接口描述**: 查询可查询的行业类型列表
    

### 请求参数

**请求对象**: `OptTaskEffectRequest`

参数同接口1（查询任务效果数据）

### 响应参数

**响应对象**: `ResultDTO<IndustryTypeListDTO>`

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| success | Boolean | 是否成功 |
| code | String | 响应码 |
| message | String | 响应消息 |
| model | IndustryTypeListDTO | 行业类型列表数据 |

**IndustryTypeListDTO 结构**:

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| industryTypes | List | 行业类型列表 |

**响应示例**:

```json
{
  "success": true,
  "code": "SUCCESS",
  "message": "查询成功",
  "model": {
    "industryTypes": [
      "美食",
      "酒店",
      "景点",
      "购物",
      "娱乐",
      "生活服务",
      "交通出行",
      "其他"
    ]
  }
}
```

### 业务说明

1.  **数据来源**: 根据开关 `AgentStatisticsSwitch.taskEffectMockDataEnabled` 决定使用mock数据还是真实数据
    
2.  **查询逻辑**: 查询场景 `TASK_EFFECT_INDUSTRY_TYPES`
    
3.  **数据说明**: 返回所有可查询的行业类型列表，用于下拉选择等场景
    

---

## 通用说明

### 错误码说明

| 错误码 | 说明 |
| --- | --- |
| SUCCESS | 成功 |
| PHASED\_DIAGNOSTICS\_SCENE\_NOT\_FOUND | 未找到场景对应的应用代码和规则代码 |

### 注意事项

1.  **开关控制**: 所有接口都受 `AgentStatisticsSwitch.taskEffectMockDataEnabled` 开关控制，默认为 `true`（使用mock数据）
    
2.  **参数校验**: 根据 `entityType` 的不同，必须传入对应的ID参数
    
3.  **数据降级**: 当真实数据查询失败时，会自动降级使用mock数据
    
4.  **日期格式**: 查询日期默认为前一天，格式为 `YYYYMMDD`
    

### 实体类型说明

| entityType | 说明 | 必填参数 |
| --- | --- | --- |
| STAFF | 小二 | staffIds |
| JOB | 组织树 | jobLevel, jobIds |
| COM | 渠道商 | channelCompanyIds |
| CHANNEL\_OPT | 渠道小二 | channelOptStaffIds |

---

## 更新记录

*   2025-01-XX: 初始版本，包含三个接口的完整文档