# 【20251217】小二/主管-任务效果看板

_**原语雀文档链接：**_[https://yuque.antfin.com/william.gc/ieofpu/lytesc5owwnt8vve](https://yuque.antfin.com/william.gc/ieofpu/lytesc5owwnt8vve)

---

## 1. 需求背景

*   PRD：[《【prd】1219-任务效果数据看板需求》](https://alidocs.dingtalk.com/i/nodes/14dA3GK8gwAG5B1mHkOOkNKg89ekBD76)
    
*   aone：[aone地址](https://project.aone.alibaba-inc.com/v2/project/2099406/req/76363733?_dt_ac=zZP5j4FqSw63vJGFXjEPgt7UXo383VKAuiAQAeGQEhGg%2BqQKYOnxNmlJA6MihtICvH%2BFHtBGXbVpSAZ8&_dt_sig=KvZzsUuwJZmBm%2FqQHfJh1NKBpmeWYVVgfrG%2FBmMzTfM%3D&_dt_ts=1766042738)
    

## 2. 业务流程

【如有业务流程有一定复杂度，可以画下，让大家了解业务流程】

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/YVDY8BqBc1nomNAP/f733de43249c4bf2a193467abe3329811861.png)

## 4. 各需求改动点

### 4.1 数据加工

#### 1、数仓前置加工

任务明细表：[https://dw.alibaba-inc.com/dmc/dlf-table/03385451da88e0744ae574222a6c67f455848c23/](https://dw.alibaba-inc.com/dmc/dlf-table/03385451da88e0744ae574222a6c67f455848c23/)

小二任务效果表： [https://dw.alibaba-inc.com/dmc/dlf-table/335e717a975159ef40ce8f6b5b58ce56afca2f7e/](https://dw.alibaba-inc.com/dmc/dlf-table/335e717a975159ef40ce8f6b5b58ce56afca2f7e/)

门店任务效果表：[https://dw.alibaba-inc.com/dmc/dlf-table/79c507ac3029599884427a2893d1fcbf33673fd5/](https://dw.alibaba-inc.com/dmc/dlf-table/79c507ac3029599884427a2893d1fcbf33673fd5/)

#### 2、取数效果（筛选时间昨天）

**人户关系的确认（回流数据）**

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mPdnpEbd8xwrgqw9/img/9e7099f5-5cfa-41e7-8ba0-32f2dd13fad7.png)

#### 3、规则配置

[swtich配置rule](http://switch.pre.alibaba-inc.com/#/switchList?appName=amap-sales-operation&envGroup=preInnerGroup&unit=pre&name=phasedDiagnosticsAppCodeMapPair)

[https://pre-xy.amap.com/sale-pc/sales-data-build/claimManagement?spm=amap.kbservcenter.XyMenu.DATAWORKBENCH-MANAGEMENT](https://pre-xy.amap.com/sale-pc/sales-data-build/claimManagement?spm=amap.kbservcenter.XyMenu.DATAWORKBENCH-MANAGEMENT)

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4maOgXbaeeEgvlWN/img/926c9bdd-1bd2-4637-8e42-0feb5f316710.png)

#### 4、任务配置

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4maOgXbaeeEgvlWN/img/812f284c-540c-4257-a410-fba0925af25e.png)

#### 5、详细取数维度

###### 小二维度

*   STAFF - 小二
    

###### 作战者地图

*   JOB-岗位（选择l1\_dept\_name ~ l7\_dept\_name聚合 ）
    
*   COM-服务商公司（选择company\_id聚合）
    
*   MANAGER\_EMPLOYEE\_ID（选择manager\_employee\_id聚合）
    

##### 门店漏斗（小二维度&作战者地图为一个SQL）

1、运维门店总数 - kbdw.dim\_kb\_sale\_employee\_partner\_relation(广告运维)

2、推荐门店数 - 查询 task\_ds 获取 recommend\_shop\_cnt 推荐店铺数

3、推荐完成门店数 - 查询 task\_ds 推荐 recommend\_task\_shop\_cnt 店铺任务完成数

4、推荐门店占比 = 推荐/总数

5、完成门店占比 = 完成/推荐数

##### 任务漏斗（小二维度&作战者地图为一个SQL）

1、推荐任务数 - 查询 task\_ds 的 recommend\_task\_cnt

2、应完成任务数 - 查询 task\_ds 的 关联**recommend\_start\_time 或 recommend\_end\_time** 在时间范围内的 推荐任务数 recommend\_task\_wait\_finish\_cnt

3、任务完成数 - 查询 recommend\_end\_time 在时间范围内的 且 status = 'FINISH'的推荐任务数 的 recommend\_task\_finish\_cnt

4、目标完成数 - 取target\_status=COMPLETED的任务完成数

5、应完成任务占比 = 应完成/总数 

6、任务完成率 = 完成/应完成

7、目标完成数 = 目标完成/完成

##### 完成效果对比-门店凭证量 + PV +  CTCVR（小二维度&作战者地图为一个SQL）

1、任务完成前 - 当前运维关系的商户下，【最近一次完成推荐任务的日期<T-7】的有推荐任务完成门店的任务完成前日均凭证量均值、PV和CTCVR

2、任务完成后 - 当前运维关系的商户下，【最近一次完成推荐任务的日期<T-7】的有推荐任务完成门店的任务完成后日均凭证量均值、PV和CTCVR

3、完成后增长 - (任务完成后-任务完成前)/任务完成前\*100%，保留2位小数

###### 完成效果对比 - 大盘凭证量 + PV + CTCVR（小二维度&作战者地图为一个SQL）

1、大盘增长率：当前运维关系的商户下，无推荐任务完成的门店，（近30天-前30天）/前30天\*100%，保留2位小数

###### 完成效果对比 - 行业凭证量 + PV + CTCVR（小二维度&作战者地图为一个SQL）

1、行业增长率：按atag1，全行业的门店，（近30天-前30天）/前30天\*100%，保留2位小数

### 4.1 接口定义

局部缓存

[《任务效果相关接口文档》](https://alidocs.dingtalk.com/i/nodes/LeBq413JA24Yz5qoIRklbe4z8DOnGvpb)

##### 小二维度入参

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4maOgXbaeeEgvlWN/img/c5b3c8a9-e94b-4ca1-839c-4604c1d21602.png)

```json
{
  "requests": [
    {
      "action": "amap-sales-operation.FireMapsFacade.queryPerformanceTargets",
      "param": {
        "pageSource": "AGENT_PAGE",
        "queryDate": "20260103",
        "entityType": "STAFF",
        "requestId": "52baa846-2c64-c2d8-a365-971d1c000897"
      },
      "requestType": "msePc",
      "extraHeader": {
        "requestid": "66fdcd7e-236d-4478-97fe-a9ae0836f868",
        "requestsource": 7
      },
      "skipCompatGwApi": true
    }
  ]
}
```

##### 作战者地图入参-选择组织树

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4maOgXbaeeEgvlWN/img/b8c7d5a1-3b42-4323-9c03-4f7b39cbe94b.png)

```json
{
  "requests": [
    {
      "action": "amap-sales-operation.FireMapsFacade.queryPerformanceDataBar",
      "param": {
        "pageSource": "MANAGER_PAGE",
        "selectedIndicator": "TOTAL_INCOME",
        "queryDate": "20260103",
        "jobIds": [
          "560741003"
        ],
        "entityType": "JOB",
        "jobLevel": 5,
        "leaf": true,
        "page": {
          "pageNo": 1,
          "pageSize": 40
        },
        "requestId": "0de61d8b-27fd-04a4-b21a-decf64c98502"
      },
      "requestType": "msePc",
      "extraHeader": {
        "requestid": "c02ea971-bcb9-47bb-96df-5b79c9429c9c",
        "requestsource": 7
      },
      "skipCompatGwApi": true
    }
  ]
}
```

##### 作战者地图入参-选择渠道运维

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4maOgXbaeeEgvlWN/img/74684399-5784-4845-87bd-c5c993964673.png)

```json
{
  "requests": [
    {
      "action": "amap-sales-operation.FireMapsFacade.queryPerformanceTargets",
      "param": {
        "pageSource": "MANAGER_PAGE",
        "queryDate": "20260103",
        -- 运维小二 
        "channelOptStaffIds": [
          "3022000000042129"
        ],
        "entityType": "CHANNEL_OPT",
        "requestId": "2bd30b2e-0c1e-dd87-f985-06332ef72ce7"
      },
      "requestType": "msePc",
      "extraHeader": {
        "requestid": "f5d7d1eb-bc85-42f2-9439-bb451392d67a",
        "requestsource": 7
      },
      "skipCompatGwApi": true
    }
  ]
}
```

##### 作战者地图入参-选择渠道服务商

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/4maOgXbaeeEgvlWN/img/e850625d-1b26-4792-8800-be29af07a57e.png)

```json
{
  "requests": [
    {
      "action": "amap-sales-operation.FireMapsFacade.queryPerformanceDataBar",
      "param": {
        "pageSource": "MANAGER_PAGE",
        "selectedIndicator": "TOTAL_INCOME",
        "queryDate": "20260103",
        "channelCompanyIds": [
          "2068800860883055"
        ],
        "entityType": "COM",
        "page": {
          "pageNo": 1,
          "pageSize": 40
        },
        "requestId": "39005ca0-feb4-3983-8a99-1bc229d08aae"
      },
      "requestType": "msePc",
      "extraHeader": {
        "requestid": "a39cc8bb-7769-4690-8ea8-b21d8dfc35c1",
        "requestsource": 7
      },
      "skipCompatGwApi": true
    }
  ]
}
```

## 5. 影响面分析

### 5.1 已有代码逻辑的影响

### 5.2 已有核对脚本的影响

### 5.3 已有监控的影响

### 5.4 已有数据的影响

【数据兼容性】

### 5.5 已有jar的影响

【jar包和接口兼容性】

## 6. 预案评估

【也即本次是否有新增相关的Switch开关等等】

**\-- 上游数据未产出，则不展示**

## 7. 核对评估

【新增的核对】

## 8. 监控评估

【新增的关键监控】

## 9. 压测评估

【是否链路有新增系统，需要新增压测】

## 10. 灰度评估

## 11. 应急评估

## 12. 重点关注点

【开发、测试一同关注】

## 13. 外围系统容量评估

【eg:新活动业务，对外围系统的容量评估】

## 14. 发布依赖评估

【发布是否涉及域内外系统的发布依赖】

## 15. 测试回归建议

## 16. 发布回滚评估

## 17.人日&排期

### 17.1 人日

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/mPdnpEbd8xwrgqw9/img/9e7099f5-5cfa-41e7-8ba0-32f2dd13fad7.png)

| **工作内容** | **人员** | **人日** |
| --- | --- | --- |
| 门店漏斗【时间筛选】<br>*   任务底表 JOIN 运维关系表 JOIN 组织表，shopId维度取数<br>    <br>*   小二视角、管理者视角 |  | 2 |
| 推荐任务漏斗【时间筛选】<br>*   任务底表 JOIN 运维关系表 JOIN 组织表，任务维度取数<br>    <br>*   小二视角、管理者视角 |  | 2 |
| 小二维度，任务前后指标对比（有任务推荐完成门店、无推荐完成门店分开统计增长率）<br>*   日均凭证量、曝光PV、CTCVR |  | 2 |
| 行业维度，月环比<br>*   日均凭证量、曝光PV、CTCVR<br>    <br>*   小二视角、管理者视角 |  | 2 |

### 17.2 排期

*   12.22 系分评审
    
*   12.23~1.4 开发（门店维度曝光 + 组织结构 ）
    
*   1.5~1.6 联调
    
*   1.7~1.14 跟测
    
*   1.15 发布