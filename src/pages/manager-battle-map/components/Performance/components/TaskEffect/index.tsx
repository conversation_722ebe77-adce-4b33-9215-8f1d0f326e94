import React, { useEffect, useState } from 'react';
import { Flex, Spin } from 'antd';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import { useRequest } from 'ahooks';
import styled from 'styled-components';
import { useStore } from '@/context/global-store';
import type { FilterOptions } from '@/types';
import TaskCompletionFunnel from './TaskCompletionFunnel';
import EffectComparison from './EffectComparison';
import { queryTaskEffect } from '../../services/taskEffectService';
import type { TaskEffectQueryParams, TaskEffectData } from '../../types/taskEffect';

const ContentWrapper = styled(Flex)`
  gap: 16px;
  align-items: stretch;
`;

const LeftSection = styled.div`
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
`;

const RightSection = styled.div`
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
`;

interface TaskEffectProps {
  pageSource: string;
  filterOptions: FilterOptions;
  queryDate?: string;
}

const TaskEffect: React.FC<TaskEffectProps> = ({ pageSource, filterOptions, queryDate }) => {
  // queryDate 保留用于未来扩展
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const _queryDate = queryDate;
  const { viewer } = useStore() || {};
  // PRD要求：默认值：当天到过去30天
  const [range, setRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs().subtract(1, 'day'), // 昨天（因为当天可能没有数据）
  ]);
  const [industryType, setIndustryType] = useState<string>('美食'); // 行业类型，如：美食

  // 获取任务效果数据（包括漏斗和效果对比）
  const {
    data: taskEffectData,
    loading: taskEffectLoading,
    run: fetchTaskEffectData,
  } = useRequest(
    async (params: TaskEffectQueryParams) => {
      const res = await queryTaskEffect(params);
      return res;
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    // 接口要求日期格式为 yyyyMMdd，如：20251002
    const startDate = range[0].format('YYYYMMDD');
    const endDate = range[1].format('YYYYMMDD');

    // 获取任务效果数据（一个接口返回所有数据）
    fetchTaskEffectData({
      pageSource,
      viewOperatorId: viewer || undefined,
      startDate,
      endDate,
      industryType, // 行业类型，如：美食
      ...filterOptions,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [range, industryType, filterOptions, viewer, pageSource]);

  const handleRangeChange = (value: [Dayjs, Dayjs] | null) => {
    if (value) {
      setRange(value);
    }
  };

  // 从接口返回的数据中提取漏斗数据和效果对比数据
  const taskCompletionData: TaskEffectData = taskEffectData
    ? {
        shopFunnel: taskEffectData.shopFunnel,
        taskFunnel: taskEffectData.taskFunnel,
      }
    : undefined;

  const comparisonData = taskEffectData
    ? {
        voucherQuantityPerStore: taskEffectData.voucherQuantityPerStore,
        exposurePvPerStore: taskEffectData.exposurePvPerStore,
        ctcvrPerStore: taskEffectData.ctcvrPerStore,
      }
    : undefined;

  return (
    <Spin spinning={taskEffectLoading}>
      <Flex vertical gap={16}>
        {/* 主要内容区域 */}
        <ContentWrapper>
          {/* 左侧：任务完成数据 */}
          <LeftSection>
            <TaskCompletionFunnel
              data={taskCompletionData}
              range={range}
              onRangeChange={handleRangeChange}
            />
          </LeftSection>

          {/* 右侧：完成后效果对比 */}
          <RightSection>
            <EffectComparison
              data={comparisonData}
              industryType={industryType}
              onIndustryTypeChange={setIndustryType}
              pageSource={pageSource}
            />
          </RightSection>
        </ContentWrapper>
      </Flex>
    </Spin>
  );
};

export default TaskEffect;
