import React from 'react';
import { Flex, Select, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import type { TaskEffectData } from '../../types/taskEffect';

const EffectComparisonContainer = styled(Flex)`
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 4px;
  height: 100%;
  flex: 1;
`;

const SectionTitle = styled.div`
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
`;

const QuestionIcon = styled(QuestionCircleOutlined)`
  color: rgba(0, 0, 0, 0.45);
  cursor: help;
  font-size: 14px;
`;

const WarningText = styled.span`
  font-size: 12px;
  color: #ff4d4f;
  white-space: nowrap;
  font-weight: 400;
`;

const MoreLink = styled.span`
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
  white-space: nowrap;

  &:hover {
    color: #40a9ff;
  }
`;

const IndicatorCard = styled.div`
  padding: 16px;
  border: 1px solid #f0f0f0;
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const IndicatorHeader = styled(Flex)`
  margin-bottom: 16px;
`;

const IndicatorName = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
`;

const IndicatorData = styled(Flex)`
  width: 100%;
  flex: 1;
`;

const DataColumn = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
`;

const DataLabel = styled.div`
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  white-space: nowrap;
`;

const DataValue = styled.div`
  font-size: 24px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  line-height: 1.2;
`;

const DataValueSmall = styled.div`
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
`;

const GrowthValue = styled(DataValue)`
  color: #52c41a;
`;

const IndustrySelect = styled(Select)`
  .ant-select-selector {
    padding: 0 4px !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
  }

  .ant-select-selection-item {
    padding: 0 !important;
    line-height: 14px !important;
  }

  .ant-select-arrow {
    margin-top: -7px;
  }
`;

const HeaderRow = styled(Flex)`
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  /* margin-bottom 由父容器的 gap 控制 */
`;

const IndicatorsList = styled(Flex)`
  flex: 1;
  min-height: 0;
  flex-direction: column;
  gap: 16px;
`;

interface EffectComparisonProps {
  data?: Pick<TaskEffectData, 'voucherQuantityPerStore' | 'exposurePvPerStore' | 'ctcvrPerStore'>;
  industryType: string; // 行业类型，如：美食
  onIndustryTypeChange: (value: string) => void;
  pageSource?: string; // 页面来源，用于判断是否显示"更多小二数据"
}

const EffectComparison: React.FC<EffectComparisonProps> = ({
  data,
  industryType,
  onIndustryTypeChange,
  pageSource,
}) => {
  // 行业选项（使用中文值，对应接口文档的 industryType）
  const industryOptions = [
    { label: '美食', value: '美食' },
    { label: '休娱', value: '休娱' },
    { label: '医疗', value: '医疗' },
    { label: '美业', value: '美业' },
    { label: '教培', value: '教培' },
  ];

  // 格式化数值显示
  const formatValue = (value: number, type: 'voucher' | 'exposure' | 'ctcvr'): string => {
    if (type === 'ctcvr') {
      return `${value.toFixed(1)}%`;
    }
    return value.toFixed(1);
  };

  // 格式化增长率显示
  const formatGrowthRate = (rate: number): string => {
    const sign = rate >= 0 ? '+' : '';
    return `${sign}${rate.toFixed(2)}%`;
  };

  // 获取指标Tooltip文本
  const getIndicatorTooltip = (key: string): string => {
    if (key === 'voucherQuantityPerStore') {
      return '店均日均凭证量，顾客在高德产生凭证量，包括（在投-美食/休娱：有效凭证到店、计费客资、ocpc客资；在投-医疗/美业/教培：计费客资、OCPC客资、cpc客资、订单量；纯年费：订单量、订座量）';
    }
    if (key === 'exposurePvPerStore') {
      return '店均日均门店详情页的曝光量';
    }
    if (key === 'ctcvrPerStore') {
      return '店均日均凭证量/店均日均曝光PV';
    }
    return `${indicators.find((ind) => ind.key === key)?.name || ''}说明`;
  };

  // 指标配置
  const indicators = [
    {
      key: 'voucherQuantityPerStore',
      name: '店均日均凭证量',
      data: data?.voucherQuantityPerStore,
      unit: '个',
      formatter: (val: number) => formatValue(val, 'voucher'),
    },
    {
      key: 'exposurePvPerStore',
      name: '店均日均曝光PV',
      data: data?.exposurePvPerStore,
      unit: '个',
      formatter: (val: number) => formatValue(val, 'exposure'),
    },
    {
      key: 'ctcvrPerStore',
      name: '店均日均CTCVR',
      data: data?.ctcvrPerStore,
      unit: '%',
      formatter: (val: number) => formatValue(val, 'ctcvr'),
    },
  ];

  return (
    <EffectComparisonContainer vertical gap={16}>
      {/* 标题行 */}
      <HeaderRow>
        <Flex align="center" gap={8}>
          <SectionTitle>完成后效果对比</SectionTitle>
          <Tooltip title="任务完成前后数据指完成前后7天的数据，大盘增长数据指无任务完成门店的近两月数据对比，行业增长率指全行业门店两月数据对比">
            <QuestionIcon />
          </Tooltip>
        </Flex>
        <Flex gap={16} align="center">
          {pageSource === 'MANAGER_PAGE' && <MoreLink>更多小二数据</MoreLink>}
          <MoreLink>更多商户数据</MoreLink>
          <MoreLink>更多门店数据</MoreLink>
        </Flex>
      </HeaderRow>

      {/* 指标卡片列表 */}
      <IndicatorsList>
        {indicators.map((indicator) => {
          if (!indicator.data) return null;
          const { beforeValue, afterValue, growthRate, marketGrowthRate, industryGrowthRate } =
            indicator.data;

          return (
            <IndicatorCard key={indicator.key}>
              <IndicatorHeader align="center" justify="space-between">
                <Flex align="center" gap={8}>
                  <IndicatorName>{indicator.name}</IndicatorName>
                  <Tooltip title={getIndicatorTooltip(indicator.key)}>
                    <QuestionIcon />
                  </Tooltip>
                </Flex>
                <WarningText>行业数据禁止以任务形式同步至外部人员</WarningText>
              </IndicatorHeader>
              <IndicatorData justify="space-between" align="center">
                <DataColumn>
                  <DataLabel>任务完成前</DataLabel>
                  <DataValue>
                    {indicator.formatter(beforeValue)}
                    {indicator.unit}
                  </DataValue>
                </DataColumn>
                <DataColumn>
                  <DataLabel>任务完成后</DataLabel>
                  <DataValue>
                    {indicator.formatter(afterValue)}
                    {indicator.unit}
                  </DataValue>
                </DataColumn>
                <DataColumn>
                  <DataLabel>完成后增长</DataLabel>
                  <GrowthValue>{formatGrowthRate(growthRate)}</GrowthValue>
                </DataColumn>
                <DataColumn>
                  <DataLabel>大盘增长率</DataLabel>
                  <DataValueSmall>{formatGrowthRate(marketGrowthRate)}</DataValueSmall>
                </DataColumn>
                <DataColumn>
                  <DataLabel>行业增长率</DataLabel>
                  <DataValueSmall>
                    <span>{formatGrowthRate(industryGrowthRate)}</span>
                    <IndustrySelect
                      size="small"
                      style={{ width: 80, marginLeft: 8 }}
                      value={industryType}
                      onChange={onIndustryTypeChange}
                      options={industryOptions}
                      bordered={false}
                    />
                  </DataValueSmall>
                </DataColumn>
              </IndicatorData>
            </IndicatorCard>
          );
        })}
      </IndicatorsList>
    </EffectComparisonContainer>
  );
};

export default EffectComparison;
