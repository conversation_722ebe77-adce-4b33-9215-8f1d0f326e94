import { Typography } from 'antd';

import styles from './index.module.less';

interface StoreProps {
  data: {
    phaseShopCount: number;
    ratio: string;
    monthOnMonth: number;
    weekOnWeek: number;
  };
}
const Store = ({ data = {} }: StoreProps) => {
  const renderTrend = (val) => {
    const isValidNumber = val !== null && val !== undefined && !isNaN(val);
    // 转换为百分比，保留两位小数
    const percentStr = isValidNumber ? `${parseFloat(val.toFixed(2))}%` : '--';
    const numValue = isValidNumber ? parseFloat(val) : 0;
    const isUp = numValue < 0;
    const color = isUp ? '#52C41A' : '#F53F3F';
    const img = isUp
      ? 'https://img.alicdn.com/imgextra/i3/O1CN01fhPoeD1bY3ocFtGsL_!!6000000003476-55-tps-7-12.svg'
      : 'https://img.alicdn.com/imgextra/i2/O1CN01X24Bgj28YrpjhgUlC_!!6000000007945-55-tps-7-12.svg';
    return (
      <span style={{ color, verticalAlign: 'bottom' }}>
        （ <img src={img} alt="" style={{ marginRight: 4 }} />
        {percentStr || 0} ）
      </span>
    );
  };

  return (
    <div className={styles.card_store}>
      <Typography.Text>
        门店数
        <Typography.Text strong style={{ color: 'rgba(0, 0, 0, 0.85)', padding: '0 4px' }}>
          {data?.phaseShopCount || 0}
        </Typography.Text>
        家，占比{' '}
        <Typography.Text strong style={{ color: 'rgba(0, 0, 0, 0.85)' }}>
          {`${data?.ratio || 0}%`}
        </Typography.Text>
        ，月环比
        {renderTrend(data?.monthOnMonth)}，周环比 {renderTrend(data?.weekOnWeek)}
      </Typography.Text>
    </div>
  );
};

export default Store;
