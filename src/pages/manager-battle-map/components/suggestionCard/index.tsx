/**
 * 诊断建议卡片
 */

import React from 'react';
import { MarkdownPreview } from '../markdown-preview';
import { Card, Space } from 'antd';
import { SUGGESTION_TEXT } from '../../constant';
import Expediting from '../MerchantStratification/components/expediting';
import Store from './store';

import styles from './index.module.less';

const Suggestion = ({ suggest, remindList = [], storeDataList = {}, agent = false }) => {
  // 去催办
  const expeditingRender = () => {
    if (remindList && remindList.length > 0) {
      return (
        <Space>
          {(remindList || []).map((task) => (
            <Expediting
              key={task.sellerId}
              sellerId={task.displayId}
              suggest={task.suggest}
              agent={agent}
            />
          ))}
        </Space>
      );
    }
  };

  return (
    <>
      <Card className={styles.detail_card} bordered={false}>
        <div className={styles.card_title}>
          <div className={styles.card_title_text}>
            <img
              src="https://img.alicdn.com/imgextra/i4/O1CN01ieqykX1z2VIrXmCh4_!!6000000006656-55-tps-23-24.svg"
              alt=""
            />
            诊断建议
          </div>
        </div>
        <div className={styles.card_content}>
          {storeDataList && Object.keys(storeDataList).length > 0 && <Store data={storeDataList} />}
          {suggest ? (
            <MarkdownPreview source={suggest} />
          ) : (
            <MarkdownPreview source={SUGGESTION_TEXT} />
          )}
        </div>

        {/* 催办列表 */}
        {expeditingRender()}
      </Card>
    </>
  );
};

export default Suggestion;
