import React, { useEffect } from 'react';
import { Input } from 'antd';
import type { FilterOptions } from '@/types';
import { EntityType } from '@/types';

/**
 * 组件Props接口
 */
interface AgentFilterProps {
  /** 筛选条件变化回调 */
  onFilterChange: (filterOptions: FilterOptions) => void;
  /** 初始值 */
  defaultValue?: Partial<FilterOptions>;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * Agent筛选组件 - 用于外部服务商
 *
 * ## 功能说明
 *
 * ### 主要功能
 * 1. **显示服务商名称**: 展示当前登录服务商的名称 (window.APP.comName)
 * 2. **自动筛选**: 组件挂载时自动调用 onFilterChange 传递服务商ID
 * 3. **只读展示**: 服务商无法切换筛选条件，只能查看自己的数据
 *
 * ### 数据来源
 * - `window.APP.comName`: 服务商名称
 * - `window.APP.comId`: 服务商ID
 *
 * ### 自动筛选逻辑
 * 组件挂载后立即调用 `onFilterChange({ channelCompanyIds: window.APP.comId })`
 */
const AgentFilter: React.FC<AgentFilterProps> = ({
  onFilterChange,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  defaultValue,
  disabled = false,
}) => {
  // 获取服务商信息
  const comName = (window as any)?.APP?.comName || '未知服务商';
  const comId = (window as any)?.APP?.comId;

  /**
   * 组件挂载时自动触发筛选
   */
  useEffect(() => {
    if (comId) {
      onFilterChange({
        channelCompanyIds: [String(comId)],
        entityType: EntityType.COM,
      });
    }
  }, [comId, onFilterChange]);

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginRight: 16 }}>
      <span style={{ color: 'rgba(0, 0, 0, 0.65)', fontSize: 14 }}>服务商:</span>
      <Input
        value={comName}
        readOnly
        disabled={disabled}
        style={{
          width: 300,
          backgroundColor: '#f5f5f5',
          cursor: 'default',
        }}
        placeholder="服务商名称"
      />
    </div>
  );
};

export default AgentFilter;
