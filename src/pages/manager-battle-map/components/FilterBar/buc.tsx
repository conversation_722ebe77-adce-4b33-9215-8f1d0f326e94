import React, { useCallback, useState } from 'react';
import { Select, Form } from 'antd';
import { useRequest } from 'ahooks';
import afetch from '@alife/amap-fetch';
import JobTree from '@alife/mo-job-tree-select';
import type { FilterOptions } from '@/types';
import { EntityType } from '@/types';
import type { UserInfo } from '../../types';
import { getEnv, Env } from '@alife/amap-mp-utils';

// 预发 渠道业务部 11835003
// 线上 渠道商家成长部 560102013
const CHANNEL_BUSINESS_DEPT_ID = getEnv() === Env.PRE ? '30234568' : '560102013';

interface Organization {
  node: {
    jobPath: string;
    leafJob: boolean;
  };
}
const isChannelBusinessLeaf = (organization: Organization) => {
  return (
    organization?.node?.jobPath?.includes(CHANNEL_BUSINESS_DEPT_ID) &&
    organization?.node?.leafJob === true
  );
};

/**
 * 组件Props接口
 */
interface BucFilterProps {
  /** 筛选条件变化回调 */
  onFilterChange: (filterOptions: FilterOptions) => void;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * BUC筛选组件 - 用于内部员工
 *
 * ## 功能说明
 *
 * ### 主要功能
 * 1. **组织架构选择**: 使用 @alife/mo-job-tree-select 组件进行组织架构选择
 * 2. **条件联动**: 根据组织架构选择结果动态显示后续筛选项
 * 3. **渠道商家成长部判断**: 检查选中节点路径是否包含"渠道商家成长部"
 * 4. **用户筛选**: 展示组织架构选择结果的用户列表
 * 5. **服务商查询**: 调用searchAgent接口获取服务商列表
 * 6. **筛选联动**: 最终触发全局filterOptions变化
 *
 * ### 交互逻辑
 * ```
 * 组织架构选择 → 判断是否包含"渠道商家成长部"且是叶子节点
 *                    ↓
 *                是 → 显示节点用户选择框 → 显示服务商选择框
 *                    ↓                    ↓
 *               选择用户 → 调用searchAgent → 选择服务商 → 触发filterOptions
 *                    ↓
 *                否 → 直接触发filterOptions
 * ```
 */
const BucFilter: React.FC<BucFilterProps> = ({ onFilterChange, disabled = false }) => {
  const [form] = Form.useForm();
  const [userOptions, setUserOptions] = useState<UserInfo[]>([]);

  // 使用 useWatch 监听表单值变化
  const organizationValue = Form.useWatch('organization', form);
  const userIdValue = Form.useWatch('userId', form);

  // 通过计算是否显示选择器
  const showUserSelect = isChannelBusinessLeaf(organizationValue);
  const showAgentSelect = showUserSelect && userIdValue;

  /**
   * 服务商查询请求
   */
  const {
    data: agentOptions = [],
    loading: agentLoading,
    run: searchAgents,
  } = useRequest(
    async (userId: string) => {
      if (!userId) return [];

      try {
        // 调用searchAgent接口获取服务商列表
        const result = await afetch({
          params: {
            action: 'alsc-kbt-merchant-admin.AlscAgentGateWayWeb.queryAlscAgentCompanyByParams',
            bizContent: {
              operationOsUserId: userId,
            },
          },
        });

        // 转换数据格式为组件需要的格式
        return result?.data?.data?.result || [];
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('查询服务商列表失败:', error);
        return [];
      }
    },
    {
      manual: true,
      onError: (error) => {
        // eslint-disable-next-line no-console
        console.error('searchAgent接口调用失败:', error);
      },
    },
  );

  /**
   * 封装筛选变化处理
   */
  const handleFilterChange = useCallback(
    (organization?: any, userId?: string, agentId?: string) => {
      let entityType = EntityType.JOB;
      let filterOptions: FilterOptions = {};

      if (agentId) {
        entityType = EntityType.COM;
        filterOptions = {
          channelCompanyIds: [String(agentId)],
          entityType,
        };
      } else if (userId) {
        entityType = EntityType.CHANNEL_OPT;
        filterOptions = {
          channelOptStaffIds: [String(userId)],
          entityType,
        };
      } else if (organization) {
        entityType = EntityType.JOB;
        filterOptions = {
          jobIds: [String(organization.node?.id)],
          entityType,
          jobLevel: organization.node?.jobPath?.split('/').filter((item) => !!item).length,
          leaf: organization.node?.leafJob,
        };
      }

      onFilterChange(filterOptions);
    },
    [onFilterChange],
  );

  /**
   * 表单字段变化监听
   */
  const handleFieldsChange = useCallback(
    (changedFields: any[]) => {
      changedFields.forEach((field) => {
        const { name, value } = field;

        if (name[0] === 'organization') {
          form.setFieldsValue({ userId: undefined, agentId: undefined });
          // 统一设置用户选项并触发筛选
          const members = value?.node?.members || [];
          const userList: UserInfo[] = members.map((member: any) => ({
            userId: member.userId || member.id,
            displayName: member.displayName,
          }));
          setUserOptions(userList || []);

          // 检查是否包含自己，如果是且在末级部门，则自动选中
          const currentUserId = window.APP?.uvUserId;
          if (
            userList.some((user) => user.userId === currentUserId) &&
            isChannelBusinessLeaf(value)
          ) {
            form.setFieldsValue({ userId: currentUserId });
            searchAgents(currentUserId);
            handleFilterChange(value, currentUserId);
          } else {
            handleFilterChange(value);
          }
        } else if (name[0] === 'userId') {
          form.setFieldsValue({ agentId: undefined });
          const userId = value;
          searchAgents(userId);
          handleFilterChange(organizationValue, userId);
        } else if (name[0] === 'agentId') {
          handleFilterChange(organizationValue, userIdValue, value);
        }
      });
    },
    [form, searchAgents, setUserOptions, handleFilterChange, organizationValue, userIdValue],
  );

  const handleLoadFinish = useCallback(
    (data: any) => {
      const organization = {
        value: data[0].value,
        title: data[0].title,
        node: data[0],
      };
      form.setFieldsValue({
        organization,
      });
      handleFieldsChange([{ name: ['organization'], value: organization }]);
    },
    [form],
  );

  return (
    <Form form={form} layout="inline" onFieldsChange={handleFieldsChange}>
      <div style={{ display: 'flex' }}>
        {/* 组织架构选择 - 使用 @alife/mo-job-tree-select */}
        <Form.Item name="organization">
          <JobTree
            style={{ width: 300 }}
            onlyUserJob
            userJobOnChangePath
            multiple={false}
            onFirstLoaded={handleLoadFinish}
          />
        </Form.Item>

        {/* 用户选择框 - 仅在包含渠道业务部时显示 */}
        {showUserSelect && (
          <Form.Item name="userId">
            <Select
              style={{ width: 240 }}
              placeholder="选择小二"
              allowClear
              disabled={
                disabled || userOptions.some((user) => user.userId === window.APP?.uvUserId)
              }
              options={userOptions.map((user) => ({
                value: user.userId,
                label: user.displayName,
                title: `${user.displayName}(${user.userId})`,
              }))}
              showSearch
              filterOption={(input, option) =>
                (option?.label?.toString() || '').toLowerCase().includes(input.toLowerCase()) ||
                (option?.title?.toString() || '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        )}

        {/* 服务商选择框 - 仅在选择用户后显示 */}
        {showAgentSelect && (
          <Form.Item name="agentId">
            <Select
              style={{ width: 240 }}
              placeholder="选择服务商"
              allowClear
              disabled={disabled}
              loading={agentLoading}
              options={agentOptions.map((agent) => ({
                value: agent.companyId,
                label: agent.companyName,
              }))}
              showSearch
              filterOption={(input, option) =>
                (option?.label?.toString() || '').toLowerCase().includes(input.toLowerCase())
              }
              notFoundContent={agentLoading ? '加载中...' : '暂无数据'}
            />
          </Form.Item>
        )}
      </div>
    </Form>
  );
};

export default BucFilter;
