import { Card, List } from 'antd';
import React from 'react';
import { MarkdownPreview } from '../../../markdown-preview';

import styles from './index.module.less';

const Overview = ({ title = '', extra = '', data = '', dataList = [] }) => {
  const LayerList = () => {
    return (
      <List
        grid={{ gutter: 16, column: 4, xs: 1, sm: 2, md: 2, lg: 4 }}
        dataSource={dataList}
        style={{ padding: '0 16px' }}
        renderItem={(item) => (
          <List.Item key={item.layerCode}>
            <div className={styles.layer_name}>{item.layerName}</div>
            <div className={styles.layer_item} style={{ padding: '5px 0' }}>
              门店 {item.shopCount}
            </div>
            {/* <div className={styles.layer_item}>任务 {item.taskCount}</div> */}
          </List.Item>
        )}
      />
    );
  };
  return (
    <Card
      title={
        <div className={styles.drawer_card_title}>
          {title} <span>{extra}</span>
        </div>
      }
      variant="borderless"
      headStyle={{ padding: 0, border: 'none' }}
      bodyStyle={{ padding: '0' }}
      className={styles.drawer_card_header}
    >
      <div style={{ paddingBottom: '16px' }}>
        <MarkdownPreview source={data} />
      </div>

      {dataList && dataList?.length > 0 && <LayerList />}
    </Card>
  );
};

export default Overview;
