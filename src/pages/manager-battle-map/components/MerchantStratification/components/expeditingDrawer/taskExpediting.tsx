import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Form, Button, Radio, Card, message, Empty } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import {
  queryOptTodoTask,
  queryPriorityTaskType,
  remindOptTask,
} from '@/services/manager-battle-map';

import styles from './index.module.less';

const TaskExpediting = ({ onClose, sellerId }) => {
  const [form] = Form.useForm();
  const actionRef = useRef(); // 用于控制表格刷新
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const [taskList, setTaskList] = useState([]); // 任务列表

  const columns = [
    { title: '商户名称', dataIndex: 'merchantName' },
    { title: '未达标门店数量', dataIndex: 'shopCount', render: (val) => val || '--' },
    { title: '任务总量', dataIndex: 'totalTaskCount' },
  ];

  // 获取任务列表
  const getTaskList = async () => {
    try {
      const res = await queryPriorityTaskType({ params: { scene: 'LEADER_URGES' } });
      const list = (res?.typeList || [])?.map((item) => {
        return { label: item?.taskName, value: item?.taskType };
      });
      setTaskList(list);
    } catch (err) {
      console.log('请求失败：', err);
    }
  };

  useEffect(() => {
    getTaskList();
  }, []);
  // 监听 taskType 变化
  const taskType = Form.useWatch('taskType', form);

  // 当 taskType 改变时，重置选中行并刷新表格
  useEffect(() => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    if (taskType) {
      actionRef?.current?.reload(); // 触发 request
    }
  }, [taskType]);

  const onRowSelectionChange = useCallback(
    (keys, rows) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
      form.setFieldsValue({ merchantIdList: rows });
    },
    [form],
  );

  const rowSelection = useMemo(
    () => ({
      selectedRowKeys,
      onChange: onRowSelectionChange,
    }),
    [selectedRowKeys, onRowSelectionChange],
  );

  // 提交
  const handleSubmit = useCallback(() => {
    form
      .validateFields(['taskType'])
      .then(async (values) => {
        // 手动校验商户选择
        if (!selectedRows || selectedRows.length === 0) {
          message.error('请选择一个商户');
          return;
        }

        setSubmitting(true);

        try {
          // ✅ 提取 merchantId 数组
          const merchantIdList = selectedRows.map((item) => item.merchantId);

          const payload = {
            taskType: values.taskType,
            merchantIdList,
            ...sellerId,
          };

          await remindOptTask({ params: payload });
          onClose();
          message.success('催办成功');
        } catch (err) {
          message.error('催办失败，请重试');
          setSubmitting(false);
        } finally {
          setSubmitting(false);
        }
      })
      .catch((err) => {
        console.log('校验失败：', err);
        setSubmitting(false);
      });
  }, [form, selectedRows, sellerId, onClose]);

  return (
    <Card
      title="任务催办"
      bodyStyle={{ paddingBottom: 0 }}
      className={styles.actions_card}
      actions={[
        <div key="actions" style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
          <Button type="primary" onClick={handleSubmit} loading={submitting}>
            确认催办
          </Button>
        </div>,
      ]}
    >
      <Form form={form} initialValues={{ taskType: undefined, merchantIdList: [] }}>
        <Form.Item
          name="taskType"
          label="任务名称"
          rules={[{ required: true, message: '请选择一个任务' }]}
        >
          <Radio.Group options={taskList} />
        </Form.Item>

        <Form.Item name="merchantIdList">
          <ProTable
            rowKey="merchantId"
            actionRef={actionRef}
            columns={columns}
            search={false}
            options={false}
            rowSelection={rowSelection}
            pagination={{
              showSizeChanger: true,
            }}
            locale={{
              emptyText: (
                <Empty
                  description={form?.getFieldValue('taskType') ? '暂无数据' : '请先选择任务名称'}
                />
              ),
            }}
            request={async (param) => {
              const params = {
                ...sellerId,
                page: {
                  pageNo: param?.current,
                  pageSize: param?.pageSize,
                },
                taskType: form?.getFieldValue('taskType'),
              };
              try {
                if (!form?.getFieldValue('taskType')) {
                  return {
                    data: [],
                    success: true,
                  };
                }
                const res = await queryOptTodoTask({ params });
                return {
                  data: res?.dataList || [],
                  success: true,
                  total: res?.pageInfo?.totalCount || 0,
                };
              } catch (err) {
                console.log('请求失败：', err);
              }
            }}
          />
        </Form.Item>
      </Form>
    </Card>
  );
};

export default TaskExpediting;
