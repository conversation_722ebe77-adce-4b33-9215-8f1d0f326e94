/**
 * 催办抽屉
 */

import { Drawer } from 'antd';
import React from 'react';
import MerchantAnalysis from './merchantAnalysis';
import TaskExpediting from './taskExpediting';

interface ExpeditingDrawerProps {
  open: boolean;
  onClose: (open: boolean) => void;
  sellerId: { companyId: string | number } | { staffId: string | number }; // 服务商id / 小二id
  title?: string; // 标题
}
const ExpeditingDrawer = (props: ExpeditingDrawerProps) => {
  const { open, onClose, sellerId, title = '服务商诊断详情' } = props;

  return (
    <Drawer
      title={title}
      onClose={onClose}
      open={open}
      closable
      destroyOnHidden
      width={930}
      bodyStyle={{ background: '#FAFAFA', padding: '16px 24px' }}
    >
      <MerchantAnalysis sellerId={sellerId} />
      <TaskExpediting onClose={onClose} sellerId={sellerId} />
    </Drawer>
  );
};

export default ExpeditingDrawer;
