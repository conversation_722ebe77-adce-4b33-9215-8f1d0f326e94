import { Card, Empty } from 'antd';
import React, { useEffect } from 'react';
import { queryEmployeeAnalysis } from '@/services/manager-battle-map';
import { useFilterOptions } from '@/context/FilterContext';
import { useRequest } from 'ahooks';
import Overview from './overview';

import styles from './index.module.less';

const MerchantAnalysis = ({ sellerId }) => {
  const { filterOptions } = useFilterOptions();

  // 获取小二分析
  const {
    run: onEmployeeAnalysis,
    data: employeeData,
    loading: employeeLoading,
  } = useRequest(queryEmployeeAnalysis, {
    manual: true,
    onSuccess: (res) => {
      return res;
    },
  });
  useEffect(() => {
    const params = {
      ...filterOptions,
      ...sellerId,
    };
    onEmployeeAnalysis({ params });
  }, [JSON.stringify(filterOptions)]);

  return (
    <>
      <Card
        loading={employeeLoading}
        className={styles.detail_card}
        bodyStyle={{ padding: '16px 20px' }}
      >
        <div className={styles.card_title}>
          <div className={styles.card_title_text}>
            <img
              src="https://img.alicdn.com/imgextra/i4/O1CN01ieqykX1z2VIrXmCh4_!!6000000006656-55-tps-23-24.svg"
              alt=""
            />
            小二分析
          </div>
          <span className={styles.card_title_extra}>平台为您推荐以下数据，辅助您完成运维任务</span>
        </div>
        {employeeData?.layerDistribution && employeeData?.layerDistribution.length > 0 ? (
          <Overview title="门店概览" dataList={employeeData?.layerDistribution} />
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
        {employeeData?.diagnose ? (
          <Overview title="诊断建议" data={employeeData?.diagnose} />
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </Card>
    </>
  );
};

export default MerchantAnalysis;
