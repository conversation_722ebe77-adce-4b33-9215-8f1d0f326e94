import { useRequest } from 'ahooks';
import { queryPhasedDiagnosticsData } from '@/services/manager-battle-map';

/**
 * 自定义 Hook：用于拉取阶段诊断数据
 * 返回 run 函数用于手动触发请求
 */
export function usePhasedDiagnostics() {
  return useRequest(
    async (params) => {
      const res = await queryPhasedDiagnosticsData({ params });

      // ✅ 只返回业务数据部分
      return res || {};
    },
    {
      manual: true, // ✅ 手动触发
      onError: (error) => {
        console.error('[usePhasedDiagnostics] 请求失败:', error.message);
      },
    },
  );
}
