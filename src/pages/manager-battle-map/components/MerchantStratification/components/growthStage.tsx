/**
 * 成长期数据诊断
 */

import { Card, Form, Select } from 'antd';
import React, { useEffect } from 'react';
import SuggestionCard from '../../suggestionCard';
import { useFilterOptions } from '@/context/FilterContext';
import {
  DEVELOP_DATA_TYPE_OPTIONS,
  SORT_ORDER_OPTIONS,
  transformToStackedColumnData,
} from '@/pages/manager-battle-map/constant';
import { usePhasedDiagnostics } from './useHooks';
import ColumnCharts from './columnCharts';

import styles from '../index.module.less';

const GrowthStage = () => {
  const [form] = Form.useForm();
  const { filterOptions } = useFilterOptions();

  const { run: fetchDiagnostics, data, loading } = usePhasedDiagnostics();
  const storeDataList = {
    phaseShopCount: data?.phaseShopCount,
    ratio: data?.ratio,
    monthOnMonth: data?.monthOnMonth,
    weekOnWeek: data?.weekOnWeek,
  };

  // 初始加载：组件挂载或 filterOptions 变化时请求一次
  useEffect(() => {
    const values = form.getFieldsValue();
    const params = {
      ...filterOptions,
      ...values,
      stage: 'DEVELOP',
    };

    fetchDiagnostics(params);
  }, [JSON.stringify(filterOptions), fetchDiagnostics, form]);

  // 表单变化时，重新请求数据
  const onValuesChange = () => {
    const values = form.getFieldsValue();
    const params = {
      ...filterOptions,
      ...values,
      stage: 'DEVELOP',
    };

    fetchDiagnostics(params);
  };

  const extraRender = () => {
    return (
      <Form onValuesChange={onValuesChange} form={form} layout="inline">
        <Form.Item name="dataType" label="" initialValue="NUM_OF_SHOP">
          <Select style={{ width: 120 }} options={DEVELOP_DATA_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item name="sortType" label="" initialValue="DESC" style={{ marginRight: '0' }}>
          <Select style={{ width: 120 }} options={SORT_ORDER_OPTIONS} />
        </Form.Item>
      </Form>
    );
  };

  return (
    <Card
      title="成长期数据诊断"
      variant="borderless"
      extra={<span className={styles.extraText}>数据来源于昨天</span>}
      bodyStyle={{ padding: '16px 24px' }}
      loading={loading}
    >
      {/* 诊断建议 */}
      <SuggestionCard
        suggest={data?.suggest || '暂无诊断数据'}
        remindList={data?.remindList || []}
        storeDataList={storeDataList || {}}
        agent={data?.agent}
      />
      <Card
        title="日均凭证量分布"
        variant="borderless"
        extra={extraRender()}
        bodyStyle={{
          padding: '16px 0 0',
          overflow: data?.data?.length > 15 && data?.data?.length < 200 ? 'auto' : null,
        }}
        headStyle={{ padding: '0', border: 'none' }}
        className={styles.no_shadow_card}
      >
        {/* 图表 */}
        <ColumnCharts
          data={transformToStackedColumnData(data?.data || []) || []}
          agent={data?.agent}
          remindFlag={data?.remindFlag}
          remindType={data?.remindType}
          xField="displayId"
          yField="numOfStore"
          width={
            data?.data?.length > 15 && data?.data?.length < 200
              ? Math.max(data?.data?.length * 120)
              : null
          }
          height={255}
          customConfig={{
            stack: true,
            colorField: 'displayColor',
            style: {
              fill: (datum) => datum.displayColor,
              maxWidth: 40,
              insetLeft: 20,
              insetRight: 20,
            },
            scrollbar:
              data?.data?.length > 200
                ? {
                    x: {
                      ratio: 0.05,
                    },
                  }
                : {},
            legend: {
              color: {
                itemLabelText: (datum, index) => {
                  return transformToStackedColumnData(data?.data || [])[index]?.originalDesc || '';
                },
                itemMarkerFill: (datum) => {
                  return datum.label;
                },
              },
            },
          }}
        />
      </Card>
    </Card>
  );
};

export default GrowthStage;
