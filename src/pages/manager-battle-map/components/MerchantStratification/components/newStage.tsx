/**
 * 新手期数据诊断
 */

import { Card, Form, Select } from 'antd';
import React, { useEffect } from 'react';
import SuggestionCard from '../../suggestionCard';
import {
  NEWBIE_DATA_TYPE_OPTIONS,
  STAY_DURATION_OPTIONS,
  transformToStackedColumnData,
} from '@/pages/manager-battle-map/constant';
import { useFilterOptions } from '@/context/FilterContext';
import ColumnCharts from './columnCharts';
import { usePhasedDiagnostics } from './useHooks';

import styles from '../index.module.less';

const NewStage = () => {
  const [form] = Form.useForm();
  const { filterOptions } = useFilterOptions();

  const { run: fetchDiagnostics, data, loading } = usePhasedDiagnostics();
  const storeDataList = {
    phaseShopCount: data?.phaseShopCount,
    ratio: data?.ratio,
    monthOnMonth: data?.monthOnMonth,
    weekOnWeek: data?.weekOnWeek,
  };

  // 初始加载：组件挂载或 filterOptions 变化时请求一次
  useEffect(() => {
    const values = form.getFieldsValue();
    const { dataType = 'NEWBIE_SHOP', stayTimeRange = 'MORE_THAN_FOURTEEN' } = values;
    const params = {
      ...filterOptions,
      dataType,
      stayTimeRange,
      stage: 'NEWBIE',
    };

    fetchDiagnostics(params);
  }, [JSON.stringify(filterOptions), fetchDiagnostics, form]);

  // 表单变化时，重新请求数据
  const onValuesChange = () => {
    const values = form.getFieldsValue();
    const { dataType = 'NEWBIE_SHOP', stayTimeRange = 'MORE_THAN_FOURTEEN' } = values;
    const params = {
      ...filterOptions,
      dataType,
      stayTimeRange,
      stage: 'NEWBIE',
    };
    // 如果 dataType 不等于 NEWBIE_SHOP，则删除 stayTimeRange 参数
    if (dataType !== 'NEWBIE_SHOP') {
      delete params.stayTimeRange;
    }
    fetchDiagnostics(params);
  };

  const extraRender = () => {
    const dataType = form.getFieldValue('dataType');
    return (
      <Form
        onValuesChange={onValuesChange}
        form={form}
        layout="inline"
        initialValues={{ dataType: 'NEWBIE_SHOP', stayTimeRange: 'MORE_THAN_FOURTEEN' }}
      >
        <Form.Item name="dataType" label="">
          <Select style={{ width: 240 }} options={NEWBIE_DATA_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item
          name="stayTimeRange"
          label=""
          style={dataType === 'NEWBIE_SHOP' ? { marginRight: '0' } : { display: 'none' }}
        >
          <Select style={{ width: 120 }} options={STAY_DURATION_OPTIONS} />
        </Form.Item>
      </Form>
    );
  };

  return (
    <Card
      title="新手期数据诊断"
      variant="borderless"
      extra={<span className={styles.extraText}>数据来源于昨天</span>}
      bodyStyle={{ padding: '16px 24px' }}
      loading={loading}
    >
      {/* 诊断建议 */}
      <SuggestionCard
        suggest={data?.suggest || '暂无诊断数据'}
        remindList={data?.remindList || []}
        storeDataList={storeDataList || {}}
        agent={data?.agent}
      />
      <Card
        title="指标&完成度"
        variant="borderless"
        extra={extraRender()}
        bodyStyle={{
          padding: '16px 0 0',
          overflow: data?.data?.length > 15 && data?.data?.length < 200 ? 'auto' : null,
        }}
        headStyle={{ padding: '0', border: 'none' }}
        className={styles.no_shadow_card}
      >
        {/* 图表 */}
        <ColumnCharts
          data={transformToStackedColumnData(data?.data || []) || []}
          agent={data?.agent}
          remindFlag={data?.remindFlag}
          remindType={data?.remindType}
          xField="displayId"
          yField="numOfStore"
          height={255}
          width={
            data?.data?.length > 15 && data?.data?.length < 200
              ? Math.max(data?.data?.length * 120)
              : null
          }
          // labelFormatterY={(val) => `${(parseFloat(val) * 100).toFixed(0)}%`}
          customConfig={{
            stack: true,
            colorField: 'displayColor',
            style: {
              fill: (datum) => datum.displayColor,
              maxWidth: 40,
              insetLeft: 20,
              insetRight: 20,
              minWidth: 20,
            },
            scrollbar:
              data?.data?.length > 200
                ? {
                    x: {
                      ratio: 0.05,
                    },
                  }
                : {},
            legend: {
              color: {
                itemLabelText: (datum, index) => {
                  return transformToStackedColumnData(data?.data || [])[index]?.originalDesc || '';
                },
                itemMarkerFill: (datum) => {
                  return datum.label;
                },
              },
            },
          }}
        />
      </Card>
    </Card>
  );
};

export default NewStage;
