import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Column } from '@ant-design/plots';
import ExpeditingDrawer from './expeditingDrawer';
import { Empty } from 'antd';
import { followUp } from '@/services/manager-battle-map';
import ConfirmSuggestionModal from './confirmSuggestionModal';

interface ColumnChartsProps {
  data: any[];
  xField: string;
  yField: string;
  height?: number;
  //   stack?: boolean; // 是否堆叠
  //   label?: any; // 标签(柱子上的数值展示)
  labelFormatterY?: (val: string) => string; // Y 轴标签格式化
  //   colorField?: string; // 柱子颜色(会显示图例)
  customConfig?: any; // 自定义配置，会覆盖默认配置
  remindFlag?: boolean; // 是否展示催办
  remindType?: string; // 催办类型
  agent?: boolean;
  width?: number;
}

const ColumnCharts = (props: ColumnChartsProps) => {
  const {
    data,
    width,
    xField,
    yField,
    height = 255,
    labelFormatterY,
    customConfig = {},
    remindFlag,
    agent,
    remindType,
  } = props;

  // 公共容器样式：居中内容，固定高度
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: `${height}px`,
    padding: '16px',
    boxSizing: 'border-box',
  };
  const chartRef = useRef(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [currentDatum, setCurrentDatum] = useState(''); // 当前点击的数据维度
  const [modalOpen, setModalOpen] = useState(false); // 控制中间弹窗
  const [follow, setFollow] = useState<any>({});

  const handleFollowUp = useCallback(async (displayId) => {
    if (!displayId) return;
    try {
      const res = await followUp({ params: { displayId, remindType } });

      // 设置当前数据项
      setCurrentDatum(displayId);

      // 根据返回结果决定是否打开抽屉
      if (res?.overLimit) {
        setModalOpen(true);
        setFollow(res);
      } else {
        setDrawerOpen(true);
      }
    } catch (error) {
      console.error('调用 followUp 接口失败:', error);
    }
  }, []);

  const labelFormatter = useCallback((datum) => {
    const { chart } = chartRef.current;
    if (!chart) return datum;

    const { document } = chart.getContext().canvas;
    const group = document.createElement('g', {});
    // 手动实现文本缩略逻辑
    const truncateText = (text: string, maxLength = 90) => {
      const textLabel = data.find((d) => d.displayId === datum)?.displayName || datum;
      if (!textLabel) return datum;

      // 简单的字符长度估算（中文字符按2个字符计算）
      const getTextWidth = (str: string) => {
        let widthColumn = 0;
        for (let i = 0; i < str.length; i++) {
          // 中文字符、全角字符等宽度较大
          if (str.charCodeAt(i) > 255) {
            widthColumn += 12; // 假设中文字符宽度为12px
          } else {
            widthColumn += 6; // 假设英文字符宽度为6px
          }
        }
        return widthColumn;
      };

      if (getTextWidth(textLabel) <= maxLength) {
        return textLabel;
      }

      // 逐步缩短文本直到符合长度要求
      let truncated = textLabel;
      while (getTextWidth(`${truncated}...`) > maxLength && truncated.length > 0) {
        truncated = truncated.slice(0, -1);
      }

      return `${truncated}...`;
    };

    // 应用缩略后的文本
    const truncatedText = truncateText(datum);

    // 添加缩略后的文本
    const label = document.createElement('text', {
      style: {
        text: truncatedText, // 使用缩略后的文本
        fontSize: 12,
        fill: 'gray',
        textAlign: 'center',
        transform: `translate(0, 20)`,
      },
    });

    // ✅ 模拟 Ant Design 默认按钮样式
    const rect = document.createElement('rect', {
      style: {
        x: -24, // 宽度 48
        y: -12, // 高度 24
        width: 48,
        height: 24,
        radius: 2,
        fill: '#FFFFFF', // 主色
        stroke: 'rgba(0, 0, 0, 0.15)', // 边框
        lineWidth: 1,
        cursor: 'pointer',
        transform: `translate(0, 40)`,
      },
    });

    // ✅ 按钮文字：Ant Design 字体大小 + 白色
    const buttonText = document.createElement('text', {
      style: {
        text: '详情',
        fill: '#000',
        fontSize: 14,
        textAlign: 'center',
        textBaseline: 'middle',
        cursor: 'pointer',
        transform: `translate(0, 40)`,
      },
    });

    // ✅ 点击事件
    group.addEventListener('click', () => {
      handleFollowUp(datum);
    });

    group.appendChild(rect);
    group.appendChild(buttonText);
    group.appendChild(label);

    return group;
  }, []);

  // 当不显示催办按钮时处理坐标点
  const textFormatter = (datum) => {
    const textLabel = data.find((d) => d.displayId === datum)?.displayName || datum;
    return textLabel;
  };

  const config = useMemo(() => {
    return {
      data,
      height,
      width,
      xField,
      yField,
      axis: {
        x: {
          size: remindFlag ? 60 : 30,
          // 当有催办按钮时，不使用 transform，在 labelFormatter 中处理
          ...(remindFlag
            ? {}
            : {
                transform: [
                  {
                    type: 'ellipsis',
                    minLength: 45,
                    maxLength: 120,
                    suffix: '...',
                    step: 1,
                  },
                ],
              }),
          labelFormatter: remindFlag ? labelFormatter : textFormatter,
        },
        y: {
          grid: true, // 网格线
          gridStrokeOpacity: 0.5, // 网格线透明度
          labelFormatter: labelFormatterY,
        },
      },
      interaction: {
        tooltip: {
          render: (e, { title, items }) => {
            // 从原始数据源获取displayName
            const displayNameObj = data?.find((d) => d.displayId === title);
            return (
              <div key={title}>
                <h4>{displayNameObj?.displayName}</h4>
                {items.map((item) => {
                  const { name, value } = item;
                  // 从原始数据源获取originalDesc
                  const currentData = data?.find((d) => d.displayColor === name);
                  return (
                    <div style={{ minWidth: 240 }}>
                      <div
                        style={{
                          margin: 0,
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>
                          <span>
                            {currentData?.originalDesc || name}：{value}
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            );
          },
        },
      },
      // ✅ onReady：可以监听筛选事件
      onReady: (plot) => {
        chartRef.current = plot;
      },
      ...customConfig,
    };
  }, [data, labelFormatter]); // 依赖 data 和 labelFormatter

  // 无数据
  if (!data.length) {
    return (
      <div style={containerStyle}>
        <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }

  // 用户点击“继续催办”
  const handleContinue = useCallback(() => {
    setModalOpen(false); // 关闭提示弹窗
    setTimeout(() => {
      setDrawerOpen(true); // 打开正式抽屉
    }, 100);
  }, []);

  // 关闭提示弹窗（取消）
  const handleModalCancel = useCallback(() => {
    setModalOpen(false);
    setFollow(null);
  }, []);

  return (
    <>
      <Column {...config} ref={chartRef} />
      {/* ✅ 中间提示弹窗 */}
      <ConfirmSuggestionModal
        open={modalOpen}
        follow={follow}
        onContinue={handleContinue}
        onCancel={handleModalCancel}
      />
      {/* 全局抽屉 */}
      <ExpeditingDrawer
        title={agent ? '服务商诊断详情' : '小二诊断详情'}
        sellerId={agent ? { companyId: currentDatum } : { staffId: currentDatum }}
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
      />
    </>
  );
};

export default ColumnCharts;
