import { Modal, Typography } from 'antd';

const { Paragraph, Text } = Typography;

interface ConfirmSuggestionModalProps {
  open: boolean;
  follow: string;
  onContinue: () => void; // 继续催办
  onCancel: () => void;
}

const ConfirmSuggestionModal: React.FC<ConfirmSuggestionModalProps> = ({
  open,
  follow,
  onContinue,
  onCancel,
}) => {
  return (
    <Modal
      title="提示"
      open={open}
      okText="继续催办"
      cancelText="取消"
      onOk={onContinue}
      onCancel={onCancel}
      centered
      width={520}
    >
      <Typography>
        <Paragraph style={{ padding: '12px', background: '#fffbe6', borderRadius: 4 }}>
          {follow?.suggest || '暂无建议信息'}
        </Paragraph>
      </Typography>
    </Modal>
  );
};

export default ConfirmSuggestionModal;
