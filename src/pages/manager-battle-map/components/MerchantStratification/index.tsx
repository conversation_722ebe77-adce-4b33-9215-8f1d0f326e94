/**
 * 商家分层
 */

import { Tabs } from 'antd';
import React, { useState, useEffect } from 'react';
import { CYCLE_LIST } from '../../constant';
import NewStage from './components/newStage';
import BarrierStage from './components/barrierStage';
import GrowthStage from './components/growthStage';
import MatureStage from './components/matureStage';

import styles from './index.module.less';

const MerchantStratification = () => {
  const [activeKey, setActiveKey] = useState<string>('newbie');

  // 渲染当前激活的组件内容
  function renderCurrentTabContent() {
    switch (activeKey) {
      case 'newbie':
        return <NewStage key="newbie" />;
      case 'bottleneck':
        return <BarrierStage key="bottleneck" />;
      case 'develop':
        return <GrowthStage key="develop" />;
      case 'mature':
        return <MatureStage key="mature" />;
      default:
        return null;
    }
  }

  // 根据 CYCLE_LIST 生成 Tabs 的 items
  const tabItems = CYCLE_LIST.map((item) => ({
    key: item.key,
    label: item.title,
    children: item.key === activeKey ? renderCurrentTabContent() : null,
  }));

  // 切换 Tab 时的处理函数
  const handleTabChange = (key: string) => {
    setActiveKey(key);
  };

  return (
    <div>
      <Tabs
        activeKey={activeKey}
        onChange={handleTabChange}
        items={tabItems}
        className={styles.merchantTabs}
      />
    </div>
  );
};

export default MerchantStratification;
