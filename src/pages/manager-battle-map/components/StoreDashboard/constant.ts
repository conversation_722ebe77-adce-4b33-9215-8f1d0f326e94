import {
  MergedCardData,
  DashboardDataProps,
  StageShopBizData,
} from '@/pages/manager-battle-map/types';

export const MATURE_DATA_TYPE_OBJ = {
  DAY_ON_DAY: '日环比',
  WEEK_ON_WEEK: '周环比',
  MONTH_ON_MONTH: '月环比',
};
// 卡片配置映射表
export const CARD_CONFIGS = [
  {
    phase: '新手期',
    cardTitle: '新手期-门店',
    chartTitle: '新手期门店停留时长分布',
    chartDesc: '门店停留在新手期时长（签约时间-至今）',
    type: 'newbieShopBizData',
  },
  {
    phase: '瓶颈期',
    cardTitle: '瓶颈期-门店',
    chartTitle: '瓶颈期-日均凭证量增长分布',
    chartDesc: '瓶颈期门店，日均凭证量近7天变化情况',
    type: 'bottleneckShopBizData',
  },
  {
    phase: '成长期',
    cardTitle: '成长期-门店',
    chartTitle: '成长期-日均凭证量增长分布',
    chartDesc: '成长期门店，日均凭证量近7天变化情况',
    type: 'developShopBizData',
  },
  {
    phase: '成熟期',
    cardTitle: '成熟期-门店',
    chartTitle: '店均ARPU增长分布',
    chartDesc: '成熟期门店的店均ARPU，在近7天数据变化情况',
    type: 'matureShopBizData',
  },
];

// 默认值常量
const DEFAULTS = {
  tips: '',
  diagnosticResult: '暂无诊断信息',
  shopNum: 0,
  shopNumCycleRatio: 0,
  shopNumCycleRatioDesc: '--',
  shopNumRatio: 0,
  shopNumRatioDesc: '--',
  shopNumRatioCycleRatio: 0,
  shopNumRatioCycleRatioDesc: '--',
  chartsData: [] as const,
};

export function mergeCardData(data: DashboardDataProps): MergedCardData[] {
  return CARD_CONFIGS.map((config) => {
    const stageData = data ? (data[config.type] as Partial<StageShopBizData>) : null;

    return {
      ...config,

      // 合并数据，优先使用真实值，否则使用默认值
      tips: stageData?.layerQuestionMarkTips ?? DEFAULTS.tips,
      diagnosticResult: stageData?.diagnosticResult ?? DEFAULTS.diagnosticResult,
      shopNum: stageData?.shopNum ?? DEFAULTS.shopNum,
      shopNumCycleRatio: stageData?.shopNumCycleRatio ?? DEFAULTS.shopNumCycleRatio,
      shopNumCycleRatioDesc: stageData?.shopNumCycleRatioDesc ?? DEFAULTS.shopNumCycleRatioDesc,
      shopNumRatio: stageData?.shopNumRatio ?? DEFAULTS.shopNumRatio,
      shopNumRatioDesc: stageData?.shopNumRatioDesc ?? DEFAULTS.shopNumRatioDesc,
      shopNumRatioCycleRatio: stageData?.shopNumRatioCycleRatio ?? DEFAULTS.shopNumRatioCycleRatio,
      shopNumRatioCycleRatioDesc:
        stageData?.shopNumRatioCycleRatioDesc ?? DEFAULTS.shopNumRatioCycleRatioDesc,
      chartsData: stageData?.shopBizDataDistributions || DEFAULTS.chartsData,
    };
  });
}

export function isZeroValue(value) {
  // 如果是 null、undefined 或非字符串/数字类型，直接返回 false
  if (value == null) return false;

  // 转换为字符串以便统一处理
  const str = String(value).trim();

  // 使用正则提取数值部分（匹配开头的数字，包括小数）
  const match = str.match(/^(-?\d+(?:\.\d+)?)/);

  if (!match) {
    return false; // 没有匹配到数字
  }

  const num = parseFloat(match[1]);
  return Math.abs(num) < 1e-9; // 判断是否接近 0（避免浮点误差）
}
