import { Bar } from '@ant-design/plots';
import React from 'react';
import { Empty } from 'antd';

interface BarChartProps {
  /**
   * 直接传入数据数组
   */
  data: string | Array<Record<string, any>>;

  /**
   * X 轴字段名
   * @default 'x'
   */
  xField?: string;

  /**
   * Y 轴字段名
   * @default 'y'
   */
  yField?: string;

  /**
   * 图表高度（影响加载/空状态容器高度）
   * @default 200
   */
  height?: number;

  /**
   * 自定义配置，会覆盖默认配置
   */
  customConfig?: any;
}

const BarChart: React.FC<BarChartProps> = ({
  data,
  xField = 'x',
  yField = 'y',
  height = 200,
  customConfig = {},
}) => {
  // 公共容器样式：居中内容，固定高度
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: `${height}px`,
    padding: '16px',
    boxSizing: 'border-box',
  };

  // 无数据
  if (!data.length) {
    return (
      <div style={containerStyle}>
        <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }

  // 图表配置
  const config = {
    data,
    xField,
    yField,
    height,
    paddingRight: 35,
    ...customConfig,
    interaction: {
      tooltip: {
        render: (e, { title, items }) => {
          return (
            <div key={title}>
              <h4>{title}</h4>
              {items.map((item) => {
                const { name, value } = item;
                // 从原始数据源获取label
                const currentData = data?.find((d) => d.color === name);
                return (
                  <div>
                    <div
                      style={{
                        margin: 0,
                        display: 'flex',
                        justifyContent: 'space-between',
                      }}
                    >
                      <div>
                        <span>
                          {currentData?.label || name}：{value}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          );
        },
      },
    },
  };

  return <Bar {...config} />;
};

export default BarChart;
