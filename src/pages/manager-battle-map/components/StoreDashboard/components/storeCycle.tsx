import React, { useMemo, useState, useRef, useEffect } from 'react';
import CycleCard from './cycleCard';
import { Col, Row } from 'antd';
import { mergeCardData } from '../constant';
import { DashboardDataProps } from '@/pages/manager-battle-map/types';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { TASK_TABPAN_ENUM, STORE_TIER_OPTIONS } from '@/common/const';
import { PageSPMKey, ModuleSPMKey, traceClick } from '@/utils/trace';

import styles from '../index.module.less';

interface StoreCycleProps {
  data: DashboardDataProps;
  form: any;
  onClick: ({ phase: string }) => void;
}
const StoreCycle = (props: StoreCycleProps) => {
  const { data = {}, form, onClick } = props;
  const cardList = useMemo(() => mergeCardData(data), [data]);
  const [selectedPhase, setSelectedPhase] = useState<string | null>(null);

  const lastClickTimeRef = useRef<number>(0);
  const lastClickPhaseRef = useRef<string | null>(null);
  const clickTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (clickTimerRef.current) {
        clearTimeout(clickTimerRef.current);
      }
    };
  }, []);

  // 处理卡片点击事件
  const handleCardClick = (card: any) => {
    const currentTime = Date.now();
    const timeDiff = currentTime - lastClickTimeRef.current;
    const isSameCard = lastClickPhaseRef.current === card.phase;

    // 清除
    if (clickTimerRef.current) {
      clearTimeout(clickTimerRef.current);
      clickTimerRef.current = null;
    }

    // 双击200ms 内点击同一个卡片
    if (timeDiff < 200 && isSameCard) {
      // 取消联动
      setSelectedPhase(null);
      lastClickTimeRef.current = 0;
      lastClickPhaseRef.current = null;

      // 清空筛选
      emitter.emit(EmitterEventMap.TaskDataClick, {
        type: TASK_TABPAN_ENUM.MERCHANT,
        params: {
          merchantStages: [],
        },
      });
      return;
    }

    // 单击逻辑
    lastClickTimeRef.current = currentTime;
    lastClickPhaseRef.current = card.phase;

    //  200ms双击则会被清除
    clickTimerRef.current = setTimeout(() => {
      // D 区埋点：门店仪表盘.门店周期点击
      traceClick(PageSPMKey.首页, ModuleSPMKey['门店仪表盘.门店周期点击'], {
        phase: card.phase,
      });

      if (onClick) {
        setSelectedPhase(card.phase);
        onClick(card);
      } else {
        setSelectedPhase(null);
      }

      // 触发商户阶段筛选
      const stageOption = STORE_TIER_OPTIONS.find((option) => option.title === card.phase);
      if (stageOption) {
        emitter.emit(EmitterEventMap.TaskDataClick, {
          type: TASK_TABPAN_ENUM.MERCHANT,
          params: {
            merchantStages: [stageOption.value],
          },
        });
      }
    }, 200);
  };
  return (
    <Row gutter={36} className={styles.storeCycle}>
      {(cardList || [])?.map((card) => (
        <Col
          className={selectedPhase === card.phase ? styles.selectedCard : ''}
          span={6}
          key={card.phase}
          onClick={() => handleCardClick(card)}
        >
          <CycleCard onClick={onClick} list={card} form={form} />
        </Col>
      ))}
    </Row>
  );
};

export default StoreCycle;
