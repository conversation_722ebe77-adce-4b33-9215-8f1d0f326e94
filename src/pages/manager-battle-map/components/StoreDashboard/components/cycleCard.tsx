import React from 'react';
import { Tooltip, Space, Progress, Divider } from 'antd';
import { QuestionCircleOutlined, RiseOutlined, FallOutlined } from '@ant-design/icons';
import { StatisticCard } from '@ant-design/pro-components';
import { isNegative } from '@/pages/manager-battle-map/constant';
import BarCharts from './barCharts';
import { isZeroValue, MATURE_DATA_TYPE_OBJ } from '../constant';

import styles from '../index.module.less';

const { Statistic } = StatisticCard;

const CycleCard = (props) => {
  const { list = {}, form, onClick } = props;

  const {
    cardTitle = '', // 卡片标题
    chartTitle = '', // 图表标题
    chartDesc = '', // 图表描述
    tips = '', // 小问号文案
    shopNum = 0, // 门店数量
    shopNumCycleRatioDesc = 0, // 门店数量环比
    shopNumRatio = 0, // 门店数量占比
    shopNumRatioCycleRatioDesc = 0, // 门店数量占比环比
    chartsData = [],
  } = list || {};
  // 上升
  const riseStyle = { color: '#F5222D', lineHeight: '20px' };
  const riseIcon = <RiseOutlined style={{ color: '#F5222D' }} />;
  // 下降
  const declineStyle = { color: '#52C41A' };
  const declineIcon = <FallOutlined style={{ color: '#52C41A', lineHeight: '20px' }} />;

  const titleRender = ({ title = '', desc = '' }) => {
    return (
      <div className={styles.cycleCardTitle}>
        <span>{title}</span>
        <Tooltip title={desc}>
          <QuestionCircleOutlined />
        </Tooltip>
      </div>
    );
  };

  const getPrefixIcon = (value: string | number) => {
    if (value === '--' || isZeroValue(value)) return null;
    return isNegative(value) ? declineIcon : riseIcon;
  };

  const getValueStyle = (value: string | number) => {
    if (value === '--' || isZeroValue(value)) return null;
    return isNegative(value) ? declineStyle : riseStyle;
  };

  const descriptionRender = () => {
    return (
      <>
        <Progress percent={shopNumRatio} showInfo={false} />
        <Statistic
          title={
            <span className={styles.comparison}>
              {MATURE_DATA_TYPE_OBJ[form.getFieldValue('cycleRatioType')]}
            </span>
          }
          prefix={getPrefixIcon(shopNumRatioCycleRatioDesc)}
          value={shopNumRatioCycleRatioDesc}
          valueStyle={getValueStyle(shopNumRatioCycleRatioDesc)}
        />
        <Divider style={{ margin: '8px 0' }} />
        <div className={styles.declineCard}>
          <StatisticCard
            statistic={{
              value: shopNum,
              suffix: <span className={styles.statisticCardSuffix}>个</span>,
              description: (
                <Statistic
                  title={
                    <span className={styles.comparison}>
                      {MATURE_DATA_TYPE_OBJ[form.getFieldValue('cycleRatioType')]}
                    </span>
                  }
                  prefix={getPrefixIcon(shopNumCycleRatioDesc)}
                  value={shopNumCycleRatioDesc}
                  valueStyle={getValueStyle(shopNumCycleRatioDesc)}
                />
              ),
            }}
          />
        </div>
        <Divider style={{ margin: '8px 0' }} />
      </>
    );
  };

  return (
    <div className={styles.cycleCard}>
      <StatisticCard
        hoverable={!!onClick}
        bordered
        title={<Space>{titleRender({ title: cardTitle, desc: tips })}</Space>}
        statistic={{
          value: shopNumRatio,
          suffix: <span className={styles.statisticCardSuffix}>%</span>,
          description: descriptionRender(),
        }}
        chart={
          <>
            <div className={styles.chartTitle}>
              {titleRender({ title: chartTitle, desc: chartDesc })}
            </div>
            <BarCharts
              data={chartsData}
              xField="label"
              yField="value"
              height={200}
              customConfig={{
                legend: false, // 隐藏图例
                style: {
                  maxWidth: 20, // 柱子宽度
                  fill: (datum) => datum.color,
                },
                scale: {
                  y: {
                    // domainMax: 1, //  坐标终点
                    domainMin: 0, // 坐标起点
                    tickCount: 4, //  柱子数量
                  },
                  // color: {
                  //   type: 'ordinal',
                  //   domain: ['14天以上', '4-14天', '0-3天'], // 明确分类顺序
                  //   range: ['#F53F3F', '#FAAD14', '#52C41A'],
                  // },
                },
                sort: false,
                // ✅ 2. 设置颜色
                colorField: 'color',
                label: {
                  formatter: (datum) => {
                    // 确保 value 是数字
                    const value = parseFloat(datum);
                    return isNaN(value) ? '' : `${parseFloat(value?.toFixed(2))}%`;
                  },
                  textAlign: 'left',
                },
                axis: {
                  x: {
                    line: true,
                  },
                  y: {
                    grid: true,
                    gridStrokeOpacity: 0.5,
                    gridFilter: (datum, index, data) => {
                      return datum.id !== '0';
                    },
                    labelFormatter: (val) => {
                      const num = parseFloat(val);
                      return isNaN(num) ? '' : `${num}%`;
                    },
                  },
                },
                tooltip: (d) => {
                  return {
                    name: d?.letter,
                    value: `${parseFloat(d?.value?.toFixed(2))}%`,
                  };
                },
              }}
            />
          </>
        }
        style={{ minWidth: 257 }}
        headStyle={{ padding: '8px 8px 0' }}
        bodyStyle={{ padding: 8 }}
      />
    </div>
  );
};

export default CycleCard;
