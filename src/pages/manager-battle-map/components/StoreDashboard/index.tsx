/**
 * 门店仪表盘
 */

import { Card, Select, Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { CONTRAST_PERIOD_OPTIONS } from '@/pages/manager-battle-map/constant';
import { queryMerchantShopDashboard } from '@/services/manager-battle-map';
import SuggestionCard from '../suggestionCard';
import StoreCycle from './components/storeCycle';
import { useStore } from '@/context/global-store';
import { PageSPMKey, ModuleSPMKey, traceExp, traceClick } from '@/utils/trace';

import styles from './index.module.less';

interface StoreDashboardProps {
  pageSource: string; // 页面来源(小二跟管理)
  entityType: string; // 实体类型(小二跟管理)
  filterOptions: any; // 筛选条件
}
const StoreDashboard = ({
  pageSource = 'MANAGER_PAGE',
  entityType = null,
  onClick,
  filterOptions = {},
}: StoreDashboardProps) => {
  const { viewer } = useStore() || {};
  const [form] = Form.useForm();
  const [data, setData] = useState({});
  const { run: onMerchantShopDashboard, loading: dashboardLoading } = useRequest(
    queryMerchantShopDashboard,
    {
      manual: true,
      onSuccess: (res) => {
        setData(res || {});
        return res || {};
      },
      onError: (err) => {
        setData({});
      },
    },
  );

  // C 区埋点：门店仪表盘曝光
  useEffect(() => {
    traceExp(PageSPMKey.首页, ModuleSPMKey['门店仪表盘'], {});
  }, []);

  useEffect(() => {
    if (!entityType) {
      return;
    }

    const values = form.getFieldsValue();
    const params = {
      ...values,
      ...filterOptions,
      pageSource,
      viewOperatorId: viewer || undefined,
      ...(entityType && { entityType }),
    };
    onMerchantShopDashboard({ params });
  }, [JSON.stringify(filterOptions), viewer]);

  // 环比
  const onValuesChange = (changedValues: any) => {
    // D 区埋点：门店仪表盘.环比选择
    if (changedValues.cycleRatioType) {
      traceClick(PageSPMKey.首页, ModuleSPMKey['门店仪表盘.环比选择'], {
        cycleRatioType: changedValues.cycleRatioType,
      });
    }
    const values = form.getFieldsValue();
    const params = {
      ...values,
      ...filterOptions,
      pageSource,
      viewOperatorId: viewer || undefined,
      ...(entityType && { entityType }),
    };
    onMerchantShopDashboard({ params });
  };

  const extraRender = () => {
    return (
      <Form onValuesChange={onValuesChange} form={form} layout="inline">
        <Form.Item
          name="cycleRatioType"
          label=""
          initialValue="DAY_ON_DAY"
          style={{ marginRight: '8px' }}
        >
          <Select style={{ width: 120 }} options={CONTRAST_PERIOD_OPTIONS} />
        </Form.Item>
        <Form.Item style={{ marginRight: 0 }}>
          <span className={styles.extraText}>数据来源于昨天</span>
        </Form.Item>
      </Form>
    );
  };

  return (
    <Card
      loading={dashboardLoading}
      title="门店仪表盘"
      extra={extraRender()}
      bodyStyle={{ padding: '16px 24px' }}
    >
      {/* 诊断建议 */}
      <SuggestionCard suggest={data?.diagnosticTips || '暂无诊断数据'} />
      {/* 门店周期 */}
      <StoreCycle onClick={onClick} data={data || {}} form={form} />
    </Card>
  );
};

export default StoreDashboard;
