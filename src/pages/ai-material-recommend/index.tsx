import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Form,
  Button,
  Modal,
  Tabs,
  Table,
  Spin,
  message,
  Flex,
  TableColumnType,
  Image,
  Radio,
  Tag,
} from 'antd';
import { useRequest, useAntdTable } from 'ahooks';
import { useQuery } from '@/hooks/useQuery';
import MerchantSelectV2 from '@/components/merchant-select-v2';
import ShopSelectV2 from '@/components/shop-select-v2';
import {
  queryTabInfo,
  queryDraftList,
  batchPublish,
  batchUpdateStatus,
} from '@/services/ai-material';
import {
  IDraftItem,
  StatusMap,
  type IMerchantItem,
  type IShopItem,
  type ITabInfoVO,
} from '@/types/ai-material/merchant';
import { ModuleSPMKey, PageSPMKey, trace, traceClick, traceExp } from '@/utils/trace';
import SendQwMsgModal from './components/SendQwMsgModal';
import dayjs from 'dayjs';
import { IconFontQi<PERSON>ei } from '@/components/icons';
import { jumpOtherUrl } from '@/common/utils';
import { ModuleType } from './components/constants';
import { isEqual } from 'radash';
import { definePageConfig } from 'ice';

const AiMaterialRecommend: React.FC = () => {
  const [form] = Form.useForm();
  const [query, , clearQuery] = useQuery();
  const [merchant, setMerchant] = useState<IMerchantItem | undefined>();
  const [shop, setShop] = useState<IShopItem | undefined>();
  const [showQwModal, setShowQwModal] = useState(false);
  const [activeTab, setActiveTab] = useState<ModuleType | undefined>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [albumSubTab, setAlbumSubTab] = useState<string>('全部');
  const [tableFilters, setTableFilters] = useState<Record<string, React.Key[] | null>>({
    status: null,
    name: null,
  });
  const shopId = shop?.shopId;

  // 从 URL 参数中获取初始值
  const { pid: urlPid, shopId: urlShopId, moduleType: urlModuleType } = query;

  // tab 区数据
  const { data: tabInfo, loading: tabLoading } = useRequest(
    () => (shopId ? queryTabInfo({ shopId }) : Promise.resolve(undefined)),
    {
      refreshDeps: [shopId],
      onSuccess: (res) => {
        let selectedModuleType: string | undefined;

        if (urlModuleType) {
          const findedTab = res?.tabList?.find((tab) => tab.moduleType === urlModuleType);
          if (findedTab) {
            selectedModuleType = findedTab.moduleType;
            setActiveTab(selectedModuleType);
          }
        } else {
          selectedModuleType = res?.tabList?.[0]?.moduleType;
          setActiveTab(selectedModuleType);
        }

        // 检查当前 tab 是否有 subAlbumTypeList，如果有则设置第一个元素，如果没有则设置为空字符串
        if (selectedModuleType) {
          const currentTabInfo = res?.tabList?.find((tab) => tab.moduleType === selectedModuleType);
          if (currentTabInfo?.extInfo?.subAlbumTypeList?.length > 0) {
            setAlbumSubTab(currentTabInfo.extInfo.subAlbumTypeList[0]);
          } else {
            setAlbumSubTab('');
          }
        } else {
          setAlbumSubTab('');
        }
      },
      onBefore: () => {
        setActiveTab(undefined);
      },
    },
  );
  const tabList: ITabInfoVO[] = tabInfo?.tabList || [];

  const { tableProps, refresh } = useAntdTable(
    async ({ current, pageSize }) => {
      const { status, name } = tableFilters || {};
      if (activeTab && shopId) {
        const params: any = {
          shopId,
          moduleType: activeTab,
          status: status?.length ? status.join(',') : undefined,
          page: {
            pageNo: current,
            pageSize,
          },
        };
        const extInfo: any = {};

        if (name?.length) {
          name.forEach((key) => {
            extInfo[key] = 'true';
          });
        }
        if (activeTab === ModuleType['商家相册']) {
          if (albumSubTab && albumSubTab !== '全部') {
            extInfo.subAlbumType = albumSubTab;
          }
        }
        if (Object.keys(extInfo).length > 0) {
          params.extInfo = extInfo;
        }
        const result = await queryDraftList(params);
        return {
          list: result.dataList || [],
          total: result.pageInfo?.totalCount || 0,
        };
      }
      return { list: [], total: 0 };
    },
    {
      refreshDeps: [shopId, activeTab, albumSubTab, tableFilters],
      defaultPageSize: 20,
    },
  );

  // 表单变更处理
  const handleMerchantChange = (m: IMerchantItem | undefined) => {
    setMerchant(m);
    if (urlPid && m?.pid === urlPid) {
      return;
    }
    clearQuery();
    // 改变商户时重置门店
    setShop(undefined);
    form.setFieldsValue({ shop: undefined });
    // D 区埋点：AI 素材推荐.商户选择
    traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.商户选择'], {
      merchantPid: m?.pid,
    });
    trace('ai-material-recommend-merchant-change', { merchantPid: m?.pid });
  };

  const handleShopChange = (s: IShopItem | undefined) => {
    setShop(s);
    if (urlShopId && s?.shopId === urlShopId) {
      return;
    }
    form.setFieldsValue({ shop: s?.shopId });
    clearQuery();
    // D 区埋点：AI 素材推荐.门店选择
    traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.门店选择'], {
      shopId: s?.shopId,
      shopName: s?.shopName,
    });
    trace('ai-material-recommend-shop-change', { shopId: s?.shopId, name: s?.shopName });
  };

  // 商户选择器自动选择回调
  const handleMerchantAutoSelect = (m: IMerchantItem) => {
    setMerchant(m);
    form.setFieldsValue({ merchant: m });
  };

  // 是否展示企微群按钮
  const showQwBtn = merchant?.optGroupCanReach && shopId && merchant?.pid;

  // 是否为手艺人 tab
  const isPersonTab = activeTab === ModuleType['手艺人'];

  // 判断是否为相册 tab
  const isAlbumTab = activeTab === ModuleType['商家相册'];

  // 判断是否为商家封面 tab
  const isCoverTab = activeTab === ModuleType['商家封面'];

  // 表格字段
  const columns: Array<TableColumnType<IDraftItem>> = [
    {
      title: '推荐素材',
      dataIndex: 'name',
      key: 'name',
      ...(isAlbumTab
        ? {
            filters: [
              { text: '橱窗图', value: 'isMain' },
              { text: '不显示重复图', value: 'filterDuplicate' },
            ],
            filteredValue: tableFilters.name || null,
          }
        : {}),
      render: (name: string, record: IDraftItem) => (
        <div>
          <Flex gap={10}>
            {/* 左侧图片 */}
            {record.image && (
              <Image
                src={record.image}
                alt="素材图片"
                width={80}
                height={60}
                style={{ borderRadius: 4, objectFit: 'cover' }}
              />
            )}
            <div style={{ flex: 1 }}>
              {name && <div style={{ fontSize: 14, fontWeight: 500, marginBottom: 4 }}>{name}</div>}
              {record.extInfo?.isMain === 'true' && <Tag color="red">橱窗图</Tag>}
            </div>
          </Flex>
          {/* 显示提醒信息 */}
          {record.tips && <div style={{ color: '#ff7875', fontSize: 12 }}>{record.tips}</div>}
          {/* 显示重复标识 */}
          {record.extInfo?.duplicateTag === 'true' && (
            <div style={{ color: '#ff4d4f', fontSize: 12 }}>疑似线上已有或审核中，注意使用</div>
          )}
        </div>
      ),
    },
    {
      title: '推荐时间',
      dataIndex: 'createTime',
      key: 'createTime',
      render: (createTime) => {
        if (createTime) {
          return dayjs(createTime).format('YYYY-MM-DD HH:mm:ss');
        }
        return '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: Object.entries(StatusMap).map(([key, value]) => ({ text: value, value: key })),
      filteredValue: tableFilters.status || null,
      render: (v: string) => StatusMap[v] || '-',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (_: any, record: IDraftItem) => (
        <Button
          type="link"
          onClick={() => {
            handleBatchSubmit([record.draftId]);
            traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.去提交'], {
              shopId,
              moduleType: activeTab,
              draftId: record.draftId,
            });
          }}
          loading={submittingIdsRef.current.has(record.draftId)}
          // disabled={isPersonTab}
        >
          去提交
        </Button>
      ),
    },
  ];

  // 多选配置
  const rowSelection =
    isPersonTab || isCoverTab
      ? undefined
      : {
          selectedRowKeys,
          onChange: setSelectedRowKeys,
        };

  const isRecommendDishTab = activeTab === ModuleType['推荐菜'];

  const submittingIdsRef = useRef<Set<string>>(new Set());

  const { run: runSubmit } = useRequest(
    async (params: { shopId: string; moduleType: string; draftIdList: string[] }) => {
      if (isPersonTab || isRecommendDishTab) {
        return batchUpdateStatus(params);
      } else {
        return batchPublish(params);
      }
    },
    {
      manual: true,
      onBefore: (params) => {
        const draftIdList = params?.[0]?.draftIdList || [];
        draftIdList.forEach((id) => submittingIdsRef.current.add(id));
      },
      onSuccess: (_, params) => {
        submittingIdsRef.current.clear();
        setSelectedRowKeys([]);
        refresh();
        message.success('提交成功');
        const { draftIdList } = params[0];
        const pid = merchant?.pid || '';
        const allTableData = tableProps.dataSource || [];
        const firstDraftId = draftIdList[0];
        const record = allTableData.find((item) => item.draftId === firstDraftId);
        const atagId = record?.extInfo?.aTagId || '';
        const brandCode = record?.extInfo?.brandCode || '';
        if (isRecommendDishTab) {
          // 推荐菜支持批量，跳转到 batchUpload，且去掉 operationType 参数
          const urlParams = [
            `draftIdList=${draftIdList.join(',')}`,
            `source=aiSupply`,
            `shopId=${shopId}`,
            `atagId=${atagId}`,
            `brandCode=${brandCode}`,
            `pid=${pid}`,
            `channel=KBSERVCENTER`,
          ];
          const url = `kb-pc/mp-recommend-dish/batchUpload?${urlParams.join('&')}`;
          jumpOtherUrl({ url });
        } else if (isPersonTab) {
          // 手艺人只允许单个
          const url = `kb-pc/mp-moda/pc/edit?moduleTypeEnum=STYLIST&draftIdList=${firstDraftId}&operationType=edit&source=aiSupply&shopId=${shopId}&atagId=${atagId}&brandCode=${brandCode}&modelNameSpace=content&pid=${pid}&channel=KBSERVCENTER`;
          jumpOtherUrl({ url });
        }
      },
      onFinally: () => {
        submittingIdsRef.current.clear();
      },
    },
  );

  const handleBatchSubmit = (draftIdList?: string[]) => {
    const ids = draftIdList || (selectedRowKeys as string[]);
    trace('ai-material-recommend-batch-submit', {
      shopId,
      moduleType: activeTab,
      draftIds: ids,
    });
    // 只有商家封面和商家相册需要二次确认
    if ([ModuleType['商家封面'], ModuleType['商家相册']].includes(activeTab)) {
      Modal.confirm({
        title: '信息确认',
        content: (
          <div style={{ padding: 12 }}>
            发布 {ids.length} 张图片至 {shop?.shopName} 门店，确认发布后则进入图片审核，
            <br />
            审核通过会在高德 C 端对应门店展示该图，是否确认发布？
          </div>
        ),
        onOk: () => {
          runSubmit({
            shopId,
            moduleType: activeTab,
            draftIdList: ids,
          });
          traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.提交确认'], {
            shopId,
            moduleType: activeTab,
          });
        },
        okButtonProps: { loading: ids.some((id) => submittingIdsRef.current.has(id)) },
      });
    } else {
      runSubmit({
        shopId,
        moduleType: activeTab,
        draftIdList: ids,
      });
    }
  };
  // C 区埋点：AI素材推荐.模块曝光（Tab切换时）
  useEffect(() => {
    if (activeTab) {
      traceExp(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.模块曝光'], {
        moduleType: activeTab,
        ...(shopId ? { shopId } : {}),
      });
    }
  }, [activeTab]);
  // C 区埋点：AI素材推荐页面 PV（页面浏览量）
  useEffect(() => {
    traceExp(PageSPMKey.首页, ModuleSPMKey['AI素材推荐'], {
      shopId,
    });
  }, []);
  return (
    <Card>
      <Form form={form}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
          <Form.Item
            name="merchant"
            label="商户"
            style={{ marginBottom: 0 }}
            labelCol={{ style: { width: 60 } }}
          >
            <MerchantSelectV2
              style={{ width: 260 }}
              value={merchant}
              onChange={(m) => {
                handleMerchantChange(m);
              }}
              autoSelectByPid={shop?.pid}
              onAutoSelect={handleMerchantAutoSelect}
              defaultValue={urlPid}
            />
          </Form.Item>
          {showQwBtn && (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '5px 10px',
                cursor: 'pointer',
                marginLeft: 8,
                color: '#4096ff',
              }}
              onClick={() => {
                setShowQwModal(true);
                // D 区埋点：AI 素材推荐.企微消息按钮
                traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.企微消息按钮'], {
                  pid: merchant?.pid,
                  shopId,
                });
                trace('ai-material-recommend-qw-btn-click', {
                  pid: merchant?.pid,
                  shopId,
                });
              }}
            >
              <IconFontQiWei style={{ fontSize: 17, marginRight: 8 }} />
              <span>信息不全？点击发送企微消息收集材料</span>
            </div>
          )}
        </div>
        <Form.Item
          name="shop"
          label="门店"
          rules={[{ required: true, message: '请选择门店' }]}
          style={{ marginTop: 16 }}
          labelCol={{ style: { width: 60 } }}
        >
          <ShopSelectV2
            value={shop}
            style={{ width: 260 }}
            onChange={(s) => {
              handleShopChange(s);
            }}
            pid={merchant?.pid}
            defaultValue={urlShopId}
          />
        </Form.Item>
      </Form>
      {/* 动态 tab 区 */}
      <Spin spinning={tabLoading}>
        <Tabs
          activeKey={activeTab}
          onChange={(key) => {
            setActiveTab(key as ModuleType);
            setTableFilters({});

            // 检查当前 tab 是否有 subAlbumTypeList，如果有则设置第一个元素，如果没有则设置为空字符串
            const currentTabInfo = tabList.find((tab) => tab.moduleType === key);
            if (currentTabInfo?.extInfo?.subAlbumTypeList?.length > 0) {
              setAlbumSubTab(currentTabInfo.extInfo.subAlbumTypeList[0]);
            } else {
              setAlbumSubTab('');
            }

            trace('ai-material-recommend-tab-change', { tab: key, shopId });
          }}
          items={tabList.map((tab) => ({
            key: tab.moduleType,
            label: `${tab.moduleName}（${tab.draftCount}）`,
          }))}
          style={{ marginTop: 16 }}
        />
      </Spin>
      {/* 相册 tab 二级 tab和标签筛选 */}
      {isAlbumTab && (
        <Radio.Group
          value={albumSubTab}
          onChange={(e) => {
            setAlbumSubTab(e.target.value);
            setTableFilters({});
            // D 区埋点：AI 素材推荐.相册二级 Tab 切换
            traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.相册二级Tab切换'], {
              subTab: e.target.value,
              shopId,
            });
            trace('ai-material-recommend-album-subtab-change', {
              subTab: e.target.value,
              shopId,
            });
          }}
          buttonStyle="solid"
          options={(
            tabList.find((tab) => tab.moduleType === ModuleType['商家相册'])?.extInfo
              ?.subAlbumTypeList || ['全部']
          ).map((subType) => ({ label: subType, value: subType }))}
          optionType="button"
        />
      )}
      {/* 批量操作区 */}
      {!isPersonTab && !isCoverTab && (
        <div style={{ margin: '16px 0' }}>
          <Button
            type="primary"
            disabled={!selectedRowKeys.length || (isPersonTab && selectedRowKeys.length > 1)}
            onClick={() => {
              // D 区埋点：AI 素材推荐.批量提交按钮
              traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.批量提交按钮'], {
                shopId,
                moduleType: activeTab,
                draftIds: selectedRowKeys,
              });
              handleBatchSubmit();
              trace('ai-material-recommend-batch-submit', {
                shopId,
                moduleType: activeTab,
                draftIds: selectedRowKeys,
              });
            }}
          >
            批量提交
          </Button>
        </div>
      )}
      {/* 表格区 */}
      <Table
        rowKey="draftId"
        columns={columns}
        {...tableProps}
        onChange={(pagination, filters, sorter) => {
          const prevFilters = tableFilters || {};
          const prevPagination = tableProps.pagination || {};

          // D 区埋点：状态筛选
          if (filters.status && !isEqual(filters.status, prevFilters.status)) {
            traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.状态筛选'], {
              status: filters.status,
              shopId,
              moduleType: activeTab,
            });
          }

          // D 区埋点：名称筛选
          if (filters.name && !isEqual(filters.name, prevFilters.name)) {
            traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.名称筛选'], {
              name: filters.name,
              shopId,
              moduleType: activeTab,
            });
          }

          // D 区埋点：分页切换
          if (
            pagination.current !== prevPagination.current ||
            pagination.pageSize !== prevPagination.pageSize
          ) {
            traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.分页切换'], {
              current: pagination.current,
              pageSize: pagination.pageSize,
              shopId,
              moduleType: activeTab,
            });
          }

          if (!isEqual(filters, tableFilters)) {
            setTableFilters(filters as Record<string, React.Key[] | null>);
          }
          // 始终调用 tableProps.onChange 以触发分页更新
          tableProps.onChange(pagination, filters, sorter);
        }}
        onRow={(record) => ({
          onClick: () => {
            // D 区埋点：AI 素材推荐.表格行点击
            traceClick(PageSPMKey.首页, ModuleSPMKey['AI素材推荐.表格行点击'], {
              draftId: record.draftId,
              shopId,
              moduleType: activeTab,
            });
          },
        })}
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
        }}
        style={{ marginTop: 16 }}
        rowSelection={rowSelection}
      />
      {/* 企微群弹窗 */}
      <SendQwMsgModal
        visible={showQwModal}
        pid={merchant?.pid || ''}
        shopId={shopId}
        merchantName={merchant?.merchantName || ''}
        onCancel={() => setShowQwModal(false)}
        onOk={() => {
          setShowQwModal(false);
        }}
        options={tabList}
        groupName={merchant?.optGroupName}
      />
    </Card>
  );
};

export default AiMaterialRecommend;

export const pageConfig = definePageConfig({
  spm: {
    spmB: 'xy-task-pc-home',
  },
});
