// src/pages/index/index.tsx
import { Flex } from 'antd';
import React, { useState, useMemo, useEffect } from 'react';
import DoubleTable from './components/double-table';
import TargetDashboard from './components/target-dashboard';
import TodoList from './components/todo-list';
import SwitchViewer from './components/switch-viewer';
import TimedTaskModule from './components/timed-task-module';
import DashboardCards from './components/dashboard-cards';
import WorkbenchSwitcher from './components/workbench-switcher';
import InfraOverviewCards from './components/infra-overview-cards';
import { IfButtonShow } from '@/components/server-controller/useAction';
import { ActionButtonType } from '@/constants';
import { CoreTaskCompletion } from '@/components/completion-rate-card/core-task';
import Performance from '../manager-battle-map/components/Performance';
import { EntityType } from '@/types';
import { definePageConfig } from 'ice';
import StoreDashboard from '@/pages/manager-battle-map/components/StoreDashboard';
import { useRequest } from 'ahooks';
import { configBusinessNewsGrey } from '@/services';
import PageTitle from '@/components/page-title';
import { WORKBENCH_TYPE } from '@/common/const';
import { useStore } from '@/context/global-store';
import { isBucLogin } from '@/utils';
import { setCommonParams } from '@alife/amap-tracker';

export default function Index() {
  // 初始状态设为undefined，等待权限接口返回后再设置
  const [workbenchType, setWorkbenchType] = useState<WORKBENCH_TYPE | undefined>(undefined);
  const { viewer, setViewer } = useStore() || {};

  // 页面挂载时清空切换视角
  useEffect(() => {
    if (viewer) {
      setViewer(undefined);
    }
  }, []);

  // 查询灰度开关 - 切换视角时需要重新调用，传入viewOperatorId
  const { data: greyData, loading: greyLoading } = useRequest(
    () =>
      configBusinessNewsGrey(
        ['TASK_PRIORITY_V2', 'MERCHANT_SALES_FUNNEL', 'INFRASTRUCTURE_WORKBENCH_QUERY'],
        viewer ? { viewOperatorId: viewer } : undefined,
      ),
    {
      refreshDeps: [viewer],
    },
  );

  const isTaskPriorityV2 =
    greyData?.find((item) => item.scene === 'TASK_PRIORITY_V2')?.switchStatus || false;
  const isMerchantSalesFunnel =
    greyData?.find((item) => item.scene === 'MERCHANT_SALES_FUNNEL')?.switchStatus || false;
  const isInfrastructureWorkbenchQuery =
    greyData?.find((item) => item.scene === 'INFRASTRUCTURE_WORKBENCH_QUERY')?.switchStatus ||
    false;

  const isInfraWorkbench = workbenchType === WORKBENCH_TYPE.INFRA;
  // 当启用工作台切换功能时，需要等待workbenchType确定后再渲染工作台相关内容，避免闪现
  const isWorkbenchReady = !isInfrastructureWorkbenchQuery || workbenchType !== undefined;
  // 判断是否显示运维工作台内容：workbenchType已确定且不是基建工作台
  const showOpsWorkbench = useMemo(
    () => isWorkbenchReady && !isInfraWorkbench,
    [isWorkbenchReady, isInfraWorkbench],
  );

  useEffect(() => {
    if (workbenchType) {
      setCommonParams({
        workbenchType,
      });
    }
  }, [workbenchType]);

  return (
    <Flex gap={12} style={{ padding: 12, background: '#f5f5f5' }} vertical>
      {/*  切换视角 */}
      <SwitchViewer />

      {/* 工作台切换 - 需要灰度控制 */}
      {isInfrastructureWorkbenchQuery && (
        <WorkbenchSwitcher value={workbenchType} onChange={setWorkbenchType} />
      )}

      {/* 页面标题 - 等待workbenchType确定后再显示 */}
      {isTaskPriorityV2 && isWorkbenchReady ? (
        <PageTitle title={isInfraWorkbench ? '基建工作台' : '小二运维工作台'} />
      ) : null}

      {/* 运维工作台展示: 根据灰度开关和工作台类型控制显示 */}
      {!isTaskPriorityV2 && <CoreTaskCompletion />}

      {/* 运维工作台：命中TASK_PRIORITY_V2灰度且不是基建工作台时显示 */}
      {isTaskPriorityV2 && showOpsWorkbench ? <DashboardCards /> : null}

      {/* 基建工作台：数据概览卡片 */}
      {isTaskPriorityV2 && isInfraWorkbench && isInfrastructureWorkbenchQuery ? (
        <InfraOverviewCards />
      ) : null}

      {/*  命中灰度时显示在柱状图区块上面 - 基建工作台时隐藏 */}
      {isTaskPriorityV2 && showOpsWorkbench ? (
        <TodoList taskPriorityV2Enabled={isTaskPriorityV2} isGreyLoading={greyLoading} />
      ) : null}

      {/* 基建工作台时隐藏绩效目标和柱状图区块 */}
      {showOpsWorkbench ? (
        <div>
          {/*  绩效目标 */}
          {isTaskPriorityV2 && (
            <Performance
              pageSource="AGENT_PAGE"
              showChart={false}
              filterOptions={{ entityType: EntityType.STAFF }}
              mergedWithNext
            />
          )}
          <IfButtonShow buttonType={ActionButtonType.柱状图区块}>
            <TargetDashboard mergedWithPrev={isTaskPriorityV2 && isBucLogin()} />
          </IfButtonShow>
        </div>
      ) : null}

      {/* 没命中灰度时显示在柱状图区块下面 */}
      {!isTaskPriorityV2 ? (
        <TodoList taskPriorityV2Enabled={isTaskPriorityV2} isGreyLoading={greyLoading} />
      ) : null}

      {/*  门店仪表盘 - 基建工作台时隐藏 */}
      {isTaskPriorityV2 && showOpsWorkbench ? (
        <StoreDashboard pageSource="AGENT_PAGE" entityType="STAFF" filterOptions={{}} />
      ) : null}

      {/*  企微自动发送限时任务 - 基建工作台时隐藏 */}
      {showOpsWorkbench ? <TimedTaskModule /> : null}

      {/*  商户列表 */}
      <DoubleTable
        taskPriorityV2Enabled={isTaskPriorityV2}
        workbenchType={isInfrastructureWorkbenchQuery ? workbenchType : undefined}
        isMerchantSalesFunnel={isMerchantSalesFunnel}
        isInfrastructureWorkbenchQuery={isInfrastructureWorkbenchQuery}
      />
    </Flex>
  );
}

export const pageConfig = definePageConfig({
  spm: {
    spmB: 'xy-task-pc-home',
  },
});
