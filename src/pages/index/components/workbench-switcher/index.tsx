import React, { useEffect, useState } from 'react';
import { Select, Form } from 'antd';
import { useMount, useRequest } from 'ahooks';
import { queryAdminPermission } from '@/services';
import { WORKBENCH_TYPE } from '@/common/const';
import { traceClick, traceExp, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { useStore } from '@/context/global-store';

interface WorkbenchSwitcherProps {
  value?: WORKBENCH_TYPE;
  onChange?: (value: WORKBENCH_TYPE) => void;
}

const WorkbenchSwitcher: React.FC<WorkbenchSwitcherProps> = ({ value, onChange }) => {
  const [workbenchType, setWorkbenchType] = useState<WORKBENCH_TYPE | undefined>(undefined);
  const [canSwitch, setCanSwitch] = useState(true);
  const { viewer } = useStore() || {};

  // 查询权限 - 当 viewer 变化时，使用 viewOperatorId 重新获取权限
  const { data: permissionData, loading } = useRequest(
    () => queryAdminPermission(viewer ? { viewOperatorId: viewer } : undefined),
    {
      refreshDeps: [viewer],
    },
  );

  useEffect(() => {
    if (permissionData) {
      const { infrastructureOperation, comprehensiveOperation } = permissionData;
      // 仅基建运维角色（infrastructureOperation为true且comprehensiveOperation为false）：默认展示基建工作台，且不可切换
      if (infrastructureOperation === true && comprehensiveOperation === false) {
        // eslint-disable-next-line no-console
        const infraType = WORKBENCH_TYPE.INFRA;
        setWorkbenchType(infraType);
        setCanSwitch(false);
        // 通知父组件初始工作台类型
        onChange?.(infraType);
      } else {
        // 其他角色：默认展示运维工作台，可切换
        // eslint-disable-next-line no-console
        const opsType = WORKBENCH_TYPE.OPS;
        setWorkbenchType(opsType);
        setCanSwitch(true);
        // 通知父组件初始工作台类型
        onChange?.(opsType);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [permissionData]);

  useEffect(() => {
    if (value !== undefined) {
      setWorkbenchType(value);
    }
  }, [value]);

  // C 区埋点：工作台切换模块曝光
  useMount(() => {
    traceExp(PageSPMKey.首页, ModuleSPMKey['工作台切换'], {});
  });

  const handleChange = (newValue: WORKBENCH_TYPE) => {
    setWorkbenchType(newValue);

    // 埋点
    traceClick(PageSPMKey.首页, ModuleSPMKey['工作台切换'], {
      workbenchType: newValue,
    });

    // 发送工作台切换事件，清理相关组件的状态
    emitter.emit(EmitterEventMap.WorkbenchSwitch, {
      workbenchType: newValue,
    });

    onChange?.(newValue);
  };

  // 如果数据还在加载中，不显示组件
  if (loading || workbenchType === undefined) {
    return null;
  }

  // 如果不可切换，不显示切换组件，但已经通过useEffect通知了父组件
  if (!canSwitch) {
    return null;
  }

  return (
    <Form.Item label="切换工作台" style={{ marginLeft: 12 }}>
      <Select
        value={workbenchType}
        onChange={handleChange}
        style={{ width: 200 }}
        options={[
          { label: '运维工作台', value: WORKBENCH_TYPE.OPS },
          { label: '基建工作台', value: WORKBENCH_TYPE.INFRA },
        ]}
      />
    </Form.Item>
  );
};

export default WorkbenchSwitcher;
