import React, { useEffect } from 'react';
import { Flex, Card } from 'antd';
import { useRequest } from 'ahooks';
import { generateOperationDiagnosisSuggestion } from '@/services';
import DiagnosticSuggestion from '../diagnostic-suggestion';
import MerchantTasks from '../merchant-tasks';
import { useStore } from '@/context/global-store';
import { PageSPMKey, ModuleSPMKey, traceExp } from '@/utils/trace';

export default function DashboardCards() {
  const { viewer } = useStore() || {};
  const { data: suggestionData, loading } = useRequest(
    async () => {
      const result = await generateOperationDiagnosisSuggestion({
        viewOperatorId: viewer || undefined,
      });
      return result;
    },
    {
      refreshDeps: [viewer],
    },
  );

  // C 区埋点：仪表盘卡片曝光
  useEffect(() => {
    traceExp(PageSPMKey.首页, ModuleSPMKey['仪表盘卡片'], {});
  }, []);

  return (
    <Card
      style={{
        background: '#f4f8ff',
        borderRadius: 8,
        border: 'none',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      }}
    >
      <Flex gap={16} style={{ width: '100%', background: '#f4f8ff' }}>
        <div style={{ flex: 1 }}>
          <DiagnosticSuggestion data={suggestionData} loading={loading} />
        </div>
        <div style={{ flex: 1 }}>
          <MerchantTasks data={suggestionData} loading={loading} />
        </div>
      </Flex>
    </Card>
  );
}
