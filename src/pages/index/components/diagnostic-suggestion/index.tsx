import React, { useEffect, useState } from 'react';
import { Card, Spin } from 'antd';

// 样式常量定义
const cardTitleStyle = {
  boxSizing: 'border-box' as const,
  border: '1px solid',
  borderImage: 'linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%)',
  backdropFilter: 'blur(10px)',
  width: 176,
  height: 56,
  textAlign: 'center' as const,
  padding: '8px 16px',
  backgroundImage:
    'url("https://img.alicdn.com/imgextra/i3/O1CN01fLFkoB1bqNeqgMHAq_!!6000000003516-55-tps-176-56.svg")',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'center right',
  backgroundSize: 'auto 100%',
  marginLeft: -4,
};

const cardTitleTextStyle = {
  fontFamily: 'PingFang SC',
  fontSize: 16,
  fontWeight: 500,
  lineHeight: '24px',
  display: 'flex',
  alignItems: 'center',
  letterSpacing: 'normal',
  background: 'linear-gradient(90deg, #0078fe 0%, #00d1e8 100%)',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  textFillColor: 'transparent',
};

const contentAreaStyle = {
  position: 'relative' as const,
  zIndex: 1,
  marginTop: -15,
  background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.7) 100%)',
  boxSizing: 'border-box' as const,
  border: '1px solid',
  borderImage: 'linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%)',
  backdropFilter: 'blur(10px)',
  fontSize: 14,
  padding: '16px 20px 16px 16px',
  lineHeight: 1.8,
  height: 170,
  wordWrap: 'break-word' as const,
  overflowWrap: 'break-word' as const,
  display: 'flex',
  alignItems: 'flex-start',
  justifyContent: 'flex-start',
  textAlign: 'left' as const,
};

const paragraphStyle = {
  marginBottom: 12,
};

const titleContainerStyle = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
};

export default function DiagnosticSuggestion({ data, loading }) {
  const [diagnosisSuggestion, setDiagnosisSuggestion] = useState('暂无诊断建议，请稍后再试');

  useEffect(() => {
    if (data) {
      const { diagnosisSuggestion: suggestion } = data;

      if (suggestion) {
        setDiagnosisSuggestion(suggestion);
      }
    }
  }, [data]);

  const paragraphs = diagnosisSuggestion.split('\n').filter((paragraph) => paragraph.trim());

  return (
    <Card
      style={{
        background: '#f4f8ff',
        borderRadius: 2,
        border: 'none',
      }}
      bodyStyle={{ padding: 0 }}
    >
      <div style={titleContainerStyle}>
        <div style={cardTitleStyle}>
          <div style={cardTitleTextStyle}>
            <img
              src="https://img.alicdn.com/imgextra/i4/O1CN01ieqykX1z2VIrXmCh4_!!6000000006656-55-tps-23-24.svg"
              alt=""
              style={{ paddingRight: 4 }}
            />
            运维诊断建议
          </div>
        </div>
      </div>

      <div style={contentAreaStyle}>
        <Spin spinning={loading}>
          {diagnosisSuggestion === '暂无诊断建议，请稍后再试' ? (
            <div style={{ color: 'rgb(153, 153, 153)', textAlign: 'left' }}>
              {diagnosisSuggestion}
            </div>
          ) : (
            <div style={{ textAlign: 'left', width: '100%' }}>
              {paragraphs.map((paragraph, index) => (
                <div
                  key={`paragraph-${paragraph.slice(0, 20)}`}
                  style={{
                    ...paragraphStyle,
                    marginBottom: index === paragraphs.length - 1 ? 0 : paragraphStyle.marginBottom,
                    textAlign: 'left',
                  }}
                >
                  {paragraph}
                </div>
              ))}
            </div>
          )}
        </Spin>
      </div>
    </Card>
  );
}
