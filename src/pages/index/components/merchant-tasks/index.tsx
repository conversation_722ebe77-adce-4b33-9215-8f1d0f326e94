import React, { useEffect, useRef, useState } from 'react';
import { Card, Typography, Spin } from 'antd';
import * as echarts from 'echarts';

const { Text } = Typography;

// 样式常量定义
const cardTitleStyle = {
  boxSizing: 'border-box' as const,
  border: '1px solid',
  borderImage: 'linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%)',
  backdropFilter: 'blur(10px)',
  width: 138,
  height: 56,
  textAlign: 'center' as const,
  padding: '8px 16px',
  backgroundImage:
    'url("https://img.alicdn.com/imgextra/i1/O1CN01K5K5cM1WmBxH3kRsl_!!6000000002830-55-tps-138-56.svg")',
  backgroundRepeat: 'no-repeat',
  backgroundPosition: 'center right',
  backgroundSize: 'auto 100%',
  marginLeft: -4,
};

const cardTitleTextStyle = {
  justifyContent: 'center',
  fontFamily: 'PingFang SC',
  fontSize: 16,
  fontWeight: 500,
  lineHeight: '24px',
  display: 'flex',
  alignItems: 'center',
  letterSpacing: 'normal',
  background: 'linear-gradient(90deg, #0078fe 0%, #00d1e8 100%)',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  textFillColor: 'transparent',
};

const contentAreaStyle = {
  position: 'relative' as const,
  zIndex: 1,
  marginTop: -15,
  background: 'linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.7) 100%)',
  boxSizing: 'border-box' as const,
  border: '1px solid',
  borderImage: 'linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%)',
  backdropFilter: 'blur(10px)',
  fontSize: 14,
  padding: '8px 28px 8px 16px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: 20,
  height: 170,
};

const chartContainerStyle = {
  width: 120,
  height: 120,
  position: 'relative' as const,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};

const legendContainerStyle = {
  flex: 1,
  display: 'grid',
  gridTemplateColumns: 'repeat(2, 1fr)',
  gap: 8,
};

const legendItemStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: 8,
  fontSize: 12,
};

const colorDotStyle = (color: string) => ({
  width: 8,
  height: 8,
  borderRadius: '50%',
  backgroundColor: color,
});

const introTextStyle = {
  flex: 1,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: 14,
  color: '#333',
  textAlign: 'center' as const,
  padding: '0 20px',
  fontWeight: 500,
};

export default function MerchantTasks({ data, loading }) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<echarts.ECharts | null>(null);
  const [chartData, setChartData] = useState<
    Array<{ name: string; value: number; itemStyle: { color: string } }>
  >([]);
  const [strategySuggestion, setStrategySuggestion] = useState('暂无任务数据，请稍后再试');

  const getStageColor = (stageCode: string) => {
    const colorMap: Record<string, string> = {
      NEWBIE: '#0078fe', // 新手期 - 蓝色
      DEVELOP: '#00d1e8', // 成长期 - 青色
      BOTTLENECK: '#ffa940', // 瓶颈期 - 黄色
      MATURE: '#722ed1', // 成熟期 - 紫色
    };
    return colorMap[stageCode];
  };

  useEffect(() => {
    if (data) {
      const { pieChartData: chartDataInfo, strategySuggestion: suggestion } = data;

      if (suggestion) {
        setStrategySuggestion(suggestion);
      }

      if (chartDataInfo && Array.isArray(chartDataInfo)) {
        const processedData = chartDataInfo.map((item) => ({
          name: item.stageName,
          value: parseFloat(item.percentage.replace('%', '')),
          itemStyle: { color: getStageColor(item.stageCode) },
          stageCode: item.stageCode,
        }));

        setChartData(processedData);
      }
    }
  }, [data]);

  // 初始化图表
  useEffect(() => {
    if (chartRef.current && !chartInstanceRef.current && chartData.length > 0) {
      const chart = echarts.init(chartRef.current);
      chartInstanceRef.current = chart;
    }

    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose();
        chartInstanceRef.current = null;
      }
    };
  }, [chartData]);

  // 更新图表数据
  useEffect(() => {
    if (chartInstanceRef.current && chartData.length > 0) {
      const option = {
        series: [
          {
            type: 'pie',
            radius: '70%',
            center: ['50%', '50%'],
            data: chartData,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
              },
            },
          },
        ],
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            const { data: itemData } = params;
            return `${itemData.name}: ${itemData.value}%`;
          },
        },
      };

      chartInstanceRef.current.setOption(option);
    }
  }, [chartData]);

  return (
    <Card
      style={{
        background: '#f4f8ff',
        borderRadius: 2,
        border: 'none',
      }}
      bodyStyle={{ padding: 0 }}
    >
      <div style={cardTitleStyle}>
        <div style={cardTitleTextStyle}>商家任务</div>
      </div>

      <div style={contentAreaStyle}>
        {strategySuggestion === '暂无任务数据，请稍后再试' ? (
          <div style={{ textAlign: 'center', color: 'rgb(153, 153, 153)' }}>
            {strategySuggestion}
          </div>
        ) : (
          <>
            <div style={introTextStyle}>{strategySuggestion}</div>
            <div style={chartContainerStyle}>
              <Spin spinning={loading} size="small">
                <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
              </Spin>
            </div>

            <div style={legendContainerStyle}>
              {chartData.map((item) => (
                <div key={item.name} style={legendItemStyle}>
                  <div style={colorDotStyle(item.itemStyle.color)} />
                  <Text style={{ fontSize: '12px' }}>
                    {item.name}: {item.value}%
                  </Text>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </Card>
  );
}
