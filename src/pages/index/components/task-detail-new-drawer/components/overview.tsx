import { Card, List } from 'antd';
import React from 'react';

import styles from './index.module.less';

const Overview = ({ title = '', extra = '', data = [], dataList = [] }) => {
  const LayerList = () => {
    return (
      <List
        grid={{ gutter: 16, column: 4, xs: 1, sm: 2, md: 2, lg: 4 }}
        dataSource={dataList}
        style={{ padding: '0 16px' }}
        renderItem={(item) => (
          <List.Item key={item.layerCode}>
            <div className={styles.layer_name}>{item.layerName}</div>
            <div className={styles.layer_item} style={{ padding: '5px 0' }}>
              门店 {item.shopCount}
            </div>
          </List.Item>
        )}
      />
    );
  };
  return (
    <Card
      title={
        <div className={styles.drawer_card_title}>
          {title} <span>{extra}</span>
        </div>
      }
      variant="borderless"
      headStyle={{ padding: 0, border: 'none', display: 'block', minHeight: 17, marginBottom: 9 }}
      bodyStyle={{ padding: '0' }}
      className={styles.drawer_card_header}
      style={{ boxShadow: 'none', background: 'none' }}
    >
      <div>
        {data &&
          data?.length > 0 &&
          data?.map((item) => {
            return <div>{item?.value}</div>;
          })}
      </div>

      {dataList && dataList?.length > 0 ? (
        <LayerList />
      ) : (
        <div className={styles.empty_state}>
          <div className={styles.empty_text}>暂无数据</div>
        </div>
      )}
    </Card>
  );
};

export default Overview;
