import React, { useEffect, useState } from 'react';
import { Pagination, List, Typography, Tooltip } from 'antd';
import { getMerchantTaskList } from '@/services';
import { ModuleSPMKey, PageSPMKey, traceExp } from '@/utils/trace';
import styles from './index.module.less';

const { Text } = Typography;

const pageSize = 5; // 固定每页5条数据

const StoreList: React.FC<{
  merchantId: string;
  taskStatus: string;
  taskType: string;
  pid: string;
  taskNo: string;
}> = ({ merchantId, taskStatus, taskType, pid, taskNo }) => {
  const [current, setCurrent] = useState(1);
  const [pageInfo, setPageInfo] = useState({ total: 0 });
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);

  const fetchStoreList = async (page: number) => {
    setLoading(true);
    const res = await getMerchantTaskList({
      merchantId,
      taskType,
      taskStatus,
      page: {
        pageNo: page,
        pageSize,
      },
    });
    setLoading(false);
    setData(res.dataList);
    setPageInfo({
      total: res?.pageInfo?.totalCount,
    });

    // 门店列表曝光埋点：数据加载成功后，触发门店列表曝光埋点
    if (res?.dataList?.length > 0 && pid && taskNo) {
      traceExp(PageSPMKey.首页, ModuleSPMKey['新商户详情.门店列表曝光'], {
        pid,
        taskNo,
      });
    }
  };

  useEffect(() => {
    fetchStoreList(current);
  }, [current]);

  // 分页改变时的处理
  const handlePageChange = (page: number) => {
    setCurrent(page);
  };

  // 文本截断处理函数
  const renderAnalysisText = (text: string) => {
    const maxLength = 100;
    if (!text) return '';

    const fullText = `：${text}`;
    if (text.length > maxLength) {
      const truncatedText = `：${text.slice(0, maxLength)}...`;
      return (
        <Tooltip title={text} placement="top">
          <span>{truncatedText}</span>
        </Tooltip>
      );
    }
    return fullText;
  };

  return (
    <div className={styles.storeList}>
      <List
        loading={loading}
        dataSource={data || []}
        renderItem={(item) => (
          <div className={styles.storeItem}>
            <Text type="secondary">
              {item.shopName}
              {item.recommendShopAnalysis && renderAnalysisText(item.recommendShopAnalysis)}
            </Text>
          </div>
        )}
      />

      <div>
        <Pagination
          current={current}
          total={pageInfo.total || 0}
          pageSize={pageSize}
          onChange={handlePageChange}
          showSizeChanger={false}
          showQuickJumper={false}
          disabled={loading}
          align="end"
          size="small"
        />
      </div>
    </div>
  );
};

export default StoreList;
