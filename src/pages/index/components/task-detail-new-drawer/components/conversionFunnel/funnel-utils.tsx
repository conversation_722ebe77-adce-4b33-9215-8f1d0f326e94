import React from 'react';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { formatCount } from '@/utils';

export interface ShopItem {
  shopId: string;
  shopName: string;
  [key: string]: any;
}

export interface FunnelDataItem {
  title: string;
  pv?: string;
  uv?: string;
  value?: string;
  color: string;
  tip: string | React.ReactNode;
}

export interface ConversionRateItem {
  rate: string;
  label: string;
}

export interface FunnelInfo {
  funnelData: FunnelDataItem[];
  conversionRates: ConversionRateItem[];
}

export type FilterChangeParams =
  | { type: 'range'; value: [Dayjs, Dayjs] | null }
  | { type: 'shopId'; value: string | undefined };

export const defaultRangeValue: [Dayjs, Dayjs] = [
  dayjs().subtract(1, 'day'),
  dayjs().subtract(1, 'day'),
];

export const defaultConversionRates: ConversionRateItem[] = [
  { rate: '0%', label: '曝光-访问转化' },
  { rate: '0%', label: '访问-点击转化' },
  { rate: '0%', label: '点击-凭证转化' },
];

// 默认漏斗数据（用于占位）
export const defaultFunnelData: FunnelDataItem[] = [
  {
    title: '门店曝光量',
    pv: '0',
    uv: '0',
    color: '#0B49D9',
    tip: '顾客在高德地图看到门店的次数（PV）或人数（UV）',
  },
  {
    title: '门店访问量',
    pv: '0',
    uv: '0',
    color: '#1A66FF',
    tip: '顾客进入门店详情页的次数（PV）或人数（UV）',
  },
  {
    title: '门店点击量',
    pv: '0',
    color: '#4287FF',
    tip: '顾客在门店详情页的所有点击行为次数（PV）',
  },
  {
    title: '凭证量',
    value: '0',
    color: '#6BA6FF',
    tip: (
      <>
        <div>顾客在高德产生凭证量，包括：</div>
        <div>• 在投-美食/休娱：有效凭证到店、计费客资、ocpc客资</div>
        <div>• 在投-医疗/美业/教培：计费客资、OCPC客资、cpc客资、订单量</div>
        <div>• 纯年费：订单量、订座量</div>
      </>
    ),
  },
];

export const defaultFunnelInfo: FunnelInfo = {
  funnelData: defaultFunnelData,
  conversionRates: defaultConversionRates,
};

// 将接口返回的扁平化数据转换为 FunnelInfo 格式
export const transformFunnelData = (data: {
  exposureUv: number;
  exposurePv: number;
  visitUv: number;
  visitPv: number;
  clickPv: number;
  voucherCount: number;
  exposureToVisitConversionRate: string;
  visitToClickConversionRate: string;
  clickToVoucherConversionRate: string;
}): FunnelInfo => {
  // 复用 defaultFunnelData 中的配置（颜色、tip等）
  const exposureItem = defaultFunnelData[0];
  const visitItem = defaultFunnelData[1];
  const clickItem = defaultFunnelData[2];
  const voucherItem = defaultFunnelData[3];

  return {
    funnelData: [
      {
        ...exposureItem,
        pv: formatCount(data.exposurePv),
        uv: formatCount(data.exposureUv),
      },
      {
        ...visitItem,
        pv: formatCount(data.visitPv),
        uv: formatCount(data.visitUv),
      },
      {
        ...clickItem,
        pv: formatCount(data.clickPv),
      },
      {
        ...voucherItem,
        value: formatCount(data.voucherCount),
      },
    ],
    conversionRates: [
      { rate: data.exposureToVisitConversionRate, label: defaultConversionRates[0].label },
      { rate: data.visitToClickConversionRate, label: defaultConversionRates[1].label },
      { rate: data.clickToVoucherConversionRate, label: defaultConversionRates[2].label },
    ],
  };
};
