.conversion_funnel {
  width: 462px;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
  border-radius: 1px;
  background: rgba(255, 255, 255, 0.6);

  .title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.85);
  }

  .funnelContainer {
    display: flex;
    align-items: flex-start;
    gap: 24px;
  }

  // 左侧漏斗区域
  .funnelLeft {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    max-width: 300px;
  }

  .funnelItem {
    position: relative;
    padding: 7px 14px;
    width: 300px;
    box-sizing: border-box;
    height: 64px;
    margin-top: 1px;
  }

  .funnelTriangle {
    position: absolute;
    right: -1px;
    top: -1px;
    bottom: -1px;
    width: 148px;
    background: #fff;
    clip-path: polygon(100% 0, 100% 100%, 0 100%);
    pointer-events: none;
  }

  .funnelContent {
    position: relative;
    z-index: 1;
  }

  .funnelTitle {
    font-size: 14px;
    color: #f5f5f5;
    line-height: 22px;
    margin-bottom: 4px;
  }

  .questionIcon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    font-size: 12px;
    cursor: help;
  }

  .funnelData {
    display: flex;
    gap: 10px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: #fff;
  }

  .arrowRateSection {
    display: flex;
    flex-direction: column;
    gap: 15px;
    min-width: 200px;
    padding-top: 15px;
    position: relative;
    left: -55px;
    z-index: 2;
  }

  .arrowRateItem {
    display: flex;
    align-items: center;
    gap: 18px;
    height: 75px;
  }

  .arrowWrapper {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .rateItem {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
    justify-content: center;
  }

  .rateValue {
    font-size: 24px;
    font-weight: 500;
    line-height: 32px;
    color: rgba(0, 0, 0, 0.85);
  }

  .rateLabel {
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.45);
  }
}
