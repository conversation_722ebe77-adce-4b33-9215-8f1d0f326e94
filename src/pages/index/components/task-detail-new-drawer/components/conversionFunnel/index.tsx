import { useEffect, useState } from 'react';
import Funnel<PERSON>hart from './Funnel';
import { DatePicker, Select, Flex, Button, Spin } from 'antd';
import styles from './index.module.less';
import dayjs from 'dayjs';
import { useRequest } from 'ahooks';
import { getConversionFunnelShopList, getConversionFunnelData } from '@/services';
import type { Dayjs } from 'dayjs';
import {
  type FunnelInfo,
  type FilterChangeParams,
  defaultRangeValue,
  defaultFunnelInfo,
  transformFunnelData,
} from './funnel-utils';

const { RangePicker } = DatePicker;

const ConversionFunnel = ({ pid }: { pid: string }) => {
  const [range, setRange] = useState<[Dayjs, Dayjs]>(defaultRangeValue);
  const [shopId, setShopId] = useState<string | null>(null);
  const [firstShopId, setFirstShopId] = useState<string | undefined>(undefined);
  const [funnelInfo, setFunnelInfo] = useState<FunnelInfo>(defaultFunnelInfo);
  const [moreLinkInfo, setMoreLinkInfo] = useState<{
    showMoreLink: boolean;
    moreDetailUrl: string;
  }>({
    showMoreLink: false,
    moreDetailUrl: '',
  });

  // 获取转化漏斗数据
  const { loading: funnelDataLoading, run: fetchFunnelData } = useRequest(
    async (data: { range: [Dayjs, Dayjs]; shopId: string | null }) => {
      if (!pid) return null;
      const res = await getConversionFunnelData({
        pid,
        startDate: data.range[0],
        endDate: data.range[1],
        shopId: data.shopId,
      });
      return res;
    },
    {
      manual: true,
      onSuccess: (data) => {
        if (data) {
          setFunnelInfo(transformFunnelData(data));
          setMoreLinkInfo({
            showMoreLink: data.showMoreLink,
            moreDetailUrl: data.moreDetailUrl,
          });
        }
      },
    },
  );

  // 获取门店列表
  const {
    data: shopListData,
    loading: shopListLoading,
    run: fetchShopList,
  } = useRequest(
    async (keyword: string | null) => {
      if (!pid) return [];
      let list = await getConversionFunnelShopList({ pid, keyword });
      list = Array.isArray(list) ? list : [];
      if (!keyword) {
        if (list.length > 0 && firstShopId === undefined) {
          setFirstShopId(list[0].shopId);
        }
        return [{ shopId: null, shopName: '全部门店' }, ...list];
      }
      return list;
    },
    {
      manual: true,
      debounceWait: 500,
    },
  );

  useEffect(() => {
    if (pid) {
      const yesterday = dayjs().subtract(1, 'day');
      const lastWeek = dayjs().subtract(7, 'day');
      const initRange = [lastWeek, yesterday] as [Dayjs, Dayjs];
      const initShopId = null;
      setRange(initRange);
      setShopId(initShopId);
      fetchShopList(initShopId);
      fetchFunnelData({ range: initRange, shopId: initShopId });
    }
  }, [pid]);

  const handleFilterChange = (params: FilterChangeParams) => {
    if (params.type === 'range') {
      setRange(params.value);
      fetchFunnelData({ range: params.value, shopId });
    } else {
      setShopId(params.value);
      fetchFunnelData({ range, shopId: params.value });
    }
  };

  function gotoJYPage() {
    const _shopId = shopId || firstShopId;
    const url = moreLinkInfo?.moreDetailUrl?.replace('{shopId}', _shopId).replace('{pid}', pid);
    window.open(url, '_blank');
  }

  return (
    <Spin spinning={funnelDataLoading}>
      <Flex className={styles.conversion_funnel} vertical>
        <Flex justify="space-between" align="center">
          <span className={styles.title}>转化漏斗</span>
          <RangePicker
            size="small"
            value={range}
            presets={[
              { label: '昨日', value: [dayjs().subtract(1, 'day'), dayjs().subtract(1, 'day')] },
              {
                label: '最近一周',
                value: [dayjs().subtract(7, 'day'), dayjs().subtract(1, 'day')],
              },
              {
                label: '最近一月',
                value: [dayjs().subtract(30, 'day'), dayjs().subtract(1, 'day')],
              },
            ]}
            disabledDate={(current) => {
              const today = dayjs();
              return (
                current.isAfter(today.subtract(1, 'day'), 'day') ||
                current.isBefore(today.subtract(60, 'day'), 'day')
              );
            }}
            onChange={(value) => handleFilterChange({ type: 'range', value })}
          />
        </Flex>
        <Select
          size="small"
          placeholder="请选择店铺"
          value={shopId}
          filterOption={false}
          showSearch
          loading={shopListLoading}
          onSearch={fetchShopList}
          options={(shopListData || []).map((item) => ({
            value: item.shopId,
            label: item.shopName,
          }))}
          onChange={(value) => {
            handleFilterChange({ type: 'shopId', value });
          }}
          notFoundContent={shopListLoading ? <Spin size="small" /> : '暂无数据'}
        />
        <FunnelChart data={funnelInfo.funnelData} conversionRates={funnelInfo.conversionRates} />
        {moreLinkInfo.showMoreLink && firstShopId && (
          <Button type="dashed" onClick={gotoJYPage}>
            查看更多
          </Button>
        )}
      </Flex>
    </Spin>
  );
};

export default ConversionFunnel;
