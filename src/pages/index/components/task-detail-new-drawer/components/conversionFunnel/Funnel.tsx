import React from 'react';
import styles from './index.module.less';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import type { FunnelDataItem, ConversionRateItem } from './funnel-utils';

interface FunnelChartProps {
  data: FunnelDataItem[];
  conversionRates: ConversionRateItem[];
}

const FunnelChart: React.FC<FunnelChartProps> = ({ data, conversionRates }) => {
  return (
    <div className={styles.funnelContainer}>
      <div className={styles.funnelLeft}>
        {data.map((item) => (
          <div
            key={item.title}
            className={styles.funnelItem}
            style={{
              backgroundColor: item.color || '#1890FF',
            }}
          >
            <div className={styles.funnelContent}>
              <div className={styles.funnelTitle}>
                <span style={{ marginRight: 8 }}>{item.title}</span>
                <Tooltip
                  // styles={item.title === '凭证量' ? { body: { width: '320px' } } : undefined}
                  // styles={{ body: { width: '320px' } }}
                  title={item.tip}
                >
                  <span style={{ position: 'relative', top: 1.5 }}>
                    <QuestionCircleOutlined />
                  </span>
                </Tooltip>
              </div>
              <div className={styles.funnelData}>
                {item.pv && <span>PV:{item.pv}</span>}
                {item.uv && <span>UV:{item.uv}</span>}
                {item.value && <span>{item.value}</span>}
              </div>
            </div>
          </div>
        ))}
        <div className={styles.funnelTriangle} />
      </div>

      <div className={styles.arrowRateSection}>
        {conversionRates.map((rate) => (
          <div key={`${rate.rate}-${rate.label}`} className={styles.arrowRateItem}>
            <div className={styles.arrowWrapper}>
              <svg fill="none" width="54" height="76.5" viewBox="0 0 54 76.51531982421875">
                <g>
                  <path
                    d="M0.14689094000000003,72.479347L3.328428,69.297813Q3.3987541,69.227478,3.4906397,69.189423Q3.5825253,69.151367,3.6819818,69.151367Q3.7814379,69.151367,3.8733232,69.189423Q3.9652088,69.227486,4.0355349,69.297813Q4.0703566,69.332626,4.0977160999999995,69.373573Q4.1250753,69.41452,4.1439207,69.460022Q4.1627659999999995,69.505524,4.1723735,69.553825Q4.1819811,69.602127,4.1819811,69.651367Q4.1819808,69.700615,4.1723733,69.748917Q4.1627659999999995,69.797211,4.1439204,69.842712Q4.1250748999999995,69.888206,4.0977154,69.929153Q4.0703559,69.970093,4.0355341,70.004921L1.7071081,72.333344L53,72.333344L53,1L39.708984,1L39.708984,0L54,0L54,73.333344L1.7071081,73.333344L4.0355341,75.661766Q4.0703559,75.696587,4.0977151,75.737534Q4.1250746,75.778473,4.1439202,75.823975Q4.1627657,75.869469,4.1723733,75.91777Q4.1819806,75.966064,4.1819808,76.01532Q4.1819806,76.06456,4.1723735,76.112854Q4.1627659999999995,76.161163,4.1439207,76.206657Q4.1250753,76.252151,4.0977159,76.293106Q4.0703566,76.334045,4.0355349,76.368874Q3.9652088,76.439209,3.8733234,76.477264Q3.7814379,76.515327,3.6819818,76.515327Q3.5825253,76.51532,3.4906397,76.477257Q3.3987541,76.439194,3.328428,76.368874L0.14688163999999998,73.187332L0.14644688,73.186897Q0.11570308000000001,73.156158,0.09072733,73.12056Q0.06575157999999998,73.084976,0.047293660000000015,73.045609Q0.028835710000000014,73.006241,0.017449680000000023,72.964279Q0.006063639999999981,72.922318,0.002091349999999992,72.879021Q-0.0018810000000000215,72.835724,0.0016796000000000033,72.792397Q0.005240199999999973,72.749069,0.01622686000000001,72.707001Q0.027213509999999996,72.664932,0.045296369999999975,72.625389Q0.06337928999999998,72.585846,0.08801562000000002,72.550026Q0.11400926,72.51223,0.14644688,72.47979L0.14689094000000003,72.479347Z"
                    fillRule="evenodd"
                    fill="#000000"
                    fillOpacity="0.25"
                  />
                </g>
              </svg>
            </div>
            <div className={styles.rateItem}>
              <div className={styles.rateValue}>{rate.rate}</div>
              <div className={styles.rateLabel}>{rate.label}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FunnelChart;
