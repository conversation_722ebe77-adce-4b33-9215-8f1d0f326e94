import React, { useEffect, useState } from 'react';
import { Tabs, Select, Card, Button, Tag, Spin, message } from 'antd';
import { IconMerchantStar } from '@/components/icons';
import StoreList from '../storeList';
import styles from './index.module.less';
import { useRequest } from 'ahooks';
import { StoreTasks, AdTasks, AdTasksNeedParams, BUTTON_CODE } from '../../constant';
import { getMerchantTaskDetailList, downloadShopDetails, reinSpection } from '@/services';
import { ModuleSPMKey, PageSPMKey, traceClick, traceExp } from '@/utils/trace';

interface TaskListProps {
  record: any;
  merchantId: string;
  onCloseTaskDetailDrawer?: () => void; // 关闭TaskDetailNewDrawer的回调
  onOpenVisitDrawer?: (data: any) => void; // 打开去拜访drawer的回调
  onSwithShopList?: (data: any) => void; // 切换到门店列表的回调
}

// 枚举值映射
const getRecommendTypeText = (type: string) => {
  const typeMap = {
    SYSTEM: '智能推荐',
    REMIND: '主管催办',
    ARRANGE: '主管下发',
  };
  return typeMap[type] || type;
};

// // 任务状态
// const TASK_STATUS = {
//   PROCESSING: 'PROCESSING',
//   FINISH: 'FINISH',
//   INVALID: 'INVALID',
// };

// // 目标状态
// const TARGET_STATUS = {
//   INCOMPLETE: 'INCOMPLETE',
//   COMPLETED: 'COMPLETED',
// };

const getTaskStatusTypeText = (taskStatus: any, taskSource: any) => {
  let text = '';
  if (taskSource === 'RECOMMEND') {
    return <Tag color="green">待处理</Tag>;
  }
  if (taskStatus === 'PROCESSING') {
    text = '待处理';
  } else if (taskStatus === 'INCOMPLETE') {
    text = '目标未完成';
  } else if (taskStatus === 'COMPLETED') {
    text = '目标已完成';
  }
  if (text) {
    return <Tag color="green">{text}</Tag>;
  }
};

const tabItems = [
  {
    key: 'RECOMMEND',
    label: '推荐任务',
  },
  {
    key: 'ALL',
    label: '全部任务',
  },
];

const selectOptions = [
  { label: '待处理', value: 'PROCESSING' },
  { label: '目标未完成', value: 'INCOMPLETE' },
  { label: '目标已完成', value: 'COMPLETED' },
];

const TaskList: React.FC<TaskListProps> = (props: TaskListProps) => {
  const { merchantId, onCloseTaskDetailDrawer, onOpenVisitDrawer, record } = props;
  const [taskStatus, setTaskStatus] = useState('PROCESSING');
  const [taskSource, setTaskSource] = useState('RECOMMEND');

  const {
    data: taskListInfo = [],
    loading,
    run: getTaskList,
  } = useRequest(
    async (_params: { merchantId: string; taskStatus: string; taskSource: string }) => {
      if (!merchantId) {
        setTaskSource('RECOMMEND');
        setTaskStatus('PROCESSING');
        return [];
      }
      const res = await getMerchantTaskDetailList({
        merchantId: _params.merchantId,
        taskStatus: _params.taskStatus,
        taskSource: _params.taskSource,
      });
      if (res?.dataList?.length > 0) {
        res.dataList.forEach((item) => {
          traceExp(PageSPMKey.首页, ModuleSPMKey['新商户详情.任务曝光'], {
            pid: record.pid,
            taskNo: item.taskNo,
          });
        });
      }
      return res.dataList;
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    const _taskStatus = taskSource === 'RECOMMEND' ? 'PROCESSING' : taskStatus;
    getTaskList({ merchantId, taskStatus: _taskStatus, taskSource });
  }, [taskSource, taskStatus, merchantId]);

  const handleTabChange = (key: string) => setTaskSource(key);

  const handleSelectChange = (value: string) => setTaskStatus(value);

  const handleClick = (item: any, index: number) => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['新商户详情.去完成任务按钮点击'], {
      pid: record.pid,
      taskNo: item.taskNo,
    });

    // 默认跳页面
    const url = item.actions?.[index]?.url;
    if (url) {
      window.open(url);
      return;
    }
    // 跳转到记拜访，把参数传过去
    if (AdTasks.includes(item.taskType)) {
      onCloseTaskDetailDrawer?.();
      const params = { ...record };
      if (AdTasksNeedParams.includes(item.taskType)) {
        params.defaultInputInfo = item.defaultInputInfo;
      }
      record && onOpenVisitDrawer?.(params);
      return;
    }
    // 跳转门店列表，把参数传过去
    if (StoreTasks.includes(item.taskType)) {
      onCloseTaskDetailDrawer?.();
      props.onSwithShopList({ pid: record.pid, processingInfraTaskType: item.taskType });
    }
  };

  const handleFeedbackClick = (item: any) => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['新商户详情.无法完成去反馈按钮点击'], {
      pid: record.pid,
      taskNo: item.taskNo,
    });
    onCloseTaskDetailDrawer?.();
    const params = { ...record, defaultInputInfo: item.defaultInputInfo || {} };
    onOpenVisitDrawer?.(params);
  };

  return (
    <div className={styles.taskListContainer}>
      <div className={styles.headerSection}>
        <Tabs
          activeKey={taskSource}
          onChange={handleTabChange}
          items={tabItems}
          className={styles.customTabs}
        />

        <div
          style={{ display: taskSource === 'ALL' ? 'block' : 'none' }}
          className={styles.selectContainer}
        >
          <Select
            value={taskStatus}
            onChange={handleSelectChange}
            options={selectOptions}
            className={styles.customSelect}
            style={{ minWidth: 120 }}
          />
        </div>
      </div>

      {/* 内容区域 */}
      <Spin spinning={loading}>
        <div className={styles.contentSection}>
          {loading && (
            <div
              style={{
                textAlign: 'center',
                padding: '20px',
                minHeight: 90,
              }}
            />
          )}
          {!loading && taskListInfo.length > 0 ? (
            taskListInfo.map((item: any, index: number) => (
              <TaskItem
                key={item.taskId}
                item={item}
                index={index}
                taskSource={taskSource}
                taskStatus={taskStatus}
                handleClick={handleClick}
                handleFeedbackClick={handleFeedbackClick}
                merchantId={merchantId}
                record={record}
              />
            ))
          ) : (
            <div style={{ padding: '80px 16px', textAlign: 'center' }}>
              <div style={{ fontSize: 14, color: 'rgba(0, 0, 0, 0.45)', lineHeight: '22px' }}>
                {taskSource === 'RECOMMEND'
                  ? '当前商户今日无未完成推荐任务，请通过全部任务进行作业'
                  : '暂无数据'}
              </div>
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
};

const TaskItem = ({
  item,
  index,
  handleClick,
  handleFeedbackClick,
  taskSource,
  taskStatus,
  merchantId,
  record,
}: {
  item: any;
  index: number;
  taskSource: string;
  taskStatus: string;
  handleClick: (item: any, index: number) => void;
  handleFeedbackClick: (item: any) => void;
  merchantId: string;
  record: any;
}) => {
  const { viewShopList } = item; // true 代表有门店列表，false代表没有门店列表
  const [showShopList, setShowShopList] = useState(viewShopList && index === 0); // 默认只有第一个门店列表展开
  const [loaded, setLoaded] = useState(viewShopList && index === 0);
  const [downloading, setDownloading] = useState(false);

  const { loading: reinSpectionLoading, run: doReinSpection } = useRequest(
    async (params: { merchantId: string; taskType: string }) => {
      const res = await reinSpection(params);
      if (res) {
        message.success('您的重新质检已提交成功，完成后会通过钉钉提示您');
      }
      return res;
    },
    {
      manual: true,
    },
  );

  // 下载明细
  const handleDownload = async () => {
    setDownloading(true);
    try {
      await downloadShopDetails({
        merchantId,
        taskType: item.taskType,
        taskStatus: taskSource === 'RECOMMEND' ? 'PROCESSING' : taskStatus,
      });
      message.success('数据下载中，完成后请前往钉钉下载');
    } catch (error: any) {
      console.log(error);
    } finally {
      setDownloading(false);
    }
  };

  const handleReinspectClick = () => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['新商户详情.重新质检按钮点击'], {
      pid: record.pid,
      taskNo: item.taskNo,
    });
    // TODO: 点击文案提示
    doReinSpection({ merchantId: record.pid, taskType: item.taskType });
  };

  const actionButtons = () => {
    if (!item.actions || !Array.isArray(item.actions)) {
      return null;
    }
    const visibleActions = item.actions.filter((action) => action.showButton === true);
    if (visibleActions.length === 0) {
      return null;
    }

    return (
      <div className={styles.actionButtons}>
        {visibleActions.map((action, curIndex) => {
          const { buttonCode, buttonName } = action;

          if (buttonCode === BUTTON_CODE.RE_QUALITY) {
            return (
              <Button
                loading={reinSpectionLoading}
                type="primary"
                onClick={() => handleReinspectClick()}
              >
                {buttonName}
              </Button>
            );
          }

          if (buttonCode === BUTTON_CODE.UN_PROVIDE_FEEDBACK) {
            return <Button onClick={() => handleFeedbackClick(item)}>{buttonName}</Button>;
          }

          return (
            <Button
              type="primary"
              className={styles.completeButton}
              onClick={() => handleClick(item, curIndex)}
            >
              {buttonName}
            </Button>
          );
        })}
      </div>
    );
  };

  return (
    <>
      <Card className={styles.taskCard}>
        {/* 任务头部 */}
        <div className={styles.taskHeader}>
          <div className={styles.taskTitleSection}>
            <span className={styles.taskTitleText}>{item.taskName}</span>
            {item.recommendType && (
              <Tag color="green">{getRecommendTypeText(item.recommendType)}</Tag>
            )}
            {getTaskStatusTypeText(taskStatus, taskSource)}
            {viewShopList && <span className={styles.storeCount}>{item.shopCount}个门店</span>}
          </div>
          {actionButtons()}
        </div>

        {/* 出价建议 */}
        <div className={styles.bidRecommendation}>
          {item.diagnoseAdvice && <div className={styles.bidText}>{item.diagnoseAdvice}</div>}
          {item.completeValue && (
            <div className={styles.benefitText}>
              <span className={styles.benefitIcon}>
                <IconMerchantStar />
              </span>
              {item.completeValue}
            </div>
          )}
        </div>

        {/* 推荐话术 */}
        {item.suggestCommunication && (
          <div className={styles.recommendedScript}>
            <div className={styles.scriptTitle}>推荐话术</div>
            <div className={styles.scriptContent}>{item.suggestCommunication}</div>
          </div>
        )}
        {/* 门店列表 */}
        {viewShopList && (
          <div className={styles.storeList}>
            <div className={styles.storeListHeader}>
              <span
                className={styles.collapseButton}
                onClick={() => {
                  setShowShopList(!showShopList);
                  setLoaded(true);
                }}
              >
                {showShopList ? '收起门店列表' : '展开门店列表'}
              </span>
              <Button
                type="link"
                danger
                onClick={handleDownload}
                loading={downloading}
                style={{ padding: 0 }}
              >
                下载明细
              </Button>
            </div>
            {loaded && (
              <div style={{ display: showShopList ? 'block' : 'none' }}>
                <StoreList
                  merchantId={merchantId}
                  taskType={item.taskType}
                  taskStatus={taskSource === 'RECOMMEND' ? 'PROCESSING' : taskStatus}
                  pid={record.pid}
                  taskNo={item.taskNo}
                />
              </div>
            )}
          </div>
        )}
      </Card>
    </>
  );
};

export default TaskList;
