.taskListContainer {
  background: #fff;
  padding: 16px 24px 24px;

  .headerSection {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .customTabs {
      flex: 1;

      :global(.ant-tabs-nav) {
        margin: 0;
      }

      :global(.ant-tabs-tab) {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        border: none;

        &.ant-tabs-tab-active {
          color: #1890ff;

          .ant-tabs-tab-btn {
            color: #1890ff;
          }
        }
      }

      :global(.ant-tabs-ink-bar) {
        background: #1890ff;
      }

      :global(.ant-tabs-nav::before) {
        border-bottom: 1px solid #f0f0f0;
      }
    }

    .selectContainer {
      position: absolute;
      left: 177px;
    }
  }

  .contentSection {
    min-height: 200px;
  }
}

.taskCard {
  border: 0;
  background: #fafafa;
  margin-top: 16px;

  :global(.ant-card-body) {
    padding: 16px;
  }

  .taskHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
  }

  .taskTitleSection {
    .taskTitleText {
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
      color: #000;
      margin-right: 8px;
    }
    :global(.ant-tag) {
      border-radius: 2px;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      color: #52c41a;
    }
    .storeCount {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

.actionButtons {
  display: flex;
  gap: 16px;
  .completeButton {
    width: 89px;
  }
}

.bidRecommendation {
  margin-bottom: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.bidText {
  margin-bottom: 8px;
  font-size: 12px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.65);
}

.benefitText {
  position: relative;
  display: inline-block;
  padding: 1px 8px;
  font-size: 14px;
  color: #000000e6;
  border-radius: 20px;
  line-height: 24px;
  background: linear-gradient(90deg, #e3f0ff 0%, #eff3ff 48%, #e3f3fe 100%);
  padding-left: 28px;
}

.benefitIcon {
  position: absolute;
  left: 6px;
  top: 0;
  transform: scale(0.7);
}

.recommendedScript {
  margin-bottom: 8px;

  .scriptTitle {
    font-size: 14px;
    font-weight: 500;
    color: #3d3d3d;
    margin-bottom: 2px;
  }

  .scriptContent {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 1.5;
  }
}

.storeList {
  .storeListHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  .storeListTitle {
    font-size: 14px;
    font-weight: 500;
    color: #000;
  }
}

.collapseButton {
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
}

.storeItems {
  margin-bottom: 12px;
  .storeItem {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    padding: 4px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
