// 任务状态枚举
export enum TaskStatus {
  INIT = 'INIT',
  PROCESSING = 'PROCESSING',
}

// 任务基础接口
export interface IBaseTask {
  taskNo: string;
  taskName: string;
  taskDesc: string;
  taskStatus: TaskStatus;
  taskStatusText: string;
  manageLimit: number;
  gmtExpired: number;
  redirectUrl: string;
}

// 优先级最高的任务
export interface IMaxPriorityTask extends IBaseTask {
  recommendSpeech: string;
  recommendAnalysis: string;
  highPotentialValues?: string[];
}

// 任务列表中的任务
export interface IListTask extends IBaseTask {
  recommendAnalysis: string;
  highPotentialValues?: string[];
}

// 分页信息
export interface IPageInfo {
  currentPageNo: number;
  pageSize: number;
  totalCount: number;
  nextPageNo: number;
  totalPage: number;
  hasMore: boolean;
}

// 任务列表
export interface ITaskPageList {
  pageInfo: IPageInfo;
  dataList: IListTask[];
}

// 完整的响应数据结构
export interface ITaskResponse {
  maxPriorityTask: IMaxPriorityTask;
  taskPageList: ITaskPageList;
}
