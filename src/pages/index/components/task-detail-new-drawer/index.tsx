/* eslint-disable no-nested-ternary */
import { useEffect } from 'react';
import { IModalProps } from '@/hooks/useModal';
import { Drawer, Flex, Spin, Card } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { useRequest } from 'ahooks';
import { useAiAnalysis } from './useAiAnlaysis-old';
import { IconMerchantStar } from '@/components/icons';
import Overview from './components/overview';
import ConversionFunnel from './components/conversionFunnel';
import styles from './index.module.less';
import TaskList from './components/taskList/index';
import { getMerchantOverviewInfo } from '@/services';
import { ModuleSPMKey, PageSPMKey, traceExp } from '@/utils/trace';

const TaskSubtitle = styled.div`
  font-size: 14px;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.65);
`;

const CardWrap = styled.div`
  border-radius: 2px;
  .title {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    display: flex;
    align-items: center;
    letter-spacing: normal;
    background: linear-gradient(90deg, #0078fe 0%, #00d1e8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }
`;

export default function TaskDetailDrawer(
  props: IModalProps<{ pid: string }, { isMerchantSalesFunnel: boolean }>,
) {
  const { pid } = props.data;
  const { isMerchantSalesFunnel } = props;
  const { aiSummary } = useAiAnalysis({
    pid,
    bizSource: 'merchant_page',
    visible: props.open,
  });

  // 获取门店概览信息
  const { data: merchantOverviewInfo, loading: merchantOverviewInfoLoading } = useRequest(
    async () => {
      if (!pid) {
        return;
      }
      const res = await getMerchantOverviewInfo({
        merchantId: pid,
      });
      return res;
    },
    {
      ready: props.visible && !!pid, // 只有当 drawer 显示且 pid 有值时才执行
      refreshDeps: [props.visible, pid],
    },
  );

  // 弹窗曝光埋点
  useEffect(() => {
    if (props.open) {
      traceExp(PageSPMKey.首页, ModuleSPMKey['新商户详情'], {});
    }
  }, [props.open]);

  useEffect(() => {
    if (pid) {
      aiSummary.createAiSummary();
    }
  }, [pid]);

  return (
    <Drawer
      {...props}
      title={
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
          }}
        >
          <span>商户任务详情</span>
          <CloseOutlined
            onClick={props.onCancel}
            style={{
              color: '#999',
              cursor: 'pointer',
              fontSize: '16px',
              padding: '4px',
            }}
          />
        </div>
      }
      width={isMerchantSalesFunnel ? 1114 : 931}
      closable={false}
      styles={{
        body: {
          background: '#FAFAFA',
          padding: '16px 24px',
        },
      }}
    >
      <CardWrap
        style={{
          background: '#F4F8FF',
          padding: '16px 20px',
          marginBottom: '10px',
        }}
      >
        <Flex align="center" style={{ marginBottom: 10 }}>
          <IconMerchantStar />
          <div className="title" style={{ marginLeft: 4, marginRight: 8 }}>
            商户分析
          </div>
          <TaskSubtitle>平台为您推荐以下数据，辅助您完成运维任务</TaskSubtitle>
        </Flex>
        <Flex style={{ gap: 10 }}>
          <Flex vertical flex={1}>
            {/* 门店概览 */}
            <div className={styles.data_merchant_overview_container}>
              <Spin spinning={merchantOverviewInfoLoading}>
                <Overview
                  title="门店概览"
                  extra={`当前商户共有${
                    merchantOverviewInfo?.totalTaskCnt ?? 0
                  }个任务需要处理，其中${
                    merchantOverviewInfo?.remindTaskCnt ?? 0
                  }个任务被主管催办需要重点关注`}
                  dataList={merchantOverviewInfo?.layerDistribution}
                />
              </Spin>
            </div>
            {/* 数据概览 */}
            <div className={styles.data_overview_container}>
              <Spin spinning={aiSummary.loading}>
                <div>
                  <Card
                    title={
                      <div className={styles.drawer_card_title}>
                        数据概览
                        <span>平台为您推荐以下数据，辅助您完成运维任务</span>
                      </div>
                    }
                    variant="borderless"
                    style={{ boxShadow: 'none', background: 'none' }}
                    headStyle={{
                      padding: 0,
                      border: 'none',
                      display: 'block',
                      minHeight: 17,
                      marginBottom: 9,
                    }}
                    bodyStyle={{ padding: '0' }}
                    className={styles.drawer_card_header}
                  >
                    <div
                      style={{
                        marginBottom: 16,
                        borderRadius: 4,
                        maxHeight: 173,
                        overflowY: 'auto',
                      }}
                    >
                      {aiSummary.content && aiSummary.enable ? (
                        <div style={{ padding: '0 0 0 16px', lineHeight: '24px', opacity: 0.7 }}>
                          {/* eslint-disable-next-line react/no-danger */}
                          <div dangerouslySetInnerHTML={{ __html: aiSummary.content }} />
                        </div>
                      ) : aiSummary.error ? (
                        <div style={{ textAlign: 'center', marginTop: 7 }}>
                          请求失败, <a onClick={() => aiSummary.createAiSummary()}>点击重试</a>
                        </div>
                      ) : (
                        <div style={{ padding: '40px 16px', textAlign: 'center' }}>
                          <div
                            style={{
                              fontSize: 14,
                              color: 'rgba(0, 0, 0, 0.45)',
                              lineHeight: '22px',
                            }}
                          >
                            暂无数据
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                </div>
              </Spin>
            </div>
          </Flex>
          {isMerchantSalesFunnel && <ConversionFunnel pid={pid} />}
        </Flex>
      </CardWrap>
      <TaskList
        record={props?.data}
        merchantId={pid}
        onCloseTaskDetailDrawer={() => props.onCancel?.()} // 关闭当前drawer
        onOpenVisitDrawer={props.handleOpenVisitDrawer} // 打开去拜访drawer的方法
        onSwithShopList={props.onSwithShopList}
      />
    </Drawer>
  );
}
