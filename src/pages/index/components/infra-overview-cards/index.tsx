import React, { useMemo } from 'react';
import { Flex, Card, Tooltip, Space } from 'antd';
import { RightOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useMount, useRequest } from 'ahooks';
import styled from 'styled-components';
import { queryInfraWorkbenchOverview, InfraWorkbenchOverviewResponse } from '@/services';
import { traceClick, traceExp, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { TASK_TABPAN_ENUM } from '@/common/const';
import { useStore } from '@/context/global-store';

interface CardGroup {
  title: string;
  items: Array<{
    label: string;
    value: string | number;
    tooltip: string;
    filterKey: string;
  }>;
}

// 分组配置映射
const GROUP_CONFIG: Array<{
  key: keyof InfraWorkbenchOverviewResponse;
  title: string;
}> = [
  { key: 'workPool', title: '基建作业底池' },
  { key: 'communicatedPool', title: '沟通触达' },
  { key: 'infraJobPool', title: '基建作业' },
  { key: 'fiveStarDecoPool', title: '五星装修' },
  { key: 'fiveStarShellPool', title: '五星货架' },
  { key: 'fiveStarShopPool', title: '五星门店' },
];

const Container = styled.div`
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(180deg, #fafafa 0%, #f5f5f5 100%);
  border-radius: 8px;
`;

const Title = styled.div`
  font-size: 18px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 20px;
  letter-spacing: 0.5px;
`;

const TipText = styled.span`
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-left: 8px;
  font-weight: 400;
`;

const StyledCard = styled(Card)`
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  position: relative;
  min-width: 200px;
  flex-shrink: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: #1890ff;
    opacity: 0;
    transition: opacity 0.3s;
  }

  &:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: #1890ff;
    transform: translateY(-2px);

    &::before {
      opacity: 1;
    }
  }

  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px;
    min-height: auto;
    background: linear-gradient(180deg, #fafafa 0%, #fff 100%);
  }

  .ant-card-head-title {
    font-size: 15px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    padding: 0;
    letter-spacing: 0.3px;
  }

  .ant-card-body {
    padding: 16px;
    background: #fff;
  }
`;

const ItemLabel = styled.span`
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 22px;
  font-weight: 400;
`;

const ItemValue = styled.span`
  font-size: 18px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  letter-spacing: 0.5px;
`;

const QuestionIcon = styled(QuestionCircleOutlined)`
  color: #8c8c8c;
  cursor: help;
  font-size: 14px;
  margin-left: 6px;
  transition: all 0.2s;
  opacity: 0.7;

  &:hover {
    color: #1890ff;
    opacity: 1;
    transform: scale(1.1);
  }
`;

const ArrowIcon = styled.span`
  width: 0;
  height: 0;
  border-left: 5px solid rgba(0, 0, 0, 0.45);
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  margin-left: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
`;

const ArrowIconDouble = styled(RightOutlined)`
  font-size: 16px;
  color: #d9d9d9;
  position: relative;
  transition: all 0.3s;

  &:first-child {
    z-index: 1;
  }

  &:last-child {
    margin-left: -8px;
    z-index: 0;
    opacity: 0.6;
  }
`;

const CardItem = styled.div`
  cursor: pointer;
  padding: 12px 10px;
  border-radius: 6px;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 6px;
  position: relative;
  border: 1px solid transparent;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background-color: #f5f5f5;

    ${ArrowIcon} {
      transform: translateX(2px);
      border-left-color: rgba(0, 0, 0, 0.65);
    }
  }
`;

const ArrowContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  flex-shrink: 0;
  height: 100%;
  min-height: 140px;
  position: relative;

  &:hover {
    ${ArrowIconDouble} {
      color: #1890ff;
      opacity: 1;
    }
  }
`;

const InfraOverviewCards: React.FC = () => {
  const { viewer } = useStore() || {};
  const { data, loading } = useRequest(
    () => queryInfraWorkbenchOverview({ viewOperatorId: viewer || undefined }),
    {
      refreshDeps: [viewer],
    },
  );

  // 将后端返回的数据结构转换为前端需要的分组结构
  const cardGroups = useMemo<CardGroup[]>(() => {
    if (!data) return [];

    return GROUP_CONFIG.reduce<CardGroup[]>((groups, config) => {
      const items = data[config.key];
      if (items && items.length > 0) {
        groups.push({
          title: config.title,
          items: items.map((item) => ({
            label: item.property,
            value: item.value,
            tooltip: item.desc,
            filterKey: item.codes?.[0] || '',
          })),
        });
      }
      return groups;
    }, []);
  }, [data]);

  const handleCardClick = (filterKey: string, label: string) => {
    // 埋点
    traceClick(PageSPMKey.首页, ModuleSPMKey['数据概览卡片'], {
      filterKey,
      label,
    });

    // 判断是基建作业筛选还是五星门店筛选
    const isFiveStarFilter =
      filterKey.startsWith('FIVE_STAR_DECO_') ||
      filterKey.startsWith('FIVE_STAR_SHELF_') ||
      filterKey.startsWith('FIVE_STAR_SHOP_');

    // 处理「等级<LV3」的特殊逻辑
    const params: any = {};
    if (filterKey === 'SCORE_BELOW_LV3') {
      // 点击「等级<LV3」时，自动选中 LV1 和 LV2
      params.shopScoreLevels = ['LV1', 'LV2'];
    } else if (isFiveStarFilter) {
      params.fiveStarFilters = [filterKey];
    } else if (filterKey !== 'JOB_POOL') {
      params.infraWorkFilters = [filterKey];
    }

    // 触发门店列表筛选事件，切换到门店列表并设置筛选条件
    emitter.emit(EmitterEventMap.TaskDataClick, {
      type: TASK_TABPAN_ENUM.SHOP,
      params,
    });
  };
  useMount(() => {
    traceExp(PageSPMKey.首页, ModuleSPMKey.数据概览卡片);
  });

  if (loading) {
    return (
      <Container>
        <Title>
          数据概览 <TipText>数据仅统计有运维关系/基建运维/新签关系的门店</TipText>
        </Title>
        <Flex gap={16} wrap="nowrap">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <StyledCard key={i} loading />
          ))}
        </Flex>
      </Container>
    );
  }

  if (!cardGroups || cardGroups.length === 0) {
    return null;
  }

  return (
    <Container>
      <Title>
        数据概览 <TipText>数据仅统计有运维关系/基建运维/新签关系的门店</TipText>
      </Title>

      <Flex gap={8} wrap="nowrap" style={{ overflowX: 'auto', paddingBottom: 4 }}>
        {cardGroups.map((group, groupIndex) => (
          <React.Fragment key={group.title}>
            <StyledCard key={group.title} title={group.title}>
              <Space direction="vertical" size={0} style={{ width: '100%' }}>
                {group.items.map((item) => (
                  <CardItem
                    key={item.label}
                    onClick={() => handleCardClick(item.filterKey, item.label)}
                  >
                    <Flex justify="space-between" align="center">
                      <Flex align="center" style={{ flex: 1 }}>
                        <ItemLabel>{item.label}</ItemLabel>
                        <Tooltip title={item.tooltip} placement="top">
                          <QuestionIcon />
                        </Tooltip>
                      </Flex>
                      <Flex align="center">
                        <ItemValue>{item.value}</ItemValue>
                        <ArrowIcon />
                      </Flex>
                    </Flex>
                  </CardItem>
                ))}
              </Space>
            </StyledCard>
            {groupIndex < cardGroups.length - 1 && (
              <ArrowContainer>
                <ArrowIconDouble />
                <ArrowIconDouble />
              </ArrowContainer>
            )}
          </React.Fragment>
        ))}
      </Flex>
    </Container>
  );
};

export default InfraOverviewCards;
