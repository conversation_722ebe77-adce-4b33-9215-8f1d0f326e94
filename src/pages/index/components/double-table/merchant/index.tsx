import MerchantSearchForm from './searchform';
import MerchantTable from './merchant-table';
import { Form } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { useAntdTable } from 'ahooks';
import { getMerchantList } from '@/services/ai-material';
import {
  MERCHANT_SORT_TYPE_STATUS,
  SORT_BY_STATUS,
  TASK_TABPAN_ENUM,
  WORKBENCH_TYPE,
} from '@/common/const';
import { useStore } from '@/context/global-store';
import { traceExp, PageSPMKey, ModuleSPMKey, traceClick } from '@/utils/trace';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { useDoubleTableStore } from '../double-table-store';
import { useTaskQueue } from '../hooks/useTaskQueue';
import { IMerchantTaskEnums } from '@/types/ai-material/merchant';
import { parse } from 'query-string';

const defaultPageSize = 10;

interface MerchantRef {
  search: (params?: any) => Promise<void>;
  form: any;
  openAlertTaskDrawer: (data: any) => void;
  openTaskDetailDrawer: (data: any) => void;
}

interface MerchantProps {
  taskPriorityV2Enabled?: boolean;
  isMerchantSalesFunnel?: boolean;
  merchantTaskEnums?: IMerchantTaskEnums;
  workbenchType?: WORKBENCH_TYPE;
}

const Merchant = forwardRef<MerchantRef, MerchantProps>((props, ref) => {
  const {
    taskPriorityV2Enabled = true,
    isMerchantSalesFunnel = false,
    merchantTaskEnums = undefined,
    workbenchType,
  } = props;
  const [form] = Form.useForm();
  const { viewer } = useStore();
  const { isExpanded } = useDoubleTableStore();
  const merchantTableRef = useRef<any>(null);
  const urlQuery = parse(location.search);
  const hasInitializedRef = useRef(false);

  // 使用 hooks 处理商户任务队列
  const { hasTask } = useTaskQueue(TASK_TABPAN_ENUM.MERCHANT);

  const { tableProps, runAsync: fetchList } = useAntdTable(
    async (page, _params: any = form.getFieldsValue()) => {
      let warningTaskLabels;
      if (_params.warningTaskLabels) {
        warningTaskLabels = Array.isArray(_params.warningTaskLabels)
          ? _params.warningTaskLabels
          : [_params.warningTaskLabels];
      }

      const params = {
        viewOperatorId: viewer || undefined,
        page: {
          pageNo: page.current,
          pageSize: page.pageSize,
        },
        source: 'BASE_MERCHANT_LIST',
        ..._params,
        warningTaskLabels,
        hasOptGroupStr: _params.hasOptGroupStr,
        recommendTaskSources: _params.recommendTaskSources,
        merchantStages: _params.merchantStages,
        todoTaskTypes: _params.todoTaskTypes,
      };

      if (page.sorter) {
        const { sorter } = page;
        const { order, field } = sorter;
        let sortBy = '';
        let sortType = '';
        if (order === 'ascend') {
          sortType = MERCHANT_SORT_TYPE_STATUS.ASC;
          if (field === 'adCurrentMonthCost') {
            sortBy = SORT_BY_STATUS.MONTHCOST;
          }
          if (field === 'adCurrentBalance') {
            sortBy = SORT_BY_STATUS.CURRENTBALANCE;
          }
        }
        if (order === 'descend') {
          sortType = MERCHANT_SORT_TYPE_STATUS.DESC;
          if (field === 'adCurrentMonthCost') {
            sortBy = SORT_BY_STATUS.MONTHCOST;
          }
          if (field === 'adCurrentBalance') {
            sortBy = SORT_BY_STATUS.CURRENTBALANCE;
          }
        }
        params.sortBy = sortBy;
        params.sortType = sortType;
      }

      const res = await getMerchantList(params);
      return {
        list: res.dataList || [],
        total: res.pageInfo?.totalCount || 0,
      };
    },
    {
      form,
      defaultPageSize,
      manual: true,
      onSuccess: (res) => {
        for (const item of res.list) {
          traceExp(PageSPMKey.首页, ModuleSPMKey['商户列表.商户曝光'], {
            pid: item.pid,
            taskNo: item.priorityTaskInfo?.taskNo || '',
          });
        }
      },
    },
  );

  const handleSearch = (params?: any) => {
    return fetchList({ current: 1, pageSize: defaultPageSize }, params);
  };

  useImperativeHandle(
    ref,
    () => ({
      search: async (params?: any) => {
        if (params) {
          // 设置表单值
          form.resetFields();
          form.setFieldsValue(params);
          // 使用传入的参数进行搜索
          await handleSearch(params);
        } else {
          await handleSearch();
        }
      },
      form,
      openAlertTaskDrawer: (data: any) => {
        if (merchantTableRef.current) {
          merchantTableRef.current.openAlertTaskDrawer(data);
        }
      },
      openTaskDetailDrawer: (data: any) => {
        if (merchantTableRef.current) {
          merchantTableRef.current.openTaskDetailDrawer(data);
        }
      },
    }),
    [fetchList, form],
  );

  const handleSwithShopList = (params: any) => {
    // 发送事件切换到门店列表，传递pid参数
    emitter.emit(EmitterEventMap.TaskDataClick, {
      type: TASK_TABPAN_ENUM.SHOP,
      params: {
        ppid: params.pid, // 将商户的pid作为门店搜索的ppid
        processingInfraTaskType: params.processingInfraTaskType,
      },
    });
  };
  const handleFormValuesChange = (changedValues: any) => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['商户列表.筛选'], {
      filterType: Object.keys(changedValues).join(','),
    });
  };

  // 监听工作台切换事件，重置筛选状态
  useEffect(() => {
    const handleWorkbenchSwitch = () => {
      // 重置表单
      form.resetFields();
    };

    emitter.on(EmitterEventMap.WorkbenchSwitch, handleWorkbenchSwitch);

    return () => {
      emitter.off(EmitterEventMap.WorkbenchSwitch, handleWorkbenchSwitch);
    };
  }, [form]);

  useEffect(() => {
    if (!hasTask) {
      if (urlQuery?.pid) {
        form.setFieldsValue({ pid: urlQuery.pid as string });
        const params = form?.getFieldsValue();
        // 设置完表单值后，直接传递参数进行查询，确保能拿到 pid
        handleSearch({ ...params, pid: urlQuery.pid as string }).then(() => {
          hasInitializedRef.current = true;
        });
      } else {
        handleSearch().then(() => {
          hasInitializedRef.current = true;
        });
      }
    }
  }, []);

  // 监听 viewer 和 workbenchType 变化，刷新列表
  useEffect(() => {
    // 如果列表已经初始化过，则刷新
    if (hasInitializedRef.current) {
      handleSearch();
    }
  }, [viewer, workbenchType]);

  return (
    <div>
      <Form form={form} onValuesChange={handleFormValuesChange}>
        <MerchantSearchForm
          onSearch={handleSearch}
          form={form}
          isShow={isExpanded}
          loading={tableProps.loading}
          taskPriorityV2Enabled={taskPriorityV2Enabled}
          merchantTaskEnums={merchantTaskEnums}
        />

        <MerchantTable
          ref={merchantTableRef}
          list={tableProps.dataSource || []}
          onSearch={handleSearch}
          pagination={tableProps.pagination}
          loading={tableProps.loading}
          onChange={tableProps.onChange}
          onSwithShopList={handleSwithShopList}
          taskPriorityV2Enabled={taskPriorityV2Enabled}
          isMerchantSalesFunnel={isMerchantSalesFunnel}
        />
      </Form>
    </div>
  );
});

Merchant.displayName = 'Merchant';

export default Merchant;
