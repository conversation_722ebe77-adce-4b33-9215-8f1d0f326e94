import React, { useState, useCallback, useEffect } from 'react';
import { Mo<PERSON>, Steps, Button, Tooltip } from 'antd';
import { BatchSubmitStep, IShopInfo } from '@/types/batch-submit';
import ShopSelector from './shop-selector';
import SubmitReview from './submit-review';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { queryUnfinishedOptWoosBizOrder } from '@/services/batch-submit';
import { configBusinessNewsGrey } from '@/services';
import { useRequest } from 'ahooks';

const BatchSubmitModal: React.FC = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState<BatchSubmitStep>(BatchSubmitStep.SHOP_SELECT);
  const [selectedShops, setSelectedShops] = useState<IShopInfo[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchBtnDisabled, setBatchBtnDisabled] = useState(false);
  const [autoShowTargetModal, setAutoShowTargetModal] = useState(false); // 是否自动弹出统一填写目标门店弹窗

  // 新增：缓存门店列表数据，避免重复请求
  const [cachedShopData, setCachedShopData] = useState<{
    shopList: IShopInfo[];
    hasLoaded: boolean;
  }>({
    shopList: [],
    hasLoaded: false,
  });

  // 查询批量提报灰度开关
  const { data: batchSubmitEnabled } = useRequest(
    async () => {
      const res = await configBusinessNewsGrey(['OPT_ESP_ORDER_BATCH_SUBMIT_GREY']);
      return res?.[0]?.switchStatus;
    },
    {
      onSuccess: (data) => {
        if (data) {
          traceExp(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {});
        }
      },
    },
  );

  // 查询未完结批量提报任务
  useEffect(() => {
    if (batchSubmitEnabled) {
      queryUnfinishedOptWoosBizOrder({}).then((res) => {
        console.log('res', res);

        setBatchBtnDisabled(res === true);
      });
    }
  }, [batchSubmitEnabled]);

  // 弹窗曝光埋点
  useEffect(() => {
    if (modalVisible) {
      traceExp(PageSPMKey.首页, ModuleSPMKey['批量提报.弹窗曝光'], {});
    }
  }, [modalVisible]);
  // 步骤切换埋点
  useEffect(() => {
    if (modalVisible) {
      if (currentStep === BatchSubmitStep.SHOP_SELECT) {
        traceExp(PageSPMKey.首页, ModuleSPMKey['批量提报.门店选择步骤'], {});
      } else if (currentStep === BatchSubmitStep.SUBMIT_REVIEW) {
        traceExp(PageSPMKey.首页, ModuleSPMKey['批量提报.提报审核步骤'], {});
      }
    }
  }, [currentStep, modalVisible]);

  // 打开弹窗
  const handleOpenModal = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['门店列表.批量提报按钮'], {});
    setModalVisible(true);
  }, []);

  // 关闭弹窗时清空所有数据
  const handleClose = useCallback(() => {
    setCurrentStep(BatchSubmitStep.SHOP_SELECT);
    setSelectedShops([]);
    setSelectedRowKeys([]);
    setCachedShopData({ shopList: [], hasLoaded: false });
    setAutoShowTargetModal(false);
    setModalVisible(false);
  }, []);

  // 步骤切换
  const handleNext = useCallback(() => {
    setCurrentStep(BatchSubmitStep.SUBMIT_REVIEW);
    // 切换到第二步时，自动弹出统一填写目标门店弹窗
    setAutoShowTargetModal(true);
  }, []);
  const handlePrev = useCallback(() => {
    setCurrentStep(BatchSubmitStep.SHOP_SELECT);
    setAutoShowTargetModal(false); // 返回第一步时重置状态
  }, []);

  // 门店选择回调
  const handleShopsSelected = useCallback((shops: IShopInfo[]) => {
    setSelectedShops(shops);
    setSelectedRowKeys(shops.map((shop) => shop.shopId));
  }, []);

  // 提交成功后关闭弹窗
  const handleSubmit = useCallback(() => {
    handleClose();
  }, [handleClose]);

  // 如果灰度开关未开启，不显示批量提报按钮
  if (!batchSubmitEnabled) {
    return null;
  }

  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: 16,
          background: '#fff',
        }}
      >
        <Tooltip title={batchBtnDisabled ? '正在处理中，暂时无法批量提报，请过5分钟后再试。' : ''}>
          <Button
            type="primary"
            style={{ marginBottom: 12 }}
            disabled={batchBtnDisabled}
            onClick={handleOpenModal}
          >
            批量提报
          </Button>
        </Tooltip>
      </div>
      <Modal
        open={modalVisible}
        onCancel={handleClose}
        footer={null}
        width={1200}
        destroyOnClose
        title="批量提报"
      >
        <Steps
          current={currentStep - 1}
          items={[{ title: '门店选择' }, { title: '提报审核' }]}
          style={{ marginBottom: 24 }}
        />
        {currentStep === BatchSubmitStep.SHOP_SELECT && (
          <ShopSelector
            onShopsSelected={handleShopsSelected}
            onCancel={handleClose}
            onNext={handleNext}
            selectedRowKeys={selectedRowKeys}
            selectedShops={selectedShops}
            onSelectedRowKeysChange={setSelectedRowKeys}
            onSelectedShopsChange={setSelectedShops}
            cachedShopData={cachedShopData}
            onCachedShopDataChange={setCachedShopData}
          />
        )}
        {currentStep === BatchSubmitStep.SUBMIT_REVIEW && (
          <SubmitReview
            shops={selectedShops}
            onTargetShopChange={() => {}}
            onCancel={handleClose}
            onPrev={handlePrev}
            onSubmit={handleSubmit}
            autoShowModal={autoShowTargetModal}
            onAutoShowModalHandled={() => {
              // 弹窗已弹出，重置状态，避免重复弹出
              setAutoShowTargetModal(false);
            }}
          />
        )}
      </Modal>
    </>
  );
};

export default BatchSubmitModal;
