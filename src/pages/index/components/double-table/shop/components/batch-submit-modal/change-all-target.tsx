import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Form, Input, Checkbox, Select, Spin } from 'antd';
import { debounce } from 'lodash';
import { queryTargetShopPageList } from '@/services/batch-submit';
import type { ITargetShopItem } from '@/types/batch-submit';

const { Item } = Form;

const ChangeAllTarget = forwardRef(({ initialValues }: { initialValues?: any }, ref) => {
  const [formRef] = Form.useForm();
  const [list, setList] = useState<ITargetShopItem[]>(() => {
    // 如果有已选择的门店，初始化时添加到列表
    if (initialValues?.targetShopId && initialValues?.amapShopName) {
      return [
        {
          targetShopId: initialValues.targetShopId,
          targetShopName: initialValues.amapShopName,
        },
      ];
    }
    return [];
  });
  const [loading, setLoading] = useState(false);
  const [searchType, setSearchType] = useState<'shopId' | 'shopName'>('shopId');

  const isNoTarget = Form.useWatch('isNoTarget', formRef);

  // 切换搜索类型时清空选择
  const handleSearchTypeChange = (type: 'shopId' | 'shopName') => {
    setSearchType(type);
    formRef.setFieldsValue({ targetShopId: undefined });
    setList([]);
  };

  useImperativeHandle(ref, () => ({
    getFieldsValue: () => {
      const values = formRef.getFieldsValue();
      const { targetShopId } = values;
      if (targetShopId) {
        const selectedShop = list.find((item) => item.targetShopId === targetShopId);
        return {
          ...values,
          amapShopName: selectedShop?.targetShopName || values.targetShopName || '',
        };
      }
      return { ...values, amapShopName: '' };
    },
    validate: () => formRef.validateFields(),
  }));

  useEffect(() => {
    if (initialValues) {
      formRef.setFieldsValue({
        ...initialValues,
        targetShopId: initialValues.targetShopId || undefined,
      });
    }
  }, [initialValues, formRef]);

  // 勾选"无目标门店名称"时，清空两个输入框
  useEffect(() => {
    if (isNoTarget) {
      formRef.setFieldsValue({
        targetShopId: undefined,
        targetShopName: '',
      });
    }
  }, [isNoTarget, formRef]);

  const doSearch = async (val: string) => {
    setLoading(true);
    try {
      const params: any = {
        page: { pageNo: 1, pageSize: 20 },
      };

      if (searchType === 'shopId') {
        // 精确查询：传 targetShopId
        params.targetShopId = val || '';
      } else {
        // 模糊查询：传 targetShopSearch
        params.targetShopSearch = val || '';
      }

      const res = await queryTargetShopPageList(params);
      const newList = res.dataList || [];
      const selectedId = formRef.getFieldValue('targetShopId');
      setList((prevList) => {
        const selectedItem = prevList.find((item) => item.targetShopId === selectedId);
        return selectedItem ? [selectedItem, ...newList] : newList;
      });
    } finally {
      setLoading(false);
    }
  };

  const debouncedSearchRef = useRef<any>(null);

  // 每次 searchType 变化时重新创建防抖函数
  useEffect(() => {
    debouncedSearchRef.current = debounce((val: string) => {
      doSearch(val);
    }, 300);

    return () => {
      debouncedSearchRef.current?.cancel();
    };
  }, [searchType]);

  const handleSearch = (val: string) => {
    debouncedSearchRef.current?.(val);
  };

  const handleChange = (shopId: string) => {
    formRef.setFieldsValue({
      targetShopId: shopId,
    });
  };

  return (
    <Form form={formRef} labelCol={{ span: 12 }} wrapperCol={{ span: 16 }}>
      <Item
        style={{ marginBottom: 16 }}
        label="目标门店对应在高德的门店名称"
        name="targetShopId"
        dependencies={['isNoTarget']}
        rules={[{ required: !isNoTarget, message: '请选择目标门店对应在高德的门店名称' }]}
        getValueFromEvent={(value) => value || undefined}
        normalize={(value) => value || undefined}
      >
        <Input.Group compact>
          <Select
            style={{ width: '100px' }}
            value={searchType}
            onChange={handleSearchTypeChange}
            disabled={isNoTarget}
            options={[
              { label: 'shopid', value: 'shopId' },
              { label: '门店名称', value: 'shopName' },
            ]}
          />
          <Select
            key={searchType}
            style={{ width: '220px' }}
            showSearch
            allowClear
            placeholder={
              searchType === 'shopId' ? '请输入单门店id,精准查询' : '请输入单门店名称,模糊查询'
            }
            loading={loading}
            filterOption={false}
            onSearch={handleSearch}
            onChange={handleChange}
            disabled={isNoTarget}
            notFoundContent={loading ? <Spin size="small" /> : '暂无数据'}
            options={list.map((item) => ({
              key: item.targetShopId,
              value: item.targetShopId,
              label: item.targetShopName,
            }))}
          />
        </Input.Group>
      </Item>
      <Item
        style={{ marginBottom: 0 }}
        label="目标门店名称"
        name="targetShopName"
        dependencies={['isNoTarget']}
        rules={[{ required: !isNoTarget, message: '请输入目标门店名称' }]}
      >
        <Input disabled={isNoTarget} placeholder="请输入目标门店名称" />
      </Item>
      <Item label={null} valuePropName="checked" name="isNoTarget">
        <Checkbox>无目标门店名称</Checkbox>
      </Item>
      <div style={{ whiteSpace: 'pre-wrap' }}>
        {`注意：
        1. 此功能适用于对标同一个目标门店，请确认好目标门店
        2. 选择"无目标门店名称"则所有门店均会勾选无目标门店
        3. 填写后，已填写的目标门店均会被覆盖
        `}
        <span style={{ color: 'red' }}>
          4. 请大家发品后，请保证高德门店商品已审核通过后，再提报审核，不然导致不过审！
        </span>
      </div>
    </Form>
  );
});

export default ChangeAllTarget;
