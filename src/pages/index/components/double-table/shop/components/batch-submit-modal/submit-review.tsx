import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  Table,
  Button,
  Input,
  Checkbox,
  Space,
  message,
  Tooltip,
  Modal,
  Row,
  Typography,
} from 'antd';
import { batchSubmitAndCreateEspOrder } from '@/services/batch-submit';
import { IShopInfo, ITargetShopSetting } from '@/types/batch-submit';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { InfoCircleOutlined } from '@ant-design/icons';
import ChangeAllTarget from './change-all-target';

interface ISubmitReviewProps {
  shops: IShopInfo[];
  onTargetShopChange: (settings: ITargetShopSetting[]) => void;
  onCancel: () => void;
  onPrev: () => void;
  onSubmit: (settings: ITargetShopSetting[]) => void;
  autoShowModal?: boolean; // 是否自动弹出统一填写目标门店弹窗
  onAutoShowModalHandled?: () => void; // 自动弹出处理完成后的回调
}

const SubmitReview: React.FC<ISubmitReviewProps> = ({
  shops,
  onTargetShopChange,
  onCancel,
  onPrev,
  onSubmit,
  autoShowModal = false,
  onAutoShowModalHandled,
}) => {
  const allChangeRefs = useRef(null);
  const [settings, setSettings] = useState<ITargetShopSetting[]>([]);
  const [loading, setLoading] = useState(false);
  const [validateError, setValidateError] = useState<Record<string, boolean>>({});
  const [allTargetValue, setAllTargetValue] = useState({
    targetShopName: '',
    targetShopId: '',
    amapShopName: '', // 高德门店名称
    isNoTarget: false,
  });

  // 初始化目标门店设置，查询装修素材提报记录
  useEffect(() => {
    const newSettings: ITargetShopSetting[] = shops.map((shop) => ({
      shopId: shop.shopId,
      targetShopName: shop.collectShopName || '',
      hasSubmitRecord: !!shop.collectShopName,
      isNoTarget: false, // 不再默认勾选无目标门店
    }));

    setSettings(newSettings);
    onTargetShopChange(newSettings);
  }, [shops, onTargetShopChange]);

  // 自动弹出统一填写目标门店弹窗（当从第一步点击下一步进入时）
  useEffect(() => {
    if (autoShowModal && shops.length > 0 && settings.length > 0) {
      handleShowModal();
      onAutoShowModalHandled?.();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoShowModal, shops.length, settings.length]);

  // 目标门店名称变更
  const handleTargetShopNameChange = useCallback(
    (shopId: string, value: string) => {
      setSettings((prev) => {
        const updated = prev.map((item) =>
          item.shopId === shopId ? { ...item, targetShopName: value, isNoTarget: !value } : item,
        );
        onTargetShopChange(updated);
        // 实时清除校验
        setValidateError((err) => ({ ...err, [shopId]: false }));
        return updated;
      });
    },
    [onTargetShopChange],
  );

  // 无目标门店勾选变更
  const handleNoTargetChange = useCallback(
    (shopId: string, checked: boolean) => {
      setSettings((prev) => {
        const updated = prev.map((item) =>
          item.shopId === shopId ? { ...item, isNoTarget: checked } : item,
        );
        onTargetShopChange(updated);
        setValidateError((err) => ({ ...err, [shopId]: false }));
        return updated;
      });
    },
    [onTargetShopChange],
  );

  // 上一步埋点
  const handlePrevStep = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.提报审核.取消'], { type: 'prev' });
    onPrev();
  }, [onPrev]);
  // 取消埋点
  const handleCancel = useCallback(() => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.提报审核.取消'], { type: 'cancel' });
    onCancel();
  }, [onCancel]);

  // 提交审核
  const handleSubmit = useCallback(async () => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.提报审核.提交'], {});
    // 校验：每个门店必须填写目标门店或勾选无目标门店
    const validateMap: Record<string, boolean> = {};
    for (const item of settings) {
      if (!item.targetShopName && !item.isNoTarget) {
        validateMap[item.shopId] = true;
      }
    }
    setValidateError(validateMap);
    if (Object.keys(validateMap).length > 0) {
      message.warning('请为所有门店填写目标门店名称或勾选无目标门店');
      return;
    }
    setLoading(true);
    try {
      await batchSubmitAndCreateEspOrder({
        createReqs: settings.map((item) => ({
          shopId: item.shopId,
          collectShopName: item.isNoTarget ? '' : item.targetShopName || '',
          targetShopId: item.isNoTarget ? undefined : item.targetShopId,
        })),
      });
      traceClick(PageSPMKey.首页, ModuleSPMKey['批量提报.提交成功'], {});
      message.success('批量提报任务创建成功');
      onSubmit(settings);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }, [settings, onSubmit]);

  const handleShowModal = () => {
    const modal = Modal.confirm({
      width: 700,
      icon: null,
      title: '连锁门店统一填写一家目标门店',
      content: <ChangeAllTarget ref={allChangeRefs} initialValues={allTargetValue} />,
      closable: false,
      onOk: () =>
        new Promise((resolve, reject) => {
          allChangeRefs.current
            ?.validate()
            .then(() => {
              const { targetShopName, targetShopId, amapShopName, isNoTarget } =
                allChangeRefs.current?.getFieldsValue() || {};
              setSettings((prev) => {
                const updated = prev.map((item) =>
                  targetShopName
                    ? {
                        ...item,
                        targetShopName,
                        targetShopId,
                        isNoTarget,
                      }
                    : { ...item, targetShopName: '', isNoTarget, targetShopId: undefined },
                );
                onTargetShopChange(updated);
                setValidateError({});
                setAllTargetValue({
                  targetShopName,
                  targetShopId,
                  amapShopName: amapShopName || '',
                  isNoTarget,
                });
                modal.destroy();
                return updated;
              });
              resolve(null);
            })
            .catch(reject);
        }),
      onCancel: () => {
        modal.destroy();
      },
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '门店名称',
      dataIndex: 'shopName',
      key: 'shopName',
      render: (_: any, record: IShopInfo) =>
        shops.find((s) => s.shopId === record.shopId)?.shopName,
    },
    { title: '门店ID', dataIndex: 'shopId', key: 'shopId' },
    {
      title: '商户ID',
      dataIndex: 'pid',
      key: 'pid',
      render: (_: any, record: IShopInfo) => shops.find((s) => s.shopId === record.shopId)?.pid,
    },
    {
      title: '最近提报时间',
      dataIndex: 'lastSubmitTime',
      key: 'lastSubmitTime',
      render: (_: any, record: IShopInfo) =>
        shops.find((s) => s.shopId === record.shopId)?.lastSubmitTime,
    },

    {
      title: '无目标门店',
      dataIndex: 'isNoTarget',
      key: 'isNoTarget',
      render: (_: any, record: IShopInfo) => {
        const setting = settings.find((s) => s.shopId === record.shopId);
        return (
          <Checkbox
            checked={!!setting?.isNoTarget}
            disabled
            onChange={(e) => handleNoTargetChange(record.shopId, e.target.checked)}
          >
            无目标
          </Checkbox>
        );
      },
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          目标门店名称
          <Tooltip title="目标门店名称，请填写审核对标门店">
            <InfoCircleOutlined style={{ marginLeft: 12 }} />
          </Tooltip>
        </div>
      ),
      dataIndex: 'targetShopName',
      key: 'targetShopName',
      render: (_: any, record: IShopInfo) => {
        const setting = settings.find((s) => s.shopId === record.shopId);
        return (
          <Input
            value={setting?.targetShopName}
            disabled
            onChange={(e) => handleTargetShopNameChange(record.shopId, e.target.value)}
            placeholder="请输入目标门店名称"
            style={{ width: 160 }}
            status={validateError[record.shopId] ? 'error' : undefined}
          />
        );
      },
    },
  ];

  return (
    <div>
      <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
        <div style={{ flex: 1 }} />
        <div style={{ display: 'flex', alignItems: 'center', maxWidth: '40%' }}>
          <span style={{ whiteSpace: 'nowrap' }}>目标门店对应在高德的门店名称:&nbsp;</span>
          <Typography.Text ellipsis={{ tooltip: allTargetValue.amapShopName }} style={{ flex: 1 }}>
            {allTargetValue.amapShopName || ''}
          </Typography.Text>
        </div>
        <div style={{ flex: 1, display: 'flex', justifyContent: 'flex-end' }}>
          <Checkbox
            disabled={!shops?.length}
            checked={allTargetValue?.targetShopName !== '' || allTargetValue?.isNoTarget}
            onChange={handleShowModal}
          >
            连锁门店统一填写一家目标门店
          </Checkbox>
        </div>
      </Row>

      <Table
        rowKey="shopId"
        columns={columns}
        dataSource={shops}
        pagination={false}
        scroll={{ x: 1000, y: 660 }}
      />
      <div
        style={{
          marginTop: 24,
          textAlign: 'right',
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16,
        }}
      >
        <Space>
          <Button onClick={handlePrevStep}>上一步</Button>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={handleSubmit}>
            提报审核
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default SubmitReview;
