import React, { useEffect, useState, useRef, forwardRef, useImperativeHandle } from 'react';
import { Badge, Table, Tag, message, Popover, Divider, Popconfirm } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import AfeClipboard from '@alife/afe-clipboard';
import Link from '@/components/Link';
import { AgentOperationShopRelationDTO } from '@/_docplus/target/types/amap-sales-operation-client';
import { useSubmitModal } from '@alife/peng-yi-peng';

import {
  TASK_STATUS,
  SHOP_SORT_TYPE_STATUS,
  MERCHANT_EXPIRE_CONDITION_ENUM_OPTIONS,
  MERCHANT_SUB_STATUS,
  TASK_DETAIL_JUMP_SOURCE,
  TASK_DETAIL_JUMP_FROM,
  SHOP_LABELS_OPTIONS,
} from '@/common/const';
import { getMerchantLabels, isAmapAgent, sendEvent } from '@/common/utils';
import WeeklyReportDrawer from '@/components/business-news/components/weekly-report-drawer';
import './index.less';
import { TaskDetailDrawer } from '@/components/task-detail';
import { getMerchantReviewExtInfo, queryContactList } from '@/services';
import { IAction } from '@/types';
import { IfButtonShow } from '@/components/server-controller/useAction';
import OrderModal from '@/components/order-modal';
import useModal from '@/hooks/useModal';
import { OutBoundDetailDrawer } from '@/pages/index/components/outbound-detail';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import TaskCountdown from '@/pages/index/components/todo-list/TaskCountdown';
import dayjs from 'dayjs';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

const map = {
  ONLINE: {
    label: '线上',
    color: '#2aa0ea',
  },
  BD: {
    label: '渠道',
    color: '#71b70d',
  },
  '1688': {
    label: '1688',
    color: '#ffd700',
  },
};
// 年度订单内容
const getAnnualInfo = (info: any, hideTime?: boolean) => {
  if (!info) return null;
  const channel = map[info.annualProductSignChannel];
  return (
    <div>
      {info.annualProductName && (
        <div>
          {channel && (
            <span
              style={{
                fontSize: 12,
                padding: '2px 4px',
                background: channel.color,
                color: '#fff',
                marginRight: 4,
              }}
            >
              {channel.label}
            </span>
          )}
          名称: {info.annualProductName}
        </div>
      )}
      {info.annualProductId && <div>ID: {info.annualProductId}</div>}
      {info.annualProductPayTime && (
        <div>支付时间: {dayjs(info.annualProductPayTime).format('YYYY-MM-DD HH:mm:ss')}</div>
      )}
      {info.annualProductOrderId && <div>订单ID: {info.annualProductOrderId}</div>}
      {/* {info.annualProductPayStatus && (
        <div>
          支付状态: {Payment_Status[info.annualProductPayStatus] || info.annualProductPayStatus}
        </div>
      )} */}
      {!hideTime && (
        <>
          {info.annualProductEffectiveDate && (
            <div>
              年费生效时间:
              {dayjs(info.annualProductEffectiveDate).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          )}
          {info.annualProductExpireDate && (
            <div>
              年费到期时间:
              {dayjs(info.annualProductExpireDate).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          )}
        </>
      )}
    </div>
  );
};

interface ShopTableRef {
  openTaskDetailDrawer: (data: any) => void;
}

interface IProps {
  list: AgentOperationShopRelationDTO[];
  listLoading: boolean;
  pagination: any;
  onChange: (pagination: any, filters: any, sorter: any) => void;
  fetchList: (searchParams?: any) => void;
  sortData: { sortBy?: any; sortType?: any };
  selectedRowKey: any;
  setSelectedRowKey: (key: any) => void;
  urlQuery: any;
  selectedLabels: string[];
  taskPriorityV2Enabled?: boolean;
  isInfrastructureWorkbenchQuery?: boolean;
}

export const ShopTable = forwardRef<ShopTableRef, IProps>(
  (
    {
      list = [],
      listLoading,
      pagination,
      onChange,
      fetchList,
      sortData,
      selectedRowKey,
      setSelectedRowKey,
      urlQuery,
      selectedLabels,
      taskPriorityV2Enabled = true,
      isInfrastructureWorkbenchQuery = false,
    },
    ref,
  ) => {
    const [drawerData, setDrawerData] = useState<any>({});
    const [taskDetailDrawerVisible, setTaskDetailDrawerVisible] = useState(false);
    const [taskDetailData, setTaskDetailData] = useState<any>({});
    const listRef = useRef(null);
    const [outBoundInfo, setOutBoundInfo] = useState<any>({});
    const [pengModalOptions, setPengModalOptions] = useState<any>(null);
    const entityId = pengModalOptions?.shopId;
    const entityType = 'amap_shop';

    // 五星门店列组件
    const FiveStarShopCell: React.FC<{ record: any }> = ({ record }) => {
      const { submitted, fiveStarShop, infraJobProgress } = record;

      if (!submitted) {
        const progressContent = (
          <div>
            <div style={{ marginBottom: 16 }}>
              <span style={{ marginRight: 8 }}>沟通触达：</span>
              <Badge
                status={infraJobProgress?.communicated ? 'success' : 'error'}
                text={infraJobProgress?.communicated ? '已完成' : '未完成'}
              />
            </div>
            <div style={{ marginBottom: 16 }}>
              <span style={{ marginRight: 8 }}>支付进件：</span>
              <Badge
                status={infraJobProgress?.paymentCompleted ? 'success' : 'error'}
                text={infraJobProgress?.paymentCompleted ? '已完成' : '未完成'}
              />
            </div>
            <div style={{ marginBottom: 16 }}>
              <span style={{ marginRight: 8 }}>资质认证：</span>
              <Badge
                status={infraJobProgress?.auditFinish ? 'success' : 'error'}
                text={infraJobProgress?.auditFinish ? '已完成' : '未完成'}
              />
            </div>
            <div>
              <span style={{ marginRight: 8 }}>费率签约：</span>
              <Badge
                status={infraJobProgress?.rateSignCompleted ? 'success' : 'error'}
                text={infraJobProgress?.rateSignCompleted ? '已完成' : '未完成'}
              />
            </div>
          </div>
        );

        return (
          <div onClick={(e) => e.stopPropagation()}>
            <div style={{ marginBottom: 8 }}>未提报</div>
            <Popover
              trigger={['click']}
              placement="leftTop"
              styles={{
                body: {
                  maxHeight: 300,
                  overflowY: 'auto',
                },
              }}
              content={progressContent}
              title="作业进度"
            >
              <a>查看作业进度</a>
            </Popover>
          </div>
        );
      }

      // 已提报质检：固定展示以下3项的最新审核状态
      if (submitted && fiveStarShop) {
        return (
          <div>
            <div style={{ marginBottom: 8, display: 'flex', alignItems: 'center' }}>
              {fiveStarShop.fiveStarDecoPass ? (
                <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 4 }} />
              ) : (
                <CloseCircleOutlined style={{ color: '#ff4d4f', marginRight: 4 }} />
              )}
              <span>五星装修达标</span>
            </div>
            <div style={{ marginBottom: 8, display: 'flex', alignItems: 'center' }}>
              {fiveStarShop.fiveStarShelfPass ? (
                <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 4 }} />
              ) : (
                <CloseCircleOutlined style={{ color: '#ff4d4f', marginRight: 4 }} />
              )}
              <span>五星货架达标</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {fiveStarShop.fiveStarShopPass ? (
                <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 4 }} />
              ) : (
                <CloseCircleOutlined style={{ color: '#ff4d4f', marginRight: 4 }} />
              )}
              <span>五星门店达标</span>
            </div>
          </div>
        );
      }

      return <span>-</span>;
    };
    const { modalEl: PengModalEl, showModalPromise: showPengModal } = useSubmitModal({
      queryOptions: {
        entityId,
        entityType,
        scene: 'TEL_VISIT',
        sourceFrom: 'XY_PC_AGENT_SHOP',
      },
      createOptions: {
        entityId,
        entityType,
        scene: 'TEL_VISIT',
      },
      submitOptions: {
        shopId: pengModalOptions?.shopId || '',
        source: 'ADVERTISE_OPERATE',
      },
    });
    useEffect(() => {
      if (pengModalOptions) {
        showPengModal()
          .then(() => {
            // 刷新
            fetchList();
          })
          .catch((error) => {
            console.error('error:', error);
          })
          .finally(() => {
            setPengModalOptions(null);
          });
      }
    }, [pengModalOptions]);

    const handleActionClick = (item, record) => {
      traceClick(PageSPMKey.首页, ModuleSPMKey['门店列表.操作项'], {
        operation: item.buttonText,
        pid: record.pid,
      });

      switch (item.buttonType) {
        case 'ALIPAY_TOUCH':
          setPengModalOptions(record);
          sendEvent('ALIPAY_TOUCH_BTN', 'CLK');
          break;
        case 'TO_COMPLETE':
          openTaskDetailDrawer(record);
          sendEvent(
            record.shopTaskStatus === TASK_STATUS.COMPLETED
              ? 'VIEW_TASK_DETAIL_BTN'
              : 'TO_DO_TASK_BTN',
            'CLK',
          );
          break;
        case 'GENERATE_NEWS':
          openDrawer(record);
          sendEvent('CREATE_BUSINESS_NEWS_BTN', 'CLK');
          break;
        case 'AD_AGENT_OPT':
          updateAndOpen({ pid: record.pid, shopId: record.shopId });
          break;
        case 'TELEPHONE_COMMUNICATION':
          handleCallClick(record);
          break;
        default:
          break;
      }
    };

    const sortOrder = (field: string): 'ascend' | 'descend' | null => {
      if (sortData.sortBy === field) {
        if (sortData.sortType === SHOP_SORT_TYPE_STATUS.ASC) {
          return 'ascend';
        }
        return 'descend';
      }
      return null;
    };

    async function handleCallClick(record: any) {
      const res = await queryContactList({
        page: 1,
        pageSize: 99,
        entityId: record.shopId,
        entityType: 'amap_shop',
        scene: 'TEL_VISIT',
      });
      if (res.length) {
        setOutBoundInfo({
          pid: record.pid,
          merchantName: record.merchantName,
          shopInfo: {
            ...record,
            contactInfo: res,
          },
        });
      } else {
        message.warning('暂时未获取到门店联系方式');
      }
    }

    const columns = [
      {
        title: '门店基础信息',
        dataIndex: 'shopInformation',
        key: 'shopInformation',
        width: 300,
        filters: SHOP_LABELS_OPTIONS.map((item) => ({
          text: item.desc,
          value: item.code,
        })),
        filteredValue: selectedLabels,
        render: (value, record) => {
          return (
            <>
              {record.shopName && <div>门店名称 : {record.shopName}</div>}
              {record.shopId && (
                <div style={{ wordWrap: 'break-word', wordBreak: 'break-all' }}>
                  门店ID : {record.shopId}
                </div>
              )}
              {record.pid && <div>PID : {record.pid}</div>}
              {record.shopCity && <div>城市 : {record.shopCity}</div>}
              {record.shopLabels &&
                record.shopLabels.length > 0 &&
                record.shopLabels
                  .sort((a, b) => {
                    const getOrder = (code: string) => {
                      if (code === '10_orders') return 2;
                      if (code === 'high_exposure') return 1;
                      return 0;
                    };
                    return getOrder(a) - getOrder(b);
                  })
                  .map((item: string) => {
                    const renderObj = getMerchantLabels(item);
                    if (React.isValidElement(renderObj)) {
                      return renderObj;
                    }
                    return (
                      <Tag className="shop-labels-tag" color={(renderObj as any).color} key={item}>
                        {(renderObj as any).value}
                      </Tag>
                    );
                  })}
            </>
          );
        },
      },
      {
        title: 'POIID',
        dataIndex: 'poiId',
        key: 'poiId',
        width: 150,
        render: (value, record) => {
          return <span>{record.poiId || '-'}</span>;
        },
      },
      // 根据灰度开关控制门店类目列的显示：false-老逻辑（分开两列），true-新逻辑（合并一列）
      ...(isInfrastructureWorkbenchQuery
        ? [
            // 新逻辑：合并成一列
            {
              title: '门店类目',
              dataIndex: 'atagIdList',
              key: 'atagIdList',
              width: 200,
              render: (atagIdList, record) => {
                const atagNameList = record.atagNameList || [];
                const hasId = Array.isArray(atagIdList) && atagIdList.length > 0;
                const hasName = Array.isArray(atagNameList) && atagNameList.length > 0;

                if (!hasId && !hasName) {
                  return <span>-</span>;
                }

                return (
                  <div>
                    {hasId && <div>类目ID: {atagIdList.join(',')}</div>}
                    {hasName && <div>类目名称: {atagNameList.join(',')}</div>}
                  </div>
                );
              },
            },
          ]
        : [
            // 老逻辑：分开两列
            {
              title: 'Atag ID',
              dataIndex: 'atagIdList',
              key: 'atagIdList',
              width: 150,
              render: (atagIdList) => {
                return <span>{atagIdList?.join(',') || '-'}</span>;
              },
            },
            {
              title: 'Atag Name',
              dataIndex: 'atagNameList',
              key: 'atagNameList',
              width: 200,
              render: (atagNameList) => {
                return <span>{atagNameList?.join(',') || '-'}</span>;
              },
            },
          ]),
      {
        title: '商家分',
        dataIndex: 'shopQualityScore',
        key: 'shopQualityScore',
        width: isInfrastructureWorkbenchQuery ? 80 : 90,
        render: (value, record) => {
          return <span>{record.shopQualityScore || '-'}</span>;
        },
      },
      // 根据灰度开关控制商家分等级列的显示：false-老逻辑（分开两列），true-新逻辑（合并一列）
      ...(isInfrastructureWorkbenchQuery
        ? [
            // 新逻辑：合并成一列
            {
              title: '商家分等级',
              dataIndex: 'shopScoreLevel',
              key: 'shopScoreLevel',
              width: 180,
              render: (value, record) => {
                const { shopScoreLevel, nextGradeScoreGap } = record;
                return (
                  <div>
                    <div>{shopScoreLevel || '-'}</div>
                    {nextGradeScoreGap && <div>距下一等级分差: {nextGradeScoreGap || '-'}分</div>}
                  </div>
                );
              },
            },
          ]
        : [
            // 老逻辑：分开两列
            {
              title: '商家分等级',
              dataIndex: 'shopScoreLevel',
              key: 'shopScoreLevel',
              width: 120,
              render: (value) => {
                return <span>{value || '-'}</span>;
              },
            },
            {
              title: '距离下一等级分差',
              dataIndex: 'nextGradeScoreGap',
              key: 'nextGradeScoreGap',
              width: 140,
              render: (value) => {
                return <span>{value || '-'}</span>;
              },
            },
          ]),
      ...(taskPriorityV2Enabled
        ? [
            {
              title: '门店分层',
              dataIndex: 'shopOperateStage',
              key: 'shopOperateStage',
              width: 100,
              render: (value, record) => {
                return <span>{record.shopOperateStage || '-'}</span>;
              },
            },
          ]
        : []),
      {
        title: '基建任务进度',
        dataIndex: 'baseTaskProgress',
        key: 'baseTaskProgress',
        width: 125,
        render: (value, record) => {
          const { infraTaskProgressList } = record;

          // 优先使用新字段 infraTaskProgressList
          if (infraTaskProgressList && Array.isArray(infraTaskProgressList)) {
            const mustTask = infraTaskProgressList.find((item) => item.progressType === 'MUST');
            const optionalTask = infraTaskProgressList.find(
              (item) => item.progressType === 'OPTIONAL',
            );

            return (
              <div>
                <div>
                  必做：{mustTask?.completedTaskCount || 0}/{mustTask?.totalTaskCount || 0}
                </div>
                <div>
                  非必做：{optionalTask?.completedTaskCount || 0}/
                  {optionalTask?.totalTaskCount || 0}
                </div>
              </div>
            );
          }

          // 兜底逻辑：使用原有字段
          return (
            <span>
              {record.infrastructTaskCompletedTotalNum && record.infrastructTaskTotalNum
                ? `${record.infrastructTaskCompletedTotalNum}/${record.infrastructTaskTotalNum}`
                : ''}
            </span>
          );
        },
      },
      {
        title: '任务完成状态',
        dataIndex: 'shopTaskStatus',
        key: 'shopTaskStatus',
        width: 130,
        render: (value, record) => {
          return (
            <span className="task-status">
              <Badge
                status={record.shopTaskStatus === TASK_STATUS.COMPLETED ? 'success' : 'error'}
              />
              {record.shopTaskStatus === TASK_STATUS.COMPLETED ? '已完成' : '未完成'}
            </span>
          );
        },
      },
      ...(isInfrastructureWorkbenchQuery
        ? [
            {
              title: '五星门店',
              dataIndex: 'fiveStarShop',
              key: 'fiveStarShop',
              width: 200,
              render: (value, record) => {
                return <FiveStarShopCell record={record} />;
              },
            },
          ]
        : []),
      {
        title: '运维作业生效时间',
        dataIndex: 'signEffectiveTime',
        sorter: true,
        sortOrder: sortOrder('SIGN_EFFECTIVE_TIME'),
        width: 200,
        render: (val, rec) => {
          return (
            <div>
              <div>{val || '-'}</div>
              {rec.taskExpireTime ? (
                <div style={{ fontWeight: 500, color: '#111c67' }}>
                  <TaskCountdown maxDay={30} showOverTime={false} expireTime={rec.taskExpireTime} />
                </div>
              ) : null}
            </div>
          );
        },
      },
      {
        title: '年费订单明细',
        dataIndex: '_mingxi',
        key: '_mingxi',
        width: 300,
        render: (value, record) => {
          const { annualProductList } = record;
          if (!annualProductList?.length) {
            return '-';
          }

          return (
            <div>
              {getAnnualInfo(annualProductList?.[0], true)}
              {annualProductList?.length > 1 && (
                <Popover
                  trigger={['click']}
                  placement="leftTop"
                  overlayInnerStyle={{
                    maxHeight: 300,
                    overflowY: 'auto',
                  }}
                  content={annualProductList.slice(1).map((item, index) => {
                    return (
                      <>
                        {getAnnualInfo(item)}
                        {index !== annualProductList.length - 2 && <Divider />}
                      </>
                    );
                  })}
                  title="更多订单信息"
                >
                  <a>更多订单</a>
                </Popover>
              )}
            </div>
          );
        },
      },
      {
        title: '年费到期时间',
        dataIndex: 'shangHuTongExpireTime',
        key: 'shangHuTongExpireTime',
        width: 130,
        render: (value) => {
          return <span>{value || '-'}</span>;
        },
      },
      {
        title: '续签状态',
        dataIndex: 'shangHuTongStatus',
        key: 'shangHuTongStatus',
        width: 135,
        render: (value, record) => {
          const { shangHuTongStatus, shangHuTongSubStatus, payJumpList } = record;
          return (
            <div>
              {shangHuTongStatus
                ? `${
                    MERCHANT_EXPIRE_CONDITION_ENUM_OPTIONS.filter(
                      (item) => item.value === shangHuTongStatus,
                    )[0]?.title
                  }${shangHuTongSubStatus ? `-${MERCHANT_SUB_STATUS[shangHuTongSubStatus]}` : ''}`
                : '-'}
              {payJumpList && (
                <div className="pay-btn">
                  {payJumpList.map((item, index) => (
                    <div className="copy-text" key={index}>
                      <AfeClipboard
                        text={`${item.jumpUrl || ''}`}
                        onSuccess={() => message.success('复制成功')}
                      >
                        {item.buttonText}
                        <img
                          src="https://img.alicdn.com/imgextra/i1/O1CN01pOrhic1h6TTkUDcZV_!!6000000004228-2-tps-64-64.png"
                          alt=""
                        />
                      </AfeClipboard>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        },
      },
      // {
      //   title: '商户意向',
      //   dataIndex: 'merchantIntention',
      //   key: 'merchantIntention',
      //   width: 125,
      //   render: (value) => {
      //     const { intentionDegree, intentionDesc } = (value || [])?.[0] || {};
      //     return (
      //       <Tooltip
      //         title={
      //           intentionDegree
      //             ? `${
      //                 MERCHANT_INTENTION_OPTIONS.filter((item) => item.value === intentionDegree)[0]
      //                   ?.title
      //               }${intentionDesc && intentionDesc.trim() ? `-${intentionDesc}` : ''}`
      //             : ''
      //         }
      //       >
      //         <div className="merchant-intention">
      //           {intentionDegree
      //             ? `${
      //                 MERCHANT_INTENTION_OPTIONS.filter((item) => item.value === intentionDegree)[0]
      //                   ?.title
      //               }${intentionDesc && intentionDesc.trim() ? `-${intentionDesc}` : ''}`
      //             : '-'}
      //         </div>
      //       </Tooltip>
      //     );
      //   },
      // },
      // {
      //   title: '策略建议',
      //   dataIndex: 'suggestionLabels',
      //   key: 'suggestionLabels',
      //   width: 125,
      //   render: (value, record) => {
      //     return (
      //       <>
      //         {value &&
      //           value.length > 0 &&
      //           value.slice(0, 3).map((item: any, index) => (
      //             <Tooltip title={(item.labelContent || []).join('、')} key={index}>
      //               <div
      //                 className="suggestion-btn"
      //                 onClick={(e) => {
      //                   e.preventDefault();
      //                   handleSuggestionClick(item.labelCode, record);
      //                 }}
      //               >
      //                 {item.labelCode ? SUGGESTION_LABELS_NAME_ENUM[item.labelCode] : '-'}
      //               </div>
      //             </Tooltip>
      //           ))}
      //       </>
      //     );
      //   },
      // },
      // 运维人员
      {
        title: '运维人员',
        key: 'staffName',
        width: isInfrastructureWorkbenchQuery ? 180 : 120,
        render: (value) => {
          return (
            <div>
              <div>运维关系：{value?.staffName || '-'}</div>
              <div>基建运维：{value?.infrastructStaffName || '-'}</div>
              <div>新签关系：{value?.newSignName || '-'}</div>
            </div>
          );
        },
      },
      {
        title: '操作',
        key: 'operation',
        width: 190,
        fixed: 'right' as const,
        render: (value, record) => {
          const buttonJumpList = (record.buttonJumpList as IAction[]) || [];
          const confirmMap = {
            // REPORT:
            //   '提报后会任务商品、相册、推荐菜数据，提报后预计第二天采回数据，避免资源浪费确认使用再提报哦',
          };
          return (
            <div className="task-operation">
              {buttonJumpList.map((item) => {
                const confirmTip = confirmMap[item.buttonType];
                return (
                  <IfButtonShow button={item}>
                    <div style={{ textAlign: 'center' }}>
                      <Popconfirm
                        disabled={!confirmTip}
                        title={confirmTip}
                        overlayInnerStyle={{
                          maxWidth: 260,
                        }}
                        okText="确认"
                        placement="leftTop"
                        onConfirm={() => handleActionClick(item, record)}
                      >
                        <Link
                          disabled={item.greyButton}
                          to=""
                          onClick={(e) => {
                            e.preventDefault();
                            if (!confirmTip) {
                              handleActionClick(item, record);
                            }
                          }}
                        >
                          {item.buttonText}
                        </Link>
                      </Popconfirm>
                      {item.desc && (
                        <div style={{ color: '#999', fontSize: '.7em' }}>{item.desc}</div>
                      )}
                    </div>
                  </IfButtonShow>
                );
              })}
            </div>
          );
        },
      },
    ];

    const openDrawer = async (record) => {
      const { shopId, shopName, pid, merchantName } = record || {};
      const extInfo = await getMerchantReviewExtInfo(pid);
      const { sendMerchantNewsAvailable } = extInfo;
      setDrawerData({
        visible: true,
        defaultShop: {
          shop: {
            id: shopId,
            name: shopName,
          },
        },
        pid,
        merchantName,
        hasOptGroup: sendMerchantNewsAvailable,
      });
    };

    const handleClose = () => {
      message.destroy();
      setDrawerData({});
    };

    const openTaskDetailDrawer = (
      shopTask: Partial<AgentOperationShopRelationDTO & { tab: string }>,
    ) => {
      const { pid, shopId, shopQualityScore, tab } = shopTask || {};
      setTaskDetailData({
        pid,
        shopId,
        shopQualityScore,
        jumpSource: TASK_DETAIL_JUMP_SOURCE.SHOP_LIST,
        from: TASK_DETAIL_JUMP_FROM.SHOP_LIST,
        defaultTab: tab,
      });
      setTaskDetailDrawerVisible(true);
    };

    const handleTaskDetailDrawerClose = () => {
      setTaskDetailDrawerVisible(false);
    };

    const onRowClick = (record) => {
      setSelectedRowKey(record.shopId);
    };
    const { updateAndOpen, modalProps } = useModal<{ pid: string; shopId: string }>();

    useImperativeHandle(
      ref,
      () => ({
        openTaskDetailDrawer,
      }),
      [],
    );

    useEffect(() => {
      const handleOpenDrawer = (data) => {
        if (data.openDrawer === 'taskDetail') {
          openTaskDetailDrawer(data);
        }
      };
      if (urlQuery.openDrawer === 'taskDetail') {
        openTaskDetailDrawer(urlQuery);
      }
      emitter.on(EmitterEventMap.OpenDrawer, handleOpenDrawer);
      return () => {
        emitter.off(EmitterEventMap.OpenDrawer, handleOpenDrawer);
      };
    }, [urlQuery]);

    return (
      <div>
        <div className="task-list-content" ref={listRef}>
          <div>
            {/* <Form.Item name="shopScoreChangeCondition">
              <CheckboxGroup
                options={SHOP_SCORE_OPTIONS}
                onChange={() => {
                  fetchList();
                }}
              />
            </Form.Item> */}
            <Table
              rowKey="shopId"
              id="table"
              scroll={{ x: 1000 }}
              loading={listLoading}
              columns={columns.filter((item) => !!item)}
              dataSource={list}
              pagination={{
                showQuickJumper: true,
                showSizeChanger: true,
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                pageSizeOptions: [10, 20],
              }}
              onChange={onChange}
              onRow={(record) => ({ onClick: () => onRowClick(record) })}
              rowClassName={(record) =>
                record.shopId === selectedRowKey ? 'select-highlight-row' : ''
              }
            />
          </div>
        </div>
        {drawerData?.visible && (
          <WeeklyReportDrawer onClose={handleClose} {...drawerData} hasOptGroup />
        )}
        {taskDetailDrawerVisible && (
          <TaskDetailDrawer
            visible={taskDetailDrawerVisible}
            onClose={handleTaskDetailDrawerClose}
            {...taskDetailData}
          />
        )}
        <OrderModal {...modalProps} />
        {outBoundInfo?.shopInfo && (
          <OutBoundDetailDrawer
            visible
            {...outBoundInfo}
            onClose={() => {
              setOutBoundInfo({});
            }}
          />
        )}
        {PengModalEl}
      </div>
    );
  },
);

ShopTable.displayName = 'ShopTable';
