/* eslint-disable no-nested-ternary */
import Card from '@/components/card';
import { Tabs, TabsProps, Flex } from 'antd';
import { useEffect } from 'react';
import { TASK_TABPAN_ENUM, WORKBENCH_TYPE } from '@/common/const';
import styled from 'styled-components';
import { CaretDownFilled, CaretUpFilled } from '@ant-design/icons';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import Merchant from './merchant';
import Shop from './shop';
import { useRequest } from 'ahooks';
import { DoubleTableProvider, useDoubleTableStore } from './double-table-store';
import { parse } from 'query-string';
import { PageSPMKey, ModuleSPMKey, traceClick } from '@/utils/trace';
import { queryTaskEnums } from '@/services';

const StyledTabs = styled(Tabs)`
  background: #fff;
  .ant-tabs-nav {
    margin-bottom: 20px;
    &::before {
      border: none !important;
    }
  }
`;

const tabOptions: TabsProps['items'] = [
  { label: '商户列表', key: TASK_TABPAN_ENUM.MERCHANT },
  { label: '门店列表', key: TASK_TABPAN_ENUM.SHOP },
];

interface DoubleTableContentProps {
  taskPriorityV2Enabled?: boolean;
  isMerchantSalesFunnel?: boolean;
  workbenchType?: WORKBENCH_TYPE;
  isInfrastructureWorkbenchQuery?: boolean;
}

function DoubleTableContent(props: DoubleTableContentProps) {
  const {
    taskPriorityV2Enabled = true,
    workbenchType,
    isMerchantSalesFunnel = false,
    isInfrastructureWorkbenchQuery = false,
  } = props;
  const {
    curTab,
    setCurTab,
    isExpanded,
    setIsExpanded,
    addTask,
    merchantRef,
    shopRef,
    tabsRef,
    scrollToTabs,
  } = useDoubleTableStore();

  const { data: merchantTaskEnums } = useRequest(() => queryTaskEnums({ scene: 'MERCHANT_LIST' }), {
    onError: (error) => {
      // eslint-disable-next-line no-console
      console.error('获取商户任务枚举失败:', error);
    },
  });

  const { data: shopTaskEnums } = useRequest(() => queryTaskEnums({ scene: 'SHOP_LIST' }), {
    onError: (error) => {
      // eslint-disable-next-line no-console
      console.error('获取门店任务枚举失败:', error);
    },
  });

  // 处理任务数据点击事件
  useEffect(() => {
    const handleTaskDataClick = (data: { type: string; params: any }) => {
      const { type, params } = data;

      // 统一使用队列机制，简化逻辑
      const executeTask = async () => {
        if (type === TASK_TABPAN_ENUM.MERCHANT && merchantRef.current) {
          await merchantRef.current.search(params);
        } else if (type === TASK_TABPAN_ENUM.SHOP && shopRef.current) {
          await shopRef.current.search(params);
        }
        scrollToTabs();
      };

      addTask(type, executeTask);
      setCurTab(type);
    };

    // 处理打开抽屉事件
    const handleOpenDrawer = (data: any) => {
      if (data.openDrawer === 'taskDetail') {
        // 门店的任务详情抽屉
        const executeTask = async () => {
          // 等待组件渲染完成，然后调用门店组件的打开抽屉方法
          if (shopRef.current && shopRef.current.openTaskDetailDrawer) {
            shopRef.current.openTaskDetailDrawer(data);
          }
        };

        addTask(TASK_TABPAN_ENUM.SHOP, executeTask);
        setCurTab(TASK_TABPAN_ENUM.SHOP);
      } else if (data.openDrawer === 'warn') {
        // 商户的警告任务抽屉
        const executeTask = async () => {
          // 等待组件渲染完成，然后调用商户组件的打开抽屉方法
          if (merchantRef.current && merchantRef.current.openAlertTaskDrawer) {
            merchantRef.current.openAlertTaskDrawer(data);
          }
        };

        addTask(TASK_TABPAN_ENUM.MERCHANT, executeTask);
        setCurTab(TASK_TABPAN_ENUM.MERCHANT);
      }
    };

    emitter.on(EmitterEventMap.TaskDataClick, handleTaskDataClick);
    emitter.on(EmitterEventMap.OpenDrawer, handleOpenDrawer);
    return () => {
      emitter.off(EmitterEventMap.TaskDataClick, handleTaskDataClick);
      emitter.off(EmitterEventMap.OpenDrawer, handleOpenDrawer);
    };
  }, []);

  useEffect(() => {
    const urlQuery = parse(location.search);
    if (urlQuery?.openDrawer === 'taskDetail') {
      setCurTab(TASK_TABPAN_ENUM.SHOP);
    }
  }, []);

  const handleTabChange = (key: string) => {
    // D 区埋点：商户/门店列表.Tab 切换
    traceClick(PageSPMKey.首页, ModuleSPMKey['商户/门店列表.Tab切换'], {
      tab: key,
    });
    setCurTab(key);
  };

  return (
    <Card>
      <div ref={tabsRef}>
        <StyledTabs
          activeKey={curTab}
          items={tabOptions}
          onChange={handleTabChange}
          tabBarExtraContent={
            <Flex align="center" gap={8}>
              {/* Portal容器：动态显示搜索表单项和按钮 */}
              <div id="search-form-portal" style={{ display: 'contents' }} />
              <a
                style={{ display: 'flex', alignItems: 'center', gap: 4, userSelect: 'none' }}
                onClick={() => {
                  // D 区埋点：商户/门店列表.更多收起按钮
                  const newExpandedState = !isExpanded;
                  traceClick(PageSPMKey.首页, ModuleSPMKey['商户/门店列表.更多收起按钮'], {
                    isExpanded: newExpandedState,
                  });
                  setIsExpanded(newExpandedState);
                }}
              >
                {isExpanded ? (
                  <>
                    <div>收起</div>
                    <CaretUpFilled />
                  </>
                ) : (
                  <>
                    <div>更多</div>
                    <CaretDownFilled />
                  </>
                )}
              </a>
            </Flex>
          }
        />
        {curTab === TASK_TABPAN_ENUM.MERCHANT && (
          <Merchant
            ref={merchantRef}
            taskPriorityV2Enabled={taskPriorityV2Enabled}
            isMerchantSalesFunnel={isMerchantSalesFunnel}
            merchantTaskEnums={merchantTaskEnums}
            workbenchType={workbenchType}
          />
        )}
        {curTab === TASK_TABPAN_ENUM.SHOP && (
          <Shop
            ref={shopRef}
            taskPriorityV2Enabled={taskPriorityV2Enabled}
            shopTaskEnums={shopTaskEnums}
            workbenchType={workbenchType}
            isInfrastructureWorkbenchQuery={isInfrastructureWorkbenchQuery}
          />
        )}
      </div>
    </Card>
  );
}

interface DoubleTableProps {
  taskPriorityV2Enabled?: boolean;
  workbenchType?: WORKBENCH_TYPE;
  isMerchantSalesFunnel?: boolean;
  isInfrastructureWorkbenchQuery?: boolean;
}

export default function DoubleTable(props: DoubleTableProps) {
  const {
    taskPriorityV2Enabled = true,
    workbenchType,
    isMerchantSalesFunnel = false,
    isInfrastructureWorkbenchQuery = false,
  } = props;
  return (
    <DoubleTableProvider>
      <DoubleTableContent
        taskPriorityV2Enabled={taskPriorityV2Enabled}
        workbenchType={workbenchType}
        isMerchantSalesFunnel={isMerchantSalesFunnel}
        isInfrastructureWorkbenchQuery={isInfrastructureWorkbenchQuery}
      />
    </DoubleTableProvider>
  );
}
