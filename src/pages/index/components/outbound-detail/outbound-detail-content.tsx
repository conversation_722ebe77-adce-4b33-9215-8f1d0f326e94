import React, { useEffect, useState, useRef } from 'react';
import { OutboundListPanel } from '@/components/outbound-list-panel';
import { OutboundDetailPanel } from '@/components/outbound-detail-panel';
import { useMerchantDetail } from './hooks';
import './index.less';
import {
  OUTBOUND_LIST_PANEL_TAB_ENUM,
  OUTBOUND_VISIT_RECORD_TARGET_TYPE,
  PID_VISIT,
  TEL_VISIT,
} from '@/common/const';
import {
  VisitRecordForm,
  showVisitRecordFormPanel,
} from '@/components/visit-plan-agency/visit-record-form';
import { useGetCallRecordMissingVisit } from '@/components/visit-plan-agency/hooks';
import ShopCard from './shop-card';
import { VisitPlanAgency } from '@/components/visit-plan-agency';
import { echoWorkbench } from '@/common/echo';

interface IProps {
  pid?: string;
  merchantName?: string;
  visitVisible?: boolean;
  boundDetailTabKey?: string;
  shopInfo?: any;
  defaultInputInfo?: any;
}

export const OutBoundDetail: React.FC<IProps> = ({
  pid,
  merchantName,
  visitVisible,
  boundDetailTabKey,
  shopInfo: _shopInfo,
  defaultInputInfo,
}) => {
  const { loading, merchantDetail = {}, loadMerchantDetail } = useMerchantDetail();
  const { missVisitRecordList = [], loadMissVisitRecord } = useGetCallRecordMissingVisit();
  const [shopInfo, setShopInfo] = useState(_shopInfo || {});
  const [selectType, setSelectType] = useState<string>(
    OUTBOUND_LIST_PANEL_TAB_ENUM.MERCHANT_INFORMATION,
  );
  const visitRecordRef = useRef(null);

  useEffect(() => {
    if (visitVisible) {
      setTimeout(() => {
        showVisitRecordFormPanel({
          replenish: true,
          targetId: pid,
          targetType: OUTBOUND_VISIT_RECORD_TARGET_TYPE.MERCHANT,
          contactScene: PID_VISIT,
        });
      }, 100);
    }
  }, [pid, visitVisible]);

  useEffect(() => {
    if (!_shopInfo) {
      loadMerchantDetail({
        pid,
      });
    }
  }, [loadMerchantDetail, pid]);

  const handleListPanelClick = (value) => {
    if (value?.selectType) {
      setShopInfo({});
      setSelectType(value?.selectType);
    }
    if (value?.shopId) {
      setShopInfo(value);
      setSelectType('');
    }
  };

  const refreshVisitRecord = () => {
    setTimeout(() => {
      visitRecordRef?.current?.recordRefresh();
    }, 2000);
  };

  const handlePackUp = () => {
    loadMissVisitRecord();
    refreshVisitRecord && refreshVisitRecord();
  };

  const onCall = (callInfo: any) => {
    echoWorkbench?.call?.call(echoWorkbench, callInfo);
  };

  return (
    <>
      {/* <div className="outbound-detail-header">
        <Echo />
      </div> */}
      <div className="outbound-detail-content">
        <div className="outbound-detail-left-panel">
          <div className="left-panel-position">
            <VisitRecordForm handlePackUp={handlePackUp} defaultInputInfo={defaultInputInfo} />
            {_shopInfo ? (
              <>
                <VisitPlanAgency
                  missVisitRecordList={missVisitRecordList}
                  loadMissVisitRecord={loadMissVisitRecord}
                  scene={TEL_VISIT}
                  pid={pid}
                />
                <ShopCard shopInfo={_shopInfo} onCall={onCall} />
              </>
            ) : (
              <OutboundListPanel
                callOut={onCall}
                pid={pid}
                selectedId={shopInfo?.shopId}
                selectType={selectType}
                merchantDetail={merchantDetail}
                onListPanelClick={handleListPanelClick}
                loadMerchantDetail={loadMerchantDetail}
                missVisitRecordList={missVisitRecordList}
                loadMissVisitRecord={loadMissVisitRecord}
              />
            )}
          </div>
        </div>
        <div className="outbound-detail-right-panel">
          <OutboundDetailPanel
            pid={pid}
            shopInfo={shopInfo}
            merchantName={merchantName}
            merchantDetail={merchantDetail}
            merchantDetailLoading={loading}
            boundDetailTabKey={boundDetailTabKey}
            visitRecordRef={visitRecordRef}
          />
        </div>
      </div>
    </>
  );
};
