import Card from '@/components/card';
import { useAntdTable, useRequest } from 'ahooks';
import { Flex, List, Pagination, Radio, Spin, Tooltip } from 'antd';
import { useState } from 'react';
import styled from 'styled-components';
import TodoTask from '../task-group/todo-task';
import TaskCountdown from './TaskCountdown';
import { getTodoTaskList, pushTodoState } from '@/services';
import { ITodoTask } from './types';
import { jumpExternalUrl } from '@/common/utils';
import { ActionButtonType } from '@/constants';
import { useAction } from '@/components/server-controller/useAction';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { ModuleSPMKeyType } from '@/utils/trace/traceMap';
import MerchantRecommendation from '../merchant-recommendation';

enum TaskType {
  TODAY_TO_DO = 'TODAY_TO_DO',
  OVER_TIME = 'OVER_TIME',
  ALL = 'all',
  MERCHANT_RECOMMENDATION = 'MERCHANT_RECOMMENDATION',
}
const getTabLabel = (tab: string, desc: string) => {
  return (
    <Flex align="center" gap={5}>
      <div>{tab}</div>
      {desc ? (
        <Tooltip title={desc} placement="top" trigger={['hover']}>
          <QuestionCircleOutlined />
        </Tooltip>
      ) : null}
    </Flex>
  );
};
const taskTypeMap = {
  [TaskType.TODAY_TO_DO]: getTabLabel('今日必做任务', '今日即将到期的任务，需要高优关注'),
  [TaskType.OVER_TIME]: getTabLabel('超时任务', '已过期但未完成任务，还可继续处理'),
  [TaskType.ALL]: '全部待办任务',
  [TaskType.MERCHANT_RECOMMENDATION]: '今日建议沟通商家',
};

const getTab = (tab: TaskType) => {
  return {
    label: taskTypeMap[tab],
    value: tab,
  };
};

const TaskCard = styled(List.Item)`
  border-radius: 6px;
  background: rgba(255, 241, 240, 0.37);
  position: relative;
  height: 170px;
  /* width: 270px; */
  flex-basis: 270px;
  flex-grow: 0;
  flex-shrink: 0;
  border-block-end: none !important;
  cursor: pointer;
`;

const StyledTaskTitle = styled.div`
  font-size: 16px;
  margin-bottom: 4px;
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const StyledTaskDescription = styled.div`
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 4px;
`;

const StyledUrgentLabel = styled.div`
  position: absolute;
  left: 0px;
  top: 2px;
  font-weight: normal;
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  letter-spacing: 0em;
  color: #f5222d;
  display: inline-block;
  transform: rotate(0deg);
  border-radius: 0px 0px 0px 4px;
  background: #fff1f0;
  padding: 2px 6px;
`;

const StyledTaskLabel = styled.div`
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  display: flex;
  align-items: center;
  letter-spacing: 0em;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.85);
`;

const StyledTaskInfo = styled.div`
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  right: 0;
`;

const StyledRevenueLabel = styled.div`
  border-radius: 20px;
  max-width: 100%;
  opacity: 1;
  background: linear-gradient(
    90deg,
    rgba(55, 165, 255, 0.1) 0%,
    rgba(0, 209, 209, 0.1) 48%,
    rgba(85, 222, 140, 0.1) 100%
  );
  padding: 1px 8px;
  gap: 3px;
  font-size: 14px;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

interface TodoListProps {
  taskPriorityV2Enabled?: boolean;
  isGreyLoading?: boolean; // 灰度加载
}

export default function TodoList({
  taskPriorityV2Enabled = false,
  isGreyLoading = false,
}: TodoListProps) {
  const [curTab, setCurTab] = useState<TaskType>();
  const serverShowToday = useAction(ActionButtonType.今日必做)?.showButton;
  const serverShowOverTime = useAction(ActionButtonType.超时任务区块)?.showButton;

  // 这里只是看哪些tab有数据
  const { data: tabs, loading: initLoading } = useRequest(
    async () => {
      if (isGreyLoading) {
        return [];
      }

      const page = {
        pageNo: 1,
        pageSize: 2,
      };
      const _tabs = [];
      let _curTab = curTab;

      if (taskPriorityV2Enabled) {
        // 灰度开启时，直接显示今日建议沟通商家tab，不再调用OVER_TIME接口
        if (!_curTab) {
          _curTab = TaskType.MERCHANT_RECOMMENDATION;
        }
        _tabs.push(getTab(TaskType.MERCHANT_RECOMMENDATION));
      } else {
        // 灰度未开启时，才调用OVER_TIME接口
        const tabPromises = [TaskType.TODAY_TO_DO, TaskType.OVER_TIME].map(async (tab) => {
          const result = await getTodoTaskList({ taskType: tab, page });
          if (result?.dataList?.length) {
            if (
              (tab === TaskType.TODAY_TO_DO && serverShowToday) ||
              (tab === TaskType.OVER_TIME && serverShowOverTime)
            ) {
              if (!_curTab) {
                _curTab = tab;
              }
              _tabs.push(getTab(tab));
            }
          }
        });
        await Promise.all(tabPromises);
      }

      if (!_curTab) {
        _curTab = TaskType.ALL;
      }
      setCurTab(_curTab);
      _tabs.push(getTab(TaskType.ALL));
      return _tabs;
    },
    {
      refreshDeps: [taskPriorityV2Enabled, isGreyLoading],
    },
  );

  const {
    data: listData,
    loading,
    pagination,
    refresh: refreshList,
  } = useAntdTable(
    async (page) => {
      if (
        !tabs?.length ||
        curTab === TaskType.ALL ||
        curTab === TaskType.MERCHANT_RECOMMENDATION ||
        !curTab
      ) {
        return {
          list: [],
          total: 0,
        };
      }
      const result = await getTodoTaskList({
        taskType: curTab,
        page: {
          pageNo: page.current,
          pageSize: page.pageSize,
        },
      });
      return {
        list: result?.dataList || [],
        total: result?.pageInfo?.totalCount || 0,
      };
    },
    {
      refreshDeps: [curTab, tabs],
      defaultPageSize: 10,
      onSuccess: (res) => {
        const list = res?.list || [];
        if (list.length > 0) {
          list.forEach((task) => {
            // 根据不同 Tab 发送具体的数据埋点
            let dataEventKey: string;
            switch (curTab) {
              case TaskType.OVER_TIME:
                dataEventKey = '待办任务.紧急待办数据';
                break;
              case TaskType.TODAY_TO_DO:
                dataEventKey = '待办任务.今日必做数据';
                break;
              case TaskType.ALL:
                dataEventKey = '待办任务.全部待办数据';
                break;
              default:
                return; // 如果没有匹配的 Tab，跳过数据埋点
            }

            if (dataEventKey && ModuleSPMKey[dataEventKey]) {
              traceExp(PageSPMKey.首页, ModuleSPMKey[dataEventKey], {
                taskNo: task.taskNo,
                taskType: curTab,
              });
            }
          });
        }
      },
    },
  );
  const dataSource = listData?.list || [];

  const handleTaskClick = async (task: ITodoTask) => {
    // 根据当前 Tab 选择对应的埋点 key
    const clickEventMap: Record<TaskType, ModuleSPMKeyType> = {
      [TaskType.OVER_TIME]: '待办任务.紧急待办数据',
      [TaskType.TODAY_TO_DO]: '待办任务.今日必做数据',
      [TaskType.ALL]: '待办任务.全部待办数据',
      [TaskType.MERCHANT_RECOMMENDATION]: '待办任务.今日建议沟通商家',
    };

    const clickEventKey = clickEventMap[curTab] || '待办任务.任务项'; // 兜底

    // 点击埋点
    traceClick(PageSPMKey.首页, ModuleSPMKey[clickEventKey], {
      taskNo: task.taskNo,
    });

    if (!task.redirectUrl) return;
    if (task.taskStatus === 'INIT') {
      await pushTodoState(task.taskNo);
      refreshList();
    }
    jumpExternalUrl(task.redirectUrl);
  };

  const handleTabChange = (e: any) => {
    const tabValue = e.target.value as TaskType;
    let event;

    switch (tabValue) {
      case TaskType.TODAY_TO_DO:
        event = ModuleSPMKey['待办任务.今日必做Tab'];
        break;
      case TaskType.OVER_TIME:
        event = ModuleSPMKey['待办任务.紧急待办Tab'];
        break;
      case TaskType.ALL:
        event = ModuleSPMKey['待办任务.全部待办Tab'];
        break;
      case TaskType.MERCHANT_RECOMMENDATION:
        event = ModuleSPMKey['待办任务.今日建议沟通商家Tab'];
        break;
      default:
        event = undefined;
        break;
    }

    if (event) {
      traceClick(PageSPMKey.首页, event, {});
    }

    setCurTab(tabValue);
  };

  return (
    <Spin spinning={initLoading}>
      <Card
        title={
          <Radio.Group
            value={curTab}
            buttonStyle="solid"
            onChange={handleTabChange}
            optionType="button"
            options={tabs}
          />
        }
      >
        <div>
          {curTab === TaskType.ALL && <TodoTask taskPriorityV2Enabled={taskPriorityV2Enabled} />}
          {curTab === TaskType.MERCHANT_RECOMMENDATION && <MerchantRecommendation />}
          {curTab !== TaskType.ALL && curTab !== TaskType.MERCHANT_RECOMMENDATION && (
            <Spin spinning={loading}>
              <Flex gap={12} style={{ width: '100%', overflowX: 'auto' }}>
                {dataSource.map((item) => (
                  <TaskCard key={item.pid} onClick={() => handleTaskClick(item)}>
                    {item.taskStatusText ? (
                      <StyledUrgentLabel>{item.taskStatusText}</StyledUrgentLabel>
                    ) : null}

                    <div
                      className="task-content"
                      style={{ padding: '38px 16px 16px 16px', width: '100%' }}
                    >
                      <Tooltip title={item.mainShopName || item.pidName}>
                        <StyledTaskTitle>{item.mainShopName || item.pidName}</StyledTaskTitle>
                      </Tooltip>
                      <StyledTaskDescription>PID: {item.pid}</StyledTaskDescription>
                      <StyledTaskLabel>{item.taskName}</StyledTaskLabel>
                      <StyledTaskInfo>
                        <TaskCountdown expireTime={item.gmtExpired} />
                      </StyledTaskInfo>
                      {item.recommendAnalysis ? (
                        <Tooltip placement="topLeft" title={item.recommendAnalysis}>
                          <StyledRevenueLabel>{item.recommendAnalysis}</StyledRevenueLabel>
                        </Tooltip>
                      ) : null}
                    </div>
                  </TaskCard>
                ))}
              </Flex>
              <Pagination size="small" {...pagination} style={{ marginTop: 12 }} align="end" />
            </Spin>
          )}
        </div>
      </Card>
    </Spin>
  );
}
