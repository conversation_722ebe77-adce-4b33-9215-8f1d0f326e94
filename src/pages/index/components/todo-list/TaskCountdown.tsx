import { useCountDown } from 'ahooks';
import dayjs from 'dayjs';
import { useState } from 'react';
import styled from 'styled-components';

const StyledBadge = styled.div<{ timeNumberColor?: string }>`
  border-radius: 6px;
  padding: 2px 6px;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.65);
  background: #e6f2ff;
  .time-number {
    color: ${(props) => props.timeNumberColor || '#1a66ff'};
  }
  &.warning {
    background: #d8d8d8;
  }
  &.overtime-badge {
    background: #d8d8d8;
    border-radius: 0px 0px 0px 4px;
  }
`;

interface IProps {
  expireTime: number;
  style?: React.CSSProperties;
  overtimeStyle?: React.CSSProperties;
  showOverTime?: boolean;
  maxDay?: number;
  timeNumberColor?: string;
}

export default function TaskCountdown(props: IProps) {
  const {
    expireTime,
    style = {},
    overtimeStyle = {},
    showOverTime = true,
    maxDay,
    timeNumberColor,
  } = props;
  const [now] = useState(() => dayjs());
  const [countdownEnd, setCoundownEnd] = useState(false);
  const isOvertime = now.isAfter(dayjs(expireTime));

  const [countdown, formattedRes] = useCountDown({
    targetDate: expireTime,
    onEnd: () => {
      if (!isOvertime) {
        setCoundownEnd(true);
      }
    },
  });

  if (!expireTime || countdownEnd) {
    return null;
  }

  // 将 maxDay 转换为毫秒
  const maxDayMs = maxDay ? maxDay * 24 * 60 * 60 * 1000 : undefined;
  if (maxDayMs !== undefined && countdown > maxDayMs) {
    return null;
  }

  const { days } = formattedRes;

  if (isOvertime) {
    const overTimeDays = now.diff(dayjs(expireTime), 'day');
    if (!showOverTime) {
      return null;
    }
    return (
      <StyledBadge
        className="overtime-badge"
        style={{ ...style, ...overtimeStyle }}
        timeNumberColor={timeNumberColor}
      >
        已超时<span className="time-number">{overTimeDays}</span>天
      </StyledBadge>
    );
  }

  const { hours, minutes, seconds } = formattedRes;

  return (
    <StyledBadge style={style} timeNumberColor={timeNumberColor}>
      剩余
      {days > 0 && (
        <>
          <span className="time-number">{days}</span>天
        </>
      )}
      <span className="time-number">{hours}</span>时<span className="time-number">{minutes}</span>分
      <span className="time-number">{seconds}</span>秒
    </StyledBadge>
  );
}
