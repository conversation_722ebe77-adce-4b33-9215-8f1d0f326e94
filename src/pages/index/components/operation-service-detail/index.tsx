import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Result, Spin, Breadcrumb, Empty } from 'antd';
import { OperationServiceHeader } from '@/components/operation-service-header';
import { OperationServiceTask } from '@/components/operation-service-task';
import { useOperationServiceDetail } from './hooks';
import './index.less';
import { OutBoundDetail } from '../outbound-detail/outbound-detail-content';
import {
  MERCHANT_TASK_TYPE,
  OPERATING_TASK_JUMP_TYPE,
  OUTBOUND_DETAIL_PANEL_TAB_ENUM,
  TASK_DETAIL_JUMP_SOURCE,
  TASK_DETAIL_JUMP_FROM,
} from '@/common/const';
import WeeklyReportForm from '@/components/business-news/components/weekly-report-form-form';
import { TaskJumpDTO } from '@/_docplus/target/types/amap-sales-operation-client';
import { TaskDetailContent } from '@/components/task-detail/task-detail-content';

interface IProps {
  visible: boolean;
  pid: string;
  merchantName: string;
  onClose: () => void;
}

export const OperationServiceDetail: React.FC<IProps> = ({
  visible,
  pid,
  merchantName,
  onClose,
}) => {
  const {
    loading,
    error,
    operationServiceDetail,
    finishedList,
    unfinishedList,
    loadOperationServiceDetail,
  } = useOperationServiceDetail();
  const [subTaskDetailData, setSubTaskDetailData] = useState({
    subTitle: '',
    subType: '',
    subData: {},
  });
  const { subTitle, subType, subData } = subTaskDetailData;

  useEffect(() => {
    loadOperationServiceDetail({
      pid,
    });
  }, [loadOperationServiceDetail, pid]);

  const closeDrawer = () => {
    onClose();
  };

  const onTaskClick = (taskJumpDTO: TaskJumpDTO, taskType: string) => {
    const { buttonType } = taskJumpDTO;
    let title = '';
    let data = {};
    switch (buttonType) {
      case OPERATING_TASK_JUMP_TYPE.GO_FINISH:
        title = '任务详情';
        data = {
          pid,
          jumpSource: TASK_DETAIL_JUMP_SOURCE.MERCHANT_LIST,
          from:
            taskType === MERCHANT_TASK_TYPE.NIAN_FEI__EXTENSION
              ? TASK_DETAIL_JUMP_FROM.SERVICE_DETAIL_ANNUAL_FEE
              : TASK_DETAIL_JUMP_FROM.SERVICE_DETAIL_SHOP_INFRASTRUCT,
        };
        break;
      case OPERATING_TASK_JUMP_TYPE.GO_VISIT:
        title = '记拜访';
        data = {
          pid,
          merchantName,
          visitVisible: true,
          boundDetailTabKey: OUTBOUND_DETAIL_PANEL_TAB_ENUM.VISIT_RECORD,
        };
        break;
      case OPERATING_TASK_JUMP_TYPE.CALL_PHONE:
        title = '电话沟通';
        data = {
          pid,
          merchantName,
        };
        break;
      case OPERATING_TASK_JUMP_TYPE.XI_BAO:
        title = '生成喜报';
        data = {
          pid,
          merchantName,
        };
        break;
      default:
        title = '';
        data = {};
        break;
    }
    setSubTaskDetailData({
      subTitle: title,
      subType: buttonType,
      subData: data,
    });
  };

  // 点击一级导屑，二级标题不显示，回退运维服务详情页面，刷新页面
  const onBreadcrumbClick = () => {
    setSubTaskDetailData({
      subTitle: '',
      subType: '',
      subData: {},
    });
    loadOperationServiceDetail({ pid });
  };

  return (
    <Drawer
      width="80%"
      title={
        <Breadcrumb separator=">" className="operation-service-title">
          <Breadcrumb.Item>
            <span
              onClick={onBreadcrumbClick}
              className={subTitle ? 'operation-service-title-link' : 'operation-service-title'}
            >
              运维服务详情
            </span>
          </Breadcrumb.Item>
          {subTitle && (
            <Breadcrumb.Item className="operation-service-subtitle">{subTitle}</Breadcrumb.Item>
          )}
        </Breadcrumb>
      }
      placement="right"
      size="large"
      className="operation-service-detail-drawer"
      bodyStyle={{ padding: 0, overflow: 'auto' }}
      drawerStyle={{ backgroundColor: '#f5f5f5' }}
      onClose={closeDrawer}
      open={visible}
      destroyOnClose
    >
      <div className="operation-service-detail-wrapper">
        {!subType && (
          <Spin spinning={loading}>
            {!error && operationServiceDetail ? (
              <div className="operation-service-detail">
                <OperationServiceHeader data={operationServiceDetail} />
                <OperationServiceTask
                  finishedList={finishedList}
                  unfinishedList={unfinishedList}
                  onTaskClick={onTaskClick}
                />
              </div>
            ) : (
              <Empty description="暂无数据" className="empty" />
            )}
            {error && (
              <Result
                className="operation-service-result"
                title="网络出错了"
                subTitle="网络开小差了，请检查网络连接后重试"
                extra={
                  <Button
                    type="primary"
                    onClick={() => {
                      loadOperationServiceDetail({});
                    }}
                  >
                    点击重试
                  </Button>
                }
              />
            )}
          </Spin>
        )}
        {/* 去完成 */}
        {subType === OPERATING_TASK_JUMP_TYPE.GO_FINISH && <TaskDetailContent {...subData} />}
        {/* 电话沟通/记拜访 */}
        {(subType === OPERATING_TASK_JUMP_TYPE.CALL_PHONE ||
          subType === OPERATING_TASK_JUMP_TYPE.GO_VISIT) && <OutBoundDetail {...subData} />}
        {/* 喜报 */}
        {subType === OPERATING_TASK_JUMP_TYPE.XI_BAO && <WeeklyReportForm {...subData} />}
      </div>
    </Drawer>
  );
};

export default OperationServiceDetail;
