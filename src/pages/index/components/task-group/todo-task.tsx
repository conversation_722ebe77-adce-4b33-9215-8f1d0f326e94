import Card from '@/components/card';
import { TaskCard } from './components/taskCard';
import { Flex } from 'antd';
import { getTaskData } from '@/services';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { TASK_STATUS, TASK_TABPAN_ENUM, TASK_TYPE_ENUM } from '@/common/const';
import { IfButtonShow } from '@/components/server-controller/useAction';
import { ActionButtonType } from '@/constants';
import dayjs from 'dayjs';
import { isAgent, sendEvent } from '@/common/utils';
import { traceClick, traceExp, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { useStore } from '@/context/global-store';

interface IProps {
  style?: React.CSSProperties;
  taskPriorityV2Enabled?: boolean;
}
// const scoreCodesMap = {
//   BELOW_THREE: [0, 2.9],
//   BELOW_FOUR: [0, 3.9],
//   BELOW_SIX: [0, 5.9],
// };

export default function TodoTask(props: IProps) {
  const { style, taskPriorityV2Enabled = false } = props;
  const { viewer } = useStore() || {};
  const queryShopTaskNumsInfo = () =>
    getTaskData('商家分+续签', { viewOperatorId: viewer || undefined });
  // 获取商家分
  // const getMerchantTask = async () => {
  //   const res = await queryShopTaskNumsInfo();
  //   const list = res.merchantCore || [];
  //   return list.map((item) => ({
  //     ...item,
  //     label: `${item.property} ${item.rightSymbol || ''} ${item.rightBoundary || ''} ${
  //       item.unit || ''
  //     }`,
  //     value: item.value,
  //     desc: item.desc,
  //     codes: item.codes.map((code) => {
  //       return scoreCodesMap[code];
  //     }),
  //   }));
  // };
  // 获取续签任务
  const getRenewTask = async () => {
    const res = await queryShopTaskNumsInfo();
    const list = res.renewalTask || [];
    const mappedData = list.map((item) => ({
      ...item,
      label: `${item.property} ${item.rightSymbol || ''} ${item.rightBoundary || ''} ${
        item.unit || ''
      }`,
      value: item.value,
      desc: item.desc,
    }));

    // 添加数据曝光埋点
    mappedData.forEach((item) => {
      traceExp(PageSPMKey.首页, ModuleSPMKey['待办任务.续签任务'], {
        taskName: item.label || item.property,
      });
    });

    return mappedData;
  };
  // 获取广告任务
  const getAdTask = async () => {
    const res = await getTaskData('广告任务', { viewOperatorId: viewer || undefined });
    const list = res.adTasks || [];

    const mappedData = list.map((item) => ({
      ...item,
      label: item.taskName,
      value: item.taskCount,
      desc: item.taskDesc,
      url: item.pcJumpUrl,
      code: item.taskCode,
      disabled: !item.pcJumpUrl && !item.taskCode,
    }));

    console.log('映射后的广告任务数据:', mappedData);

    // 特别检查新增的任务
    const newTasks = mappedData.filter(
      (item) => item.code === 'AD_FIRST_CONTINUED' || item.code === 'AD_RAISE_DURATION',
    );
    console.log('新增的广告任务:', newTasks);

    // 添加数据曝光埋点
    mappedData.forEach((item) => {
      traceExp(PageSPMKey.首页, ModuleSPMKey['待办任务.广告任务'], {
        taskName: item.label,
      });
    });

    return mappedData;
  };
  // 获取服务预警 - 已移除，不再调用 queryMerchantWarnTaskNumsInfo 接口
  const getServerData = async () => {
    const res = await getTaskData('预警任务', { viewOperatorId: viewer || undefined });
    let list = res.warningNums || [];

    // 判断是否为渠道用户，只有渠道用户才显示门店留存未达标商户
    const isChannelUser = isAgent();
    if (!isChannelUser) {
      list = list.filter((item) => {
        // 过滤掉门店留存未达标商户任务（直营用户不显示）
        return !item.codes || !item.codes.includes(TASK_TYPE_ENUM.CONSUME_RETENTION_WARING);
      });
    }

    const mappedData = list.map((item) => ({
      ...item,
      label: `${item.property}${item.rightSymbol || ''}${item.rightBoundary || ''} ${
        item.unit || ''
      }`,
      value: item.value,
      desc: item.desc,
    }));

    // 添加数据曝光埋点
    mappedData.forEach((item) => {
      traceExp(PageSPMKey.首页, ModuleSPMKey['待办任务.预警任务'], {
        value: item.value,
        taskName: item.label,
      });
    });

    return mappedData;
  };
  // 获取基建任务
  const getInfrastructTask = async () => {
    const res = await queryShopTaskNumsInfo();
    const list = res.shopInfrastruct || [];
    console.log('基建任务原始数据:', res);
    console.log('shopInfrastruct数组:', list);

    const mappedData = list.map((item) => ({
      ...item,
      label: `${item.property} ${item.rightSymbol || ''} ${item.rightBoundary || ''} ${
        item.unit || ''
      }`,
      value: item.value,
      desc: item.desc,
      codes: item.codes,
    }));

    // 添加数据曝光埋点
    mappedData.forEach((item) => {
      traceExp(PageSPMKey.首页, ModuleSPMKey['待办任务.基建任务'], {
        value: item.value,
        taskName: item.label,
      });
    });

    return mappedData;
  };
  // 埋点处理函数
  const handleTaskClick = (taskType: string, data: any) => {
    // 发送点击埋点
    sendEvent('TASK_CARD_CLICK', 'CLK', {
      taskType,
      taskCode: data.code || data.codes?.[0],
      taskName: data.label || data.property,
      taskValue: data.value,
    });
    traceClick(PageSPMKey.首页, ModuleSPMKey[`待办任务.${taskType}`], {
      taskName: data.label,
    });
  };

  return (
    <Card title="待办任务" style={style}>
      <Flex gap={20} wrap>
        {/* <IfButtonShow buttonType={ActionButtonType.商家分}>
          <TaskCard
            style={{ width: 210 }}
            title="商家分概览"
            getData={getMerchantTask}
            onItemClick={(data) => {
              if (data.codes?.length) {
                emitter.emit(EmitterEventMap.TaskDataClick, {
                  type: TASK_TABPAN_ENUM.SHOP,
                  params: {
                    shopScoreCondition: data.codes[0],
                    filterOptRelation: true,
                  },
                });
              }
            }}
          />
        </IfButtonShow> */}
        <IfButtonShow buttonType={ActionButtonType.广告任务}>
          <TaskCard
            style={{ width: 390 }}
            title="广告任务"
            column={2}
            isWarning
            getData={getAdTask}
            refreshDeps={[viewer]}
            onItemClick={(data) => {
              // 添加埋点
              handleTaskClick('广告任务', data);

              if (data.code) {
                if (taskPriorityV2Enabled) {
                  // 命中灰度：使用新的todoTaskTypes
                  emitter.emit(EmitterEventMap.TaskDataClick, {
                    type: TASK_TABPAN_ENUM.MERCHANT,
                    params: {
                      todoTaskTypes: [data.code],
                    },
                  });
                } else {
                  // 没命中灰度：使用现有的adTaskLabels 这是old
                  emitter.emit(EmitterEventMap.TaskDataClick, {
                    type: TASK_TABPAN_ENUM.MERCHANT,
                    params: {
                      adTaskLabels: [data.code],
                    },
                  });
                }
              } else if (data.url) {
                window.open(data.url, '_blank');
              }
            }}
          />
        </IfButtonShow>
        <IfButtonShow buttonType={ActionButtonType.续签任务}>
          <TaskCard
            style={{ width: 210 }}
            title="续签任务"
            isWarning
            getData={getRenewTask}
            refreshDeps={[viewer]}
            onItemClick={(data) => {
              // 添加埋点
              handleTaskClick('续签任务', data);

              if (data.codes?.length) {
                if (taskPriorityV2Enabled) {
                  // 命中灰度：使用新的现有的逻辑
                  emitter.emit(EmitterEventMap.TaskDataClick, {
                    type: TASK_TABPAN_ENUM.MERCHANT,
                    params: {
                      todoTaskTypes: data.codes,
                    },
                  });
                } else {
                  // 没命中灰度：使用old门店逻辑
                  emitter.emit(EmitterEventMap.TaskDataClick, {
                    type: TASK_TABPAN_ENUM.SHOP,
                    params: {
                      shangHuTongExpireCondition: [...data.codes],
                    },
                  });
                }
              }
            }}
          />
        </IfButtonShow>
        {/* 根据灰度开关控制预警任务显示：未灰度时显示预警任务 */}
        {!taskPriorityV2Enabled && (
          <IfButtonShow buttonType={ActionButtonType.预警任务}>
            <TaskCard
              style={{ width: 210 }}
              title="预警任务"
              isWarning
              getData={getServerData}
              refreshDeps={[viewer]}
              onItemClick={(data) => {
                // 添加埋点
                handleTaskClick('预警任务', data);

                if (data.codes?.length) {
                  emitter.emit(EmitterEventMap.TaskDataClick, {
                    type: TASK_TABPAN_ENUM.MERCHANT,
                    params: {
                      filterOptRelation: true,
                      warningTaskLabels: data.codes[0],
                    },
                  });
                }
              }}
            />
          </IfButtonShow>
        )}
        <IfButtonShow buttonType={ActionButtonType.基建任务}>
          <TaskCard
            style={{ width: taskPriorityV2Enabled ? 390 : 210 }}
            title="基建任务"
            column={taskPriorityV2Enabled ? 2 : undefined}
            getData={getInfrastructTask}
            refreshDeps={[viewer]}
            onItemClick={(data) => {
              // 添加埋点
              handleTaskClick('基建任务', data);

              const code = data.codes?.[0];
              const today = dayjs();
              const tomorrow = dayjs().add(1, 'day');
              const extra: {
                taskExpireTime?: any[];
                nonCompliant?: string;
                shopLabelCondition?: string[];
                processingInfraTaskType?: string;
              } = {};

              if (code === 'TODAY_EXPIRE') {
                extra.taskExpireTime = [today, today];
              } else if (code === 'TOMORROW_EXPIRE') {
                extra.taskExpireTime = [tomorrow, tomorrow];
              } else if (code === 'SHOP_RISK_LABEL') {
                // 门店风控
                extra.shopLabelCondition = ['shop_risk_label'];
              } else if (data.codes?.length) {
                // 直接使用第一个code作为processingInfraTaskType
                extra.processingInfraTaskType = data.codes[0];
              }

              emitter.emit(EmitterEventMap.TaskDataClick, {
                type: TASK_TABPAN_ENUM.SHOP,
                params: {
                  shopTaskStatus: TASK_STATUS.INCOMPLETE,
                  filterOptRelation: true,
                  ...extra,
                },
              });
            }}
          />
        </IfButtonShow>
      </Flex>
    </Card>
  );
}
