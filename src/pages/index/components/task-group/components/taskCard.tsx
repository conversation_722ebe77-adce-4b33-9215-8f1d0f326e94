import React from 'react';
import { Flex, List, Tooltip } from 'antd';
import { QuestionCircleOutlined, CaretRightOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import classNames from 'classnames';
import { sendEvent } from '@/common/utils';
import { useRequest } from 'ahooks';

interface IProps {
  title?: string;
  isWarning?: boolean;
  column?: number;
  onItemClick?: (data: any) => void;
  getData: () => Promise<
    Array<{
      label: string;
      value: string;
      desc?: string;
      disabled?: boolean;
    }>
  >;
  style?: React.CSSProperties;
  refreshDeps?: React.DependencyList;
}
const Container = styled.div`
  padding: 5px 7px;
  border: 1px solid #e7e7e7;
  border-radius: 4px;
  &.warning {
    border-color: #ff4d4f;
  }
  min-width: 120px;
`;
const ListItem = styled(List.Item)`
  cursor: pointer;
  &:hover {
    background: #f5f5f5;
  }
`;
export const TaskCard: React.FC<IProps> = (props) => {
  const { title, isWarning, column = 1, onItemClick, getData, style = {}, refreshDeps } = props;
  const { error, refresh, data: dataList = [], loading } = useRequest(getData, {
    refreshDeps,
  });
  const handleItemClick = (data) => {
    // if (!data?.label) return; // 添加简单的数据校验
    onItemClick?.(data);
    sendEvent('TO_DO_TASK', 'CLK', {
      c1: data.label,
    });
  };
  // if (!dataList?.length && !error) {
  //   return null;
  // }
  return (
    <Container
      className={classNames({
        warning: isWarning && dataList.some((item) => item.value && !!Number(item.value)),
      })}
      style={style}
    >
      {title && <div style={{ marginBottom: 7 }}>{title}</div>}
      <List
        grid={{ column, gutter: 30 }}
        split={false}
        dataSource={error ? [] : dataList}
        locale={{
          emptyText: error ? (
            <span>
              数据请求失败, <a onClick={refresh}>点击重试</a>
            </span>
          ) : (
            <span>暂无数据</span>
          ),
        }}
        itemLayout="vertical"
        loading={loading}
        renderItem={(data) => (
          <ListItem
            style={{ display: 'flex', alignItems: 'center', fontSize: 13, marginBlockEnd: 7 }}
            extra={
              <Flex align="center" gap={5}>
                <div>{data.value}</div>
                {data.disabled ? null : <CaretRightOutlined />}
              </Flex>
            }
            onClick={() => handleItemClick?.(data)}
          >
            <div>
              <Flex gap={7} align="center">
                <div>{data.label}</div>
                {data.desc && (
                  <Tooltip title={data.desc}>
                    <QuestionCircleOutlined />
                  </Tooltip>
                )}
              </Flex>
            </div>
          </ListItem>
        )}
      />
    </Container>
  );
};
