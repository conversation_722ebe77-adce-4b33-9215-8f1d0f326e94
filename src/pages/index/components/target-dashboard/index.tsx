import React, { useEffect, useState } from 'react';
import { useAntdTable } from 'ahooks';
import { Badge, Button, Col, Form, Radio, Row, Select, Spin } from 'antd';
import Card from '@/components/card';
import { getTargetDashboardData } from './service';
import './index.less';
import {
  contrastPeriodOptions,
  EmergencyLevelColorOptions,
  economdata,
  indicatoData,
} from './common';
import { RightOutlined } from '@ant-design/icons';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { TASK_TABPAN_ENUM } from '@/common/const';
import BarChart from '@/components/charts/bar';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { useStore } from '@/context/global-store';

const tabOptions = [
  { label: '广告', value: 'ADVERTISE' },
  { label: '年费', value: 'MANUAL_FEE' },
];
const EmergencyLevelColorMap = {
  BLUE: '#165DFF',
  YELLOW: '#FAAD14',
  GREEN: '#52C41A',
};

const pageSize = 100;
export default function TargetDashboard({ mergedWithPrev = false }: { mergedWithPrev?: boolean }) {
  const { viewer } = useStore() || {};
  const [form] = Form.useForm();
  const [isDataUpdated, setIsDataUpdated] = useState(false);
  const queryType = Form.useWatch('queryType', form) || 'ADVERTISE';
  const indicatorType: string = Form.useWatch('indicatorType', form) || '';
  const indicatorOptions = queryType === 'ADVERTISE' ? economdata : indicatoData;
  const indicatorLabel = indicatorOptions.find((item) => item.value === indicatorType)?.label;
  const contrastPeriod = Form.useWatch('contrastPeriod', form);
  const contrastPeriodLabel = contrastPeriodOptions.find(
    (item) => item.value === contrastPeriod,
  )?.label;
  const EmergencyLevelOptionMap = {
    BLUE: '无预警健康商户',
    GREEN: queryType === 'ADVERTISE' ? '续充预警且意向度高' : '续签预警且意向度高',
    YELLOW: queryType === 'ADVERTISE' ? '续充预警且意向度低' : '续签预警且意向度低',
  };
  const [hasMore, setHasMore] = useState(false);
  const [dataList, setDataList] = useState([]);
  const [isScrolledToEnd, setIsScrolledToEnd] = useState(false);
  const [histogramTips, setHistogramTips] = useState('');
  const {
    search,
    loading,
    run: getData,
    pagination,
  } = useAntdTable(
    async (page) => {
      try {
        const params = form.getFieldsValue();
        if (!params?.queryType) {
          return {
            list: [],
            total: 0,
          };
        }
        const res = await getTargetDashboardData({
          ...params,
          viewOperatorId: viewer || undefined,
          page: {
            pageNo: page.current,
            pageSize: page.pageSize,
          },
        });
        // 处理接口返回数据
        setHistogramTips(res?.histogramTips);
        const rawList = Array.isArray(res.dataList) ? res.dataList : [];
        const processedList = rawList;
        if (page.current === 1) {
          setDataList(processedList);
          setIsDataUpdated(true);
        } else {
          setIsDataUpdated(false);
          setDataList((prev) => [...prev, ...processedList]);
        }
        setHasMore(processedList.length >= pageSize);
        return {
          list: processedList,
          total: processedList.length,
        };
      } catch (error) {
        return { list: [], total: 0 };
      }
    },
    {
      defaultPageSize: pageSize,
      refreshDeps: [viewer],
      onSuccess: (result) => {
        // 任务曝光埋点
        if (result.list && result.list.length > 0) {
          const exposureEvent =
            queryType === tabOptions[0].value
              ? ModuleSPMKey['柱状图.广告任务']
              : ModuleSPMKey['柱状图.年费任务'];

          result.list.forEach((item) => {
            traceExp(PageSPMKey.首页, exposureEvent, {
              taskNo: item.taskNo,
              pid: item.pid,
              indicatorType: form.getFieldValue('indicatorType'),
              contrastPeriod: form.getFieldValue('contrastPeriod'),
              emergencyLevel: item.emergencyLevel,
            });
          });
        }
      },
    },
  );

  const handleItemClick = (pid: string) => {
    const clickEvent =
      queryType === tabOptions[0].value
        ? ModuleSPMKey['柱状图.广告柱子']
        : ModuleSPMKey['柱状图.年费柱子'];

    // 添加点击埋点
    traceClick(PageSPMKey.首页, clickEvent, {
      pid,
      indicatorType: form.getFieldValue('indicatorType'),
      contrastPeriod: form.getFieldValue('contrastPeriod'),
      emergencyLevel: form.getFieldValue('emergencyLevel'),
    });

    emitter.emit(EmitterEventMap.TaskDataClick, {
      type: TASK_TABPAN_ENUM.MERCHANT,
      params: {
        pid,
      },
    });
  };

  const handleFormChange = (changedValues) => {
    // Tab切换点击埋点
    if ('queryType' in changedValues) {
      const event =
        changedValues.queryType === tabOptions[0].value
          ? ModuleSPMKey['柱状图.广告Tab']
          : ModuleSPMKey['柱状图.年费Tab'];
      traceClick(PageSPMKey.首页, event, {
        tabType: changedValues.queryType,
        indicatorType: form.getFieldValue('indicatorType'),
        contrastPeriod: form.getFieldValue('contrastPeriod'),
        emergencyLevel: form.getFieldValue('emergencyLevel'),
      });
    }

    // 续充意向度/续签意向度选择埋点
    if ('emergencyLevel' in changedValues) {
      const event =
        queryType === tabOptions[0].value
          ? ModuleSPMKey['柱状图.续充意向度选择']
          : ModuleSPMKey['柱状图.续签意向度选择'];
      traceClick(PageSPMKey.首页, event, {
        emergencyLevel: changedValues.emergencyLevel,
        queryType,
        indicatorType: form.getFieldValue('indicatorType'),
        contrastPeriod: form.getFieldValue('contrastPeriod'),
      });
    }

    // 指标选择埋点
    if ('indicatorType' in changedValues) {
      traceClick(PageSPMKey.首页, ModuleSPMKey['柱状图.指标选择'], {
        indicatorType: changedValues.indicatorType,
        queryType,
        contrastPeriod: form.getFieldValue('contrastPeriod'),
        emergencyLevel: form.getFieldValue('emergencyLevel'),
      });
    }

    // 对比周期选择埋点
    if ('contrastPeriod' in changedValues) {
      traceClick(PageSPMKey.首页, ModuleSPMKey['柱状图.对比周期选择'], {
        contrastPeriod: changedValues.contrastPeriod,
        queryType,
        indicatorType: form.getFieldValue('indicatorType'),
        emergencyLevel: form.getFieldValue('emergencyLevel'),
      });
    }

    search.submit();
    setHasMore(false); // 重置为初始状态
  };

  // 加载更多处理（保持不变）
  const handleLoadMore = () => {
    const nextPage = pagination.current + 1;
    getData({ ...pagination, current: nextPage });
  };
  const handleTipClick = () => {
    // trace('target-dashboard-text-click', { pid });
    // 广告去余额预警，年费去30天续签
    if (queryType === tabOptions[0].value) {
      // 广告
      emitter.emit(EmitterEventMap.TaskDataClick, {
        type: TASK_TABPAN_ENUM.MERCHANT,
        params: {
          adTaskLabels: ['BALANCE_WARNING'],
        },
      });
    } else {
      emitter.emit(EmitterEventMap.TaskDataClick, {
        type: TASK_TABPAN_ENUM.SHOP,
        params: {
          shangHuTongExpireCondition: ['EXPIRE_IN_THIRTY_DAY'],
        },
      });
    }
  };

  useEffect(() => {
    form.setFieldsValue({
      indicatorType: indicatorOptions?.[0]?.value,
      contrastPeriod: contrastPeriodOptions[0]?.value,
    });
  }, [queryType]);

  // if (!dataList.length) return null;
  return (
    <Spin spinning={loading}>
      <div>
        <Card
          style={{
            padding: '16px 24px',
            ...(mergedWithPrev && {
              borderRadius: '0 0 6px 6px',
            }),
          }}
        >
          <Form
            style={{ marginBottom: 20, display: 'block' }}
            layout="inline"
            form={form}
            onValuesChange={handleFormChange}
            colon
          >
            <Row gutter={16}>
              <Col>
                <Form.Item name="queryType" initialValue={tabOptions[0].value}>
                  <Radio.Group buttonStyle="solid" options={tabOptions} optionType="button" />
                </Form.Item>
              </Col>
              <Col>
                <Form.Item
                  label={queryType === 'ADVERTISE' ? '续充意向度' : '续签意向度'}
                  name="emergencyLevel"
                >
                  <Select style={{ width: 120 }} options={EmergencyLevelColorOptions} allowClear />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 20 }}>
              <Col>
                <Form.Item
                  label="指标"
                  name="indicatorType"
                  initialValue={indicatorOptions?.[0]?.value}
                >
                  <Select style={{ width: 150 }} options={indicatorOptions} />
                </Form.Item>
              </Col>
              <Col>
                <Form.Item
                  label="对比周期"
                  name="contrastPeriod"
                  initialValue={contrastPeriodOptions[0]?.value}
                >
                  <Select style={{ width: 120 }} options={contrastPeriodOptions} />
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <div className="data-source-Chart">
            <BarChart
              allowLegendClick={false}
              barWidth={30}
              legend={[
                { name: EmergencyLevelOptionMap.GREEN, color: EmergencyLevelColorMap.GREEN },
                { name: EmergencyLevelOptionMap.YELLOW, color: EmergencyLevelColorMap.YELLOW },
                { name: EmergencyLevelOptionMap.BLUE, color: EmergencyLevelColorMap.BLUE },
              ]}
              width="100%"
              updated={isDataUpdated}
              onScroll={(params) => {
                setIsScrolledToEnd(params.end === 100);
              }}
              isPercent={indicatorType.includes('RATE')}
              data={{
                xAxis: dataList.map((item) => ({
                  value: item.mainShopName || item.partnerName,
                  name: EmergencyLevelOptionMap[item.economicLevel],
                })),
                series: dataList.map((item) => {
                  const color = EmergencyLevelColorMap[item.economicLevel];
                  const { indicatorValuePercent } = item;
                  return {
                    ...item,
                    name: EmergencyLevelOptionMap[item.economicLevel],
                    value: item.indicatorValue,
                    itemStyle: {
                      color,
                    },
                    label: {
                      show: indicatorValuePercent && indicatorValuePercent != 0,
                      position: 'top',
                      formatter: () => {
                        return `${indicatorValuePercent}%`;
                      },
                    },
                  };
                }),
              }}
              onItemClick={(data) => {
                handleItemClick(data.pid);
              }}
              tooltip={{
                trigger: 'axis',
                renderMode: 'html',
                enterable: true,
                extraCssText: 'user-select: text;',
                showDelay: 0,
                position(point) {
                  // 计算tooltip位置，让它相对固定
                  const x = point[0] + 10;
                  const y = point[1] - 10;
                  const finalX = x;
                  const finalY = y;

                  return [finalX, finalY];
                },
                formatter: (params) => {
                  const record = params?.[0]?.data;
                  let value = record.indicatorValue || '-';
                  if (value !== '-' && indicatorType.includes('RATE')) {
                    value = `${value}%`;
                  }
                  return `<div>
                      <div>主店名: ${record.mainShopName}</div>
                      <div>PID: ${record.pid}</div>
                      <div>
                        ${indicatorLabel}：${value}
                      </div>
                      <div>
                        ${contrastPeriodLabel}：${
                    record.indicatorValuePercent && record.indicatorValuePercent != 0
                      ? `${record.indicatorValuePercent}%`
                      : '-'
                  }
                      </div>
                    </div>`;
                },
              }}
            />
            {hasMore && isScrolledToEnd ? (
              <div
                style={{
                  position: 'absolute',
                  right: 10,
                  top: '50%',
                  transform: 'translateY(-50%)',
                  zIndex: 1,
                  background: 'rgba(255,255,255,0.8)',
                  borderRadius: '50%',
                }}
              >
                <Button
                  shape="circle"
                  icon={<RightOutlined />}
                  onClick={handleLoadMore}
                  style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.15)' }}
                />
              </div>
            ) : null}
          </div>
          {histogramTips && (
            <div
              className="data-source-text"
              style={{ cursor: 'pointer' }}
              onClick={handleTipClick}
            >
              <Badge color={'blue'} style={{ marginRight: 5 }} />
              <span dangerouslySetInnerHTML={{ __html: histogramTips }} />
            </div>
          )}
        </Card>
      </div>
    </Spin>
  );
}
