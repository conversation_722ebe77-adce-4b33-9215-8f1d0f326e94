import React, { useEffect, useState } from 'react';
import { <PERSON>lex, Spin } from 'antd';
import MerchantRecommendationCard from '@/components/merchant-recommendation-card';
import styled from 'styled-components';
import { useRequest } from 'ahooks';
import { queryTodayRecommendTaskInfo } from '@/services';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { TASK_TABPAN_ENUM, RECOMMEND_TAG_TYPE } from '@/common/const';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { useStore } from '@/context/global-store';

const RecommendationSection = styled.div`
  margin-bottom: 24px;
`;

export default function MerchantRecommendation() {
  const { viewer } = useStore() || {};
  const [recommendationData, setRecommendationData] = useState<any[]>([]);

  const { data: recommendTaskData, loading } = useRequest(
    async () => {
      const result = await queryTodayRecommendTaskInfo({
        viewOperatorId: viewer || undefined,
      });
      return result;
    },
    {
      refreshDeps: [viewer],
    },
  );

  useEffect(() => {
    if (recommendTaskData && Array.isArray(recommendTaskData)) {
      const processedData = recommendTaskData.map((item) => ({
        countdownTime: item.recommendExpiredTime,
        merchantName: item.merchantName,
        recommendTagType: getRecommendTagType(item.recommendType),
        pid: item.merchantId,
        taskInfo: item.taskName,
        storeCount: item.shopCount,
        estimatedRevenue: item.valueText,
        taskType: item.taskType,
        merchantId: item.merchantId,
        recommendType: item.recommendType,
        taskNo: item.taskNo,
      }));

      setRecommendationData(processedData);

      // 添加商家曝光埋点
      processedData.forEach((item) => {
        traceExp(PageSPMKey.首页, ModuleSPMKey['待办任务.今日建议沟通商家'], {
          pid: item.merchantId,
          taskName: item.taskInfo,
          recommendType: item.recommendType,
          taskNo: item.taskNo,
        });
      });
    } else {
      setRecommendationData([]);
    }
  }, [recommendTaskData]);

  const getRecommendTagType = (recommendType: string) => {
    const typeMap: Record<string, string> = {
      SYSTEM: RECOMMEND_TAG_TYPE.SMART_RECOMMEND,
      REMIND: RECOMMEND_TAG_TYPE.SUPERVISOR_URGE,
      ARRANGE: RECOMMEND_TAG_TYPE.SUPERVISOR_ASSIGN,
    };
    return typeMap[recommendType] || RECOMMEND_TAG_TYPE.SMART_RECOMMEND;
  };

  const handleRecommendationClick = (item: any) => {
    // 商家点击埋点
    traceClick(PageSPMKey.首页, ModuleSPMKey['待办任务.今日建议沟通商家'], {
      pid: item.merchantId,
      taskName: item.taskInfo,
      recommendType: item.recommendType,
      taskNo: item.taskNo,
    });

    emitter.emit(EmitterEventMap.TaskDataClick, {
      type: TASK_TABPAN_ENUM.MERCHANT,
      params: {
        pid: item.merchantId,
      },
    });

    // 自动打开任务完成页面
    setTimeout(() => {
      emitter.emit(EmitterEventMap.OpenDrawer, {
        pid: item.merchantId,
        openDrawer: 'task', // 改为task，打开任务完成页面
        tab: 'task',
      });
    }, 500); // 延迟500ms确保列表页面加载完成
  };

  return (
    <RecommendationSection>
      <Spin spinning={loading}>
        {recommendationData.length > 0 ? (
          <Flex gap={16} style={{ width: '100%', overflowY: 'auto' }}>
            {recommendationData.map((item) => (
              <div key={item.pid} style={{ flex: '0 0 270px', height: '190px' }}>
                <MerchantRecommendationCard
                  countdownTime={item.countdownTime}
                  merchantName={item.merchantName}
                  recommendTagType={item.recommendTagType}
                  pid={item.pid}
                  taskInfo={item.taskInfo}
                  storeCount={item.storeCount}
                  estimatedRevenue={item.estimatedRevenue}
                  onClick={() => handleRecommendationClick(item)}
                />
              </div>
            ))}
          </Flex>
        ) : (
          !loading && (
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              暂无推荐商家数据
            </div>
          )
        )}
      </Spin>
    </RecommendationSection>
  );
}
