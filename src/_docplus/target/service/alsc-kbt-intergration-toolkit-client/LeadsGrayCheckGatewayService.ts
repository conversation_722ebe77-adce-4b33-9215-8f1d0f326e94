/**
 * Generated By @ali/alsc-docplus-cli (Ver.1.3.15)
 * @see https://yuque.antfin.com/alsc-gateway/docplus/pywu9g
 *
 * GroupId: com.alsc.kbt
 * ArtifactId: alsc-kbt-leads-center-gateway
 * Version: -
 * Branch: releases/20240822115531740_r_release_211629_alsc-kbt-leads-center-code
 */
import client from '@/_docplus/target/request/client';

const DOC_MODE = 'releases/20240822115531740_r_release_211629_alsc-kbt-leads-center-code,,';

class LeadsGrayCheckGatewayServiceClient implements Partial<any> {
  async checkGray(request) {
    const innerKey = `alsc-kbt-leads-center.LeadsGrayCheckGatewayService.checkGray:${DOC_MODE}`;

    const res = await client.fetch({
      apiKey: innerKey,
      params: [request],
    });
    return res;
  }
}

export default new LeadsGrayCheckGatewayServiceClient();
