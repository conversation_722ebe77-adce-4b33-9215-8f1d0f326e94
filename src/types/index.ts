import { ActionButtonType } from '@/constants';

export interface IAction {
  jumpType: string;
  jumpTypeNew: string;
  buttonText: string;
  buttonType: ActionButtonType;
  greyButton: boolean;
  jumpUrlList: string;
  showButton: boolean;
  client: string;
  jumpUrl: string;
  desc?: string;
}
export interface IActivity {
  activityId: string;
  originRechargeFee: string;
  additionalFee: string;
  expireDay: string;
  endTime: string;
  name: string;
}

// battle-map
/*
JOB-岗位，CHANNEL_OPT-渠道运维，COM-服务商公司（管理者页面）    
STAFF-小二（小二页面传）
*/
export enum EntityType {
  JOB = 'JOB', // JOB-岗位
  CHANNEL_OPT = 'CHANNEL_OPT', // CHANNEL_OPT-渠道运维
  COM = 'COM', // COM-服务商公司
  STAFF = 'STAFF', // STAFF-小二（小二页面传）
}
export interface FilterOptions {
  /** 组织架构（岗位）选择结果，对应原 deptId */
  jobIds?: string[];
  /** 渠道运维小二ID，对应原 aliCode */
  channelOptStaffIds?: string[];
  /** 服务商公司ID，对应原 companyId */
  channelCompanyIds?: string[];
  /** 实体类型 */
  entityType?: EntityType;
  /** 岗位层级 */
  jobLevel?: string;
  /** 是否是叶子节点 */
  leaf?: boolean;
}
