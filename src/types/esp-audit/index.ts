// ESP审核相关类型定义

export interface ShopStaffRelation {
  staffId: string; // 员工ID
  relationType: string; // 关系类型
  relationTypeDesc: string; // 关系类型描述
  firstOperator: boolean; // 是否为运维1
}

export interface MerchantScoreInfo {
  score: number; // 商家质量分
  scoreVersion: string; // 商家分版本，如 "1.0" 或 "3.0"
  outstandingLevel: boolean; // 是否通过
}

export interface ShopItemInspectInfo {
  inspectResult: 'PASS' | 'FAIL' | 'UNABLE'; // 质检结果：通过/不通过/无法质检
  inspectResultDesc: string; // 质检结果描述
  inspectDetail?: string | null; // 质检失败原因明细
  failReason?: string; // 商品质检失败原因
  shopId: string; // 门店ID
}

export interface ShopScoreInfo {
  score: number; // 商家质量分
  scoreVersion: string; // 商家分版本，如 "1.0" 或 "3.0"
  outstandingLevel: boolean; // 是否通过
  shopId: string; // 门店ID
  level?: number; // 商家分3.0版本的LV级别
}

// 上次审核信息
export interface LastAuditInfo {
  auditId: string;
  auditType: string;
  bizId: string;
  submitterId: string;
  submitterType: string;
  auditStatus: string;
  auditStatusDesc: string;
  collectShopName: string;
  optEspOrderDetailUrl: string;
  gmtTime: string;
  orderAuditType?: 'MANUAL' | 'SEMI_AUTO' | 'AUTO'; // 审核类型：人工质检、半自动质检、自动质检
}

export interface AuditBizOrder {
  orderNo?: string; // 业务流水ID
  bizId?: string; // 业务ID
  bizType?: string; // 业务类型
  gmtTime: string; // 业务时间
  operator?: string; // 操作人
  status?: string; // 状态
  orderNodeDesc: string; // 业务节点描述
  reason?: string; // 原因
  scoreDeadlineTime?: string; // 商家分截止时间
}

export interface AuditRecord {
  auditId: string;
  auditType: string;
  bizId: string;
  submitterId: string;
  submitterType: string;
  auditStatus: string;
  auditStatusDesc: string;
  auditBizOrders: AuditBizOrder[];
  collectShopName: string;
  optEspOrderDetailUrl: string;
  // 新增字段
  lastAuditInfo?: LastAuditInfo; // 上次审核信息
  auditPassCount: number; // 审核通过次数
  autoAuditResult: 'PASS' | 'FAIL' | 'UNABLE'; // 自动审核结果
  orderAuditType?: 'MANUAL' | 'SEMI_AUTO' | 'AUTO'; // 审核类型：人工质检、半自动质检、自动质检
  shopStaffRelations: ShopStaffRelation[]; // 人店关系数据
  shopScoreInfo: ShopScoreInfo; // 商家质量分
  shopItemInspectInfo?: ShopItemInspectInfo; // 门店商品质检信息（已废弃，不再使用）
  taskExecuteInfo?: string; // 任务执行信息
  show?: boolean; // 是否显示提报审核模块
}

// 卡点原因项
export interface BarrierReason {
  reason: string; // 阻塞原因
  reasonDesc: string; // 阻塞原因详情
}

export interface UnableSubmitReason {
  lackMaterial?: string; // 缺失材料
  processBarrier?: string; // 有卡点需解决
  otherReason?: string; // 其他原因
}

export interface AuditUnableCommitRecord {
  auditId: string;
  auditType: 'ESP_WOOS_ORDER_UNABLE_COMMIT';
  bizId: string;
  submitterId: string;
  submitterType: 'PROVIDER_STAFF';
  unableSubmitReason: UnableSubmitReason;
  barrierReasons: BarrierReason[]; // 卡点原因数据结构
}
