import request, { gdRequest } from './request';

/**
 * 门店仪表盘
 */
export interface QueryMerchantShopDashboardParams {
  viewOperatorId?: string;
  [key: string]: any;
}

export const queryMerchantShopDashboard = ({
  params,
}: {
  params?: QueryMerchantShopDashboardParams;
} = {}) => {
  return gdRequest('amap-sales-operation.FireMapsFacade.queryMerchantShopDashboard', {
    ...params,
  });
};

/**
 * 分阶段诊断数据
 */
export const queryPhasedDiagnosticsData = ({
  params,
}: {
  params?: any;
} = {}): Promise<{
  materials: Array<{ employeeId: string }>;
}> => {
  return gdRequest('amap-sales-operation.OptPhasedDiagnosticsFacade.queryPhasedDiagnosticsData', {
    ...params,
  });
};

/**
 * 柱状图去催办
 */
export const followUp = ({
  params,
}: {
  params?: any;
} = {}): Promise<{
  materials: Array<{ employeeId: string }>;
}> => {
  return gdRequest('amap-sales-operation.OptPhasedDiagnosticsFacade.followUp', {
    ...params,
  });
};

/**
 * 诊断建议展示(催办弹窗)
 */
export const queryEmployeeAnalysis = ({
  params,
}: {
  params?: any;
} = {}) => {
  return gdRequest('amap-sales-operation.AgentOperationQueryFacade.queryEmployeeAnalysis', {
    ...params,
  });
};

/**
 * 任务催办(催办弹窗)
 */
export const queryOptTodoTask = ({
  params,
}: {
  params?: any;
} = {}) => {
  return gdRequest('amap-sales-operation.OptTaskManageFacade.queryOptTodoTask', {
    ...params,
  });
};

/**
 * 任务枚举(催办弹窗)
 */
export const queryPriorityTaskType = ({
  params,
}: {
  params?: any;
} = {}) => {
  return gdRequest('amap-sales-operation.OptConfigQueryHsf.queryPriorityTaskType', {
    ...params,
  });
};

/**
 * 任务催办提交(催办弹窗)
 */
export const remindOptTask = ({
  params,
}: {
  params?: any;
} = {}) => {
  return gdRequest('amap-sales-operation.OptTaskManageFacade.remindOptTask', {
    ...params,
  });
};
