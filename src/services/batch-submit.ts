import { gdRequest } from './request';
import {
  IBatchSubmitShopListParams,
  IBatchSubmitShopListResponse,
  IQueryUnfinishedOptWoosBizOrderParams,
  IUnfinishedOptWoosBizOrderResponse,
  IBatchSubmitAndCreateEspOrderParams,
  IBatchSubmitAndCreateEspOrderResponse,
  IQueryShopCollectInfoParams,
  IShopCollectInfoResponse,
  IQueryTargetShopPageListParams,
  IQueryTargetShopPageListResponse,
} from '@/types/batch-submit';

// 运维工单门店信息查询（新增接口）
export const getBatchSubmitShopList = (
  params: IBatchSubmitShopListParams,
): Promise<IBatchSubmitShopListResponse> => {
  return gdRequest('amap-sales-operation.AgentOperationQueryFacade.queryOperationOrderShopList', {
    ...params,
    queryType: 'OptOrderShopInfo', // 查询类型固定为运维工单门店信息
    includeShopTaskProcess: true, // 包含门店任务进度
    appSource: 'xy-client', // 应用来源
    // 排序参数从params中获取，如果没有则使用默认值
    sortBy: params.sortBy || 'LAST_SUBMIT_TIME', // 支持动态排序字段
    sortType: params.sortType || 'asc', // 支持动态排序方向
  });
};

// 查询批量提报未完结流水
export const queryUnfinishedOptWoosBizOrder = (
  params: IQueryUnfinishedOptWoosBizOrderParams,
): Promise<IUnfinishedOptWoosBizOrderResponse> => {
  return gdRequest(
    'amap-sales-operation.ShopInfraManageFacade.queryUnfinishedOptWoosBizOrder',
    params,
  );
};

// 批量提报任务创建
export const batchSubmitAndCreateEspOrder = (
  params: IBatchSubmitAndCreateEspOrderParams,
): Promise<IBatchSubmitAndCreateEspOrderResponse> => {
  return gdRequest(
    'amap-sales-operation.ShopInfraManageFacade.batchSubmitAndCreateEspOrder',
    params,
  );
};

// 装修素材提报记录查询（复用现有接口）
export const queryShopCollectInfo = (
  params: IQueryShopCollectInfoParams,
): Promise<IShopCollectInfoResponse> => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.queryShopCollectInfo', params);
};

// 高德门店名称模糊分页查询
export const queryTargetShopPageList = (
  params: IQueryTargetShopPageListParams,
): Promise<IQueryTargetShopPageListResponse> => {
  return gdRequest(
    'amap-sales-operation.ShopInfraManageFacade.queryTargetShopPageList',
    params,
  );
};
