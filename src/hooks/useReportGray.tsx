import { useRequest } from 'ahooks';
import { queryPageConfig } from '@/services';

export const useReportGray = () => {
  const {
    data: isGrayHit,
    loading,
    error,
  } = useRequest(async () => {
    const result = await queryPageConfig();
    console.log({ result });
    const _isGrayHit =
      result?.specialValues?.some(
        (item) => item.key === 'COMMON_GREY_HIT_XIBAO_NEW_VERSION' && item.keyValue === 'true',
      ) || false;
    return _isGrayHit;
  });

  return {
    isGrayHit: error ? false : !!isGrayHit,
    hasReady: !loading,
  };
};
