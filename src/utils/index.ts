import dayjs from 'dayjs';
import numeral from 'numeral';

export const isAmapXy = () => /xy.amap/.test(location.host);
/**
 * 时间戳（毫秒）转化为标准时间格式
 */
export function getFullTime(timeStamp: any) {
  if (!timeStamp) return;
  const stamp = new Date(timeStamp);
  const time = dayjs(stamp).format('YYYY-MM-DD HH:mm:ss');
  return time;
}

/**
 * 格式化金额，以分为单位
 */
export const formatMoneyToNumber = (price: string) => {
  return price === null || price === undefined || Number.isNaN(price) || price === ''
    ? undefined
    : String(numeral(price).multiply(100).value());
};

export const isMpa = () => {
  return !!location.pathname.match('sale-webapp');
};

/**
 * 判断是否为BUC用户登录
 */
export const isBucLogin = () => {
  return window.APP?.operatorType === 'BUC';
};

export const fixed2 = (num: number | any, fixed = 2) => {
  if (typeof num === 'number') {
    // 判断数字的小数部分长度
    const decimalPart = num.toString().split('.')[1];
    if (decimalPart && decimalPart.length > fixed) {
      // 如果有小数部分且超过两位，则保留两位小数
      return parseFloat(num.toFixed(2)).toString();
    } else {
      // 否则，直接返回原数，不做修改
      return num.toString();
    }
  }
  return num;
};

export const getKpFlag = (contact) => {
  const { isKp, kpFlag } = contact;
  if (isKp) {
    // eslint-disable-next-line no-nested-ternary
    return isKp === '1' ? 'YES' : isKp === '2' ? 'UNKNOWN' : 'NO';
  } else {
    return kpFlag === true ? 'YES' : 'NO';
  }
};

/**
 *
 * 返回 MB 单位
 * @param size {单位: MB}
 * @returns
 */
export const getFileSize = (size: number) => {
  return 1024 * 1024 * size;
};

/**
 * 整数
 * @param num
 * @returns
 */
export const formatCount = (num: string | number | undefined) => {
  return numeral(num).format('0,0');
};
