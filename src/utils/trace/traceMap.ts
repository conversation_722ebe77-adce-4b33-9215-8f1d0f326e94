export enum PageSPMKey {
  首页 = 'xy-task-pc-home',
}

/**
 * 埋点事件映射表 - 统一管理所有埋点事件名称
 * 使用中文描述，映射到具体的英文事件名
 * 通过 traceExp() 和 traceClick() 方法来区分曝光和点击事件
 * 采用模块.功能的层级结构
 */
export enum ModuleSPMKey {
  // 核心任务完成率模块
  '核心任务完成率' = 'core_task_completion',
  '核心任务完成率.任务' = 'core_task_completion.task',
  '核心任务完成率.卡片' = 'core_task_completion.card',

  // 柱状图模块
  '柱状图' = 'target_dashboard',
  '柱状图.广告任务' = 'target_dashboard.ad_task',
  '柱状图.年费任务' = 'target_dashboard.annual_task',
  '柱状图.广告Tab' = 'target_dashboard.ad_tab',
  '柱状图.年费Tab' = 'target_dashboard.annual_tab',
  '柱状图.广告柱子' = 'target_dashboard.ad_chart',
  '柱状图.年费柱子' = 'target_dashboard.annual_chart',
  '柱状图.续充意向度选择' = 'target_dashboard.recharge_intention_select',
  '柱状图.续签意向度选择' = 'target_dashboard.renewal_intention_select',
  '柱状图.指标选择' = 'target_dashboard.indicator_select',
  '柱状图.对比周期选择' = 'target_dashboard.contrast_period_select',

  // 待办任务模块
  '待办任务' = 'todo_list',
  '待办任务.任务' = 'todo_list.task',
  '待办任务.广告任务' = 'todo_list.ad_task',
  '待办任务.续签任务' = 'todo_list.renewal_task',
  '待办任务.预警任务' = 'todo_list.warning_task',
  '待办任务.基建任务' = 'todo_list.infrastructure_task',
  '待办任务.紧急待办Tab' = 'todo_list.urgent_tab',
  '待办任务.紧急待办数据' = 'todo_list.urgent_data',
  '待办任务.今日必做Tab' = 'todo_list.today_tab',
  '待办任务.今日必做数据' = 'todo_list.today_data',
  '待办任务.全部待办Tab' = 'todo_list.all_tab',
  '待办任务.全部待办数据' = 'todo_list.all_data',
  '待办任务.任务项' = 'todo_list.task_item',
  '待办任务.今日建议沟通商家Tab' = 'todo_list.merchant_recommendation_tab',
  '待办任务.今日建议沟通商家' = 'todo_list.merchant_recommendation',

  // 企微任务模块
  '企微任务' = 'qw_task',
  '企微任务.任务' = 'qw_task.task',
  '企微任务.自动发送' = 'qw_task.auto_send',
  '企微任务.发送至企微群' = 'qw_task.send_to_qw',

  // 仪表盘卡片模块
  '仪表盘卡片' = 'dashboard_cards',

  // 绩效目标模块
  '绩效目标' = 'performance',
  '绩效目标.指标卡片点击' = 'performance.indicator_card',
  '绩效目标.日期选择' = 'performance.date_select',

  // 门店仪表盘模块
  '门店仪表盘' = 'store_dashboard',
  '门店仪表盘.环比选择' = 'store_dashboard.cycle_ratio_select',
  '门店仪表盘.门店周期点击' = 'store_dashboard.store_cycle_click',

  // 商户列表模块
  '商户列表' = 'merchant_list',
  '商户列表.商户曝光' = 'merchant_list.merchant_exposure',
  '商户列表.筛选' = 'merchant_list.filter',
  '商户列表.操作项' = 'merchant_list.operation',
  '商户列表.查询按钮' = 'merchant_list.query_button',
  '商户列表.重置按钮' = 'merchant_list.reset_button',

  // 门店列表模块
  '门店列表' = 'shop_list',
  '门店列表.筛选' = 'shop_list.filter',
  '门店列表.标签筛选' = 'shop_list.label_filter',
  '门店列表.操作项' = 'shop_list.operation',
  '门店列表.批量提报按钮' = 'shop_list.batch_submit_button',
  '门店列表.查询按钮' = 'shop_list.query_button',
  '门店列表.重置按钮' = 'shop_list.reset_button',

  // 商户/门店列表模块（共用）
  '商户/门店列表.Tab切换' = 'double_table.tab_switch',
  '商户/门店列表.更多收起按钮' = 'double_table.expand_toggle',

  // 喜报模块
  '喜报' = 'business_news',
  '喜报.AI智能分析' = 'business_news.ai_analysis',
  '喜报.字段切换' = 'business_news.field_toggle',
  '喜报.明细' = 'business_news.detail',
  '喜报.下载明细表' = 'business_news.download_table',
  '喜报.下载图片' = 'business_news.download_image',
  '喜报.发到企微群' = 'business_news.send_to_qw',

  // 拜访记录模块
  '拜访记录' = 'visit_record',
  '拜访记录.商户详情Tab' = 'visit_record.merchant_detail_tab',
  '拜访记录.拜访记录Tab' = 'visit_record.history_tab',
  '拜访记录.复盘数据Tab' = 'visit_record.replay_tab',
  '拜访记录.记拜访' = 'visit_record.create',
  '拜访记录.tab切换' = 'visit_record.tab_switch',
  '拜访记录.继续填写' = 'visit_record.continue_fill',

  // 投放方案模块
  '投放方案' = 'ad_plan',
  '投放方案.模块曝光' = 'ad_plan.module_exposure',
  '投放方案.操作' = 'ad_plan.operation',
  '投放方案.查询' = 'ad_plan.query',
  '投放方案.新增' = 'ad_plan.create',
  '投放方案.删除' = 'ad_plan.delete',
  '投放方案.关闭修改' = 'ad_plan.close_edit',
  '投放方案.移除门店' = 'ad_plan.remove_shop',
  '投放方案.下载' = 'ad_plan.download_excel',
  '投放方案.下载图片' = 'ad_plan.download_image',
  '投放方案.发送至企微群' = 'ad_plan.send_to_qw',
  '投放方案.切换预览模式' = 'ad_plan.switch_preview_mode',
  // 基建任务模块
  '基建任务' = 'infrastructure_task',
  '基建任务.模块曝光' = 'infrastructure_task.module_exposure',
  '基建任务.任务卡片点击' = 'infrastructure_task.task_card_click',
  '基建任务.装修素材提报' = 'infrastructure_task.decoration_material_submit',

  // 年费续签任务模块
  '年费续签任务' = 'annual_renewal_task',
  '年费续签任务.模块曝光' = 'annual_renewal_task.module_exposure',
  '年费续签任务.任务卡片点击' = 'annual_renewal_task.task_card_click',
  '年费续签任务.任务详情曝光' = 'annual_renewal_task.task_detail_exposure',
  '年费续签任务.任务操作' = 'annual_renewal_task.task_operation',

  // 任务详情弹窗模块
  '任务详情' = 'task_detail',
  '任务详情.关闭按钮' = 'task_detail.close_button',
  '任务详情.Tab切换' = 'task_detail.tab_switch',
  '任务详情.门店选择' = 'task_detail.shop_select',
  '任务详情.SOP文档点击' = 'task_detail.sop_doc_click',
  '任务详情.SOP一页纸点击' = 'task_detail.sop_one_page_click',

  // 商户详情模块
  '商户详情' = 'merchant_detail',
  '商户详情.任务曝光' = 'merchant_detail.task_exposure',
  '商户详情.高优任务曝光' = 'merchant_detail.high_priority_task_exposure',
  '商户详情.去完成任务按钮点击' = 'merchant_detail.to_do_task_btn',

  // 新商户详情模块
  '新商户详情' = 'merchant_detail_v2',
  '新商户详情.任务曝光' = 'merchant_detail.task_exposure_v2',
  '新商户详情.去完成任务按钮点击' = 'merchant_detail.to_do_task_btn_v2',
  '新商户详情.门店列表曝光' = 'merchant_detail.shop_list_exposure_v2',
  '新商户详情.重新质检按钮点击' = 'merchant_detail.reinspection_btn_v2',
  '新商户详情.无法完成去反馈按钮点击' = 'merchant_detail.unable_complete_feedback_btn_v2',

  '商家分任务' = 'merchant_score_task',
  '视角切换' = 'viewer_switch',
  门店详情 = 'shop_detail',
  机器人 = 'robotBlock_Robot.arouseRobot',

  // 批量提报模块
  '批量提报' = 'batch_submit',
  '批量提报.按钮点击' = 'batch_submit.button_click',
  '批量提报.弹窗曝光' = 'batch_submit.modal_exposure',
  '批量提报.门店选择步骤' = 'batch_submit.shop_select_step',
  '批量提报.门店选择.筛选' = 'batch_submit.shop_select.filter',
  '批量提报.门店选择.选择门店' = 'batch_submit.shop_select.select_shop',
  '批量提报.门店选择.下一步' = 'batch_submit.shop_select.next_step',
  '批量提报.门店选择.取消' = 'batch_submit.shop_select.cancel',
  '批量提报.提报审核步骤' = 'batch_submit.submit_review_step',
  '批量提报.提报审核.设置目标门店' = 'batch_submit.submit_review.set_target',
  '批量提报.提报审核.提交' = 'batch_submit.submit_review.submit',
  '批量提报.提报审核.取消' = 'batch_submit.submit_review.cancel',
  '批量提报.提交成功' = 'batch_submit.submit_success',

  '装修素材提报任务' = 'purchase_task',
  '装修素材提报任务_任务选择' = 'purchase_task.task_select',
  '装修素材提报任务_提报任务' = 'purchase_task.submit_task',
  '装修素材提报任务_点击去发布' = 'purchase_task.click_to_publish',

  'AI素材推荐' = 'ai_material_recommend',
  'AI素材推荐.模块曝光' = 'ai_material_recommend.module_exposure',
  'AI素材推荐.去提交' = 'ai_material_recommend.to_submit',
  'AI素材推荐.提交确认' = 'ai_material_recommend.submit_confirm',

  // 工作台切换模块
  '工作台切换' = 'workbench_switch',

  // 基建工作台数据概览卡片
  '数据概览卡片' = 'infra_workbench_overview_card',
  'AI素材推荐.商户选择' = 'ai_material_recommend.merchant_select',
  'AI素材推荐.门店选择' = 'ai_material_recommend.shop_select',
  'AI素材推荐.相册二级Tab切换' = 'ai_material_recommend.album_sub_tab_switch',
  'AI素材推荐.状态筛选' = 'ai_material_recommend.status_filter',
  'AI素材推荐.名称筛选' = 'ai_material_recommend.name_filter',
  'AI素材推荐.批量提交按钮' = 'ai_material_recommend.batch_submit_button',
  'AI素材推荐.企微消息按钮' = 'ai_material_recommend.qw_message_button',
  'AI素材推荐.表格行点击' = 'ai_material_recommend.table_row_click',
  'AI素材推荐.分页切换' = 'ai_material_recommend.pagination_change',
}

export type ModuleSPMKeyType = keyof typeof ModuleSPMKey;
