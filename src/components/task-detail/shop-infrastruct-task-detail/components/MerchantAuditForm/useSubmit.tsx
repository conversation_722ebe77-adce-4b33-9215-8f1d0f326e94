import { createEspOrder, queryLocalOptEspWoosOrder } from '@/services';
import { useRequest } from 'ahooks';
import { Modal, message } from 'antd';
import { FormInstance } from 'antd/es/form';
import React from 'react';
import { SubmitPermissionResult } from '../../utils';

interface IProps {
  shopId: string;
  form: FormInstance;
  submitPermission: SubmitPermissionResult;
  isRejection: boolean;
  setExpanded: (expanded: boolean) => void;
  refreshAuditData: (auditId?: string) => void;
  auditStatus?: string;
}

// 质检结果类型
interface QualityCheckResult {
  merchantScore: {
    passed: boolean;
    version: string;
    score?: number;
    level?: string;
  };
  orderAuditType?: any;
  modalConfig?: {
    title: string;
    content: React.ReactNode;
  } | null;
}

export function useSubmit(props: IProps) {
  const { shopId, form, submitPermission, setExpanded, refreshAuditData } = props;

  // 高亮关键词的函数
  const highlightKeywords = (text: string): React.ReactNode => {
    if (!text) return text;

    const keywords = ['1-3天再提报', '低于', '不通过'];
    let result: React.ReactNode = text;

    keywords.forEach((keyword) => {
      if (typeof result === 'string' && result.includes(keyword)) {
        const parts = result.split(keyword);
        result = parts.reduce((acc: React.ReactNode[], part, index) => {
          if (index === 0) return [part];
          return [...acc, <span style={{ color: '#ff4d4f' }}>{keyword}</span>, part];
        }, []);
      }
    });

    return result;
  };

  // 获取质检结果
  const getQualityCheckResult = async (): Promise<QualityCheckResult> => {
    try {
      const res = await queryLocalOptEspWoosOrder({ shopId });
      const auditData = res;
      const { shopScoreInfo, orderAuditType, shopScoreInspectInfo } = auditData;

      let modalConfig: { title: string; content: React.ReactNode } | null = null;

      // 处理后端返回的弹窗数据
      if (shopScoreInspectInfo) {
        const { title, reasonInfos } = shopScoreInspectInfo;

        if (title && Array.isArray(reasonInfos) && reasonInfos.length > 0) {
          modalConfig = {
            title: '提示',
            content: (
              <div>
                <div>
                  {highlightKeywords(title)}
                  <div style={{ height: '8px' }} />
                </div>
                {reasonInfos.map((item, index) => (
                  <div key={index}>
                    <div>{highlightKeywords(item.reason)}</div>
                    <div style={{ whiteSpace: 'pre-line' }}>{highlightKeywords(item.detail)}</div>
                    <div>{highlightKeywords(item.suggest)}</div>
                  </div>
                ))}
              </div>
            ),
          };
        }
      }

      // 处理商家分质检结果
      const merchantScore = {
        passed: shopScoreInfo?.outstandingLevel === true,
        version: shopScoreInfo?.scoreVersion || '1.0',
        score: shopScoreInfo?.score,
        level:
          shopScoreInfo?.scoreVersion && shopScoreInfo?.scoreVersion >= '3.0'
            ? shopScoreInfo?.grade
            : undefined,
      };

      return {
        merchantScore,
        orderAuditType,
        modalConfig,
      };
    } catch (error) {
      return {
        merchantScore: { passed: true, version: '1.0' },
        orderAuditType: undefined,
        modalConfig: null,
      };
    }
  };

  // ESP工单提报处理逻辑
  const { runAsync: handleSubmit, loading: submitting } = useRequest(
    async () => {
      // 1. 获取质检结果
      const qualityCheckResult = await getQualityCheckResult();

      // 2. 显示质检结果弹窗
      if (qualityCheckResult.modalConfig) {
        await new Promise((resolve, reject) => {
          Modal.confirm({
            title: qualityCheckResult.modalConfig!.title,
            content: qualityCheckResult.modalConfig!.content,
            okText: '坚持提报',
            cancelText: '取消',
            onOk: () => resolve(true),
            onCancel: () => reject(new Error('取消提报')),
          });
        });
      }

      // 3. 运维多次提报警告
      if (submitPermission.needWarning && submitPermission.warningMessage) {
        await new Promise((resolve, reject) => {
          Modal.confirm({
            title: '提示',
            content: submitPermission.warningMessage,
            okText: '确认',
            cancelText: '取消',
            onOk: () => resolve(true),
            onCancel: () => reject(new Error('取消提报')),
          });
        });
      }

      // 4. 表单数据处理
      let collectShopName = '';
      if (props.auditStatus === 'AUDIT_PASS') {
        collectShopName = form.getFieldValue('collectShopName')?.trim() || '';
      } else {
        const { collectShopName: name = '', hasNoMerchant } = form.getFieldsValue();
        collectShopName = name.trim();
        if (!hasNoMerchant && !collectShopName) {
          message.error('请填写目标门店名称');
          return;
        }
        if (hasNoMerchant) {
          collectShopName = '';
        }
      }

      // 5. 提交ESP工单
      const auditId = await createEspOrder({
        shopId,
        collectShopName,
        targetShopId: shopId,
      });

      // 6. 显示成功提示
      message.success('提报成功');

      // 7. 收起卡片并刷新数据
      setExpanded(false);
      setTimeout(() => refreshAuditData(auditId), 1000);
    },
    { manual: true, debounceWait: 500, debounceLeading: true, debounceTrailing: false },
  );

  return { handleSubmit, submitting };
}
