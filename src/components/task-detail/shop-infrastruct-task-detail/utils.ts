import * as EspAuditTypes from '@/types/esp-audit';

/**
 * 用户角色枚举
 */
export enum UserRole {
  OPERATOR_1 = 'OPERATOR_1', // 运维1
  OPERATOR_2 = 'OPERATOR_2', // 运维2
  UNKNOWN = 'UNKNOWN', // 未知角色
}

/**
 * 提报权限检查结果
 */
export interface SubmitPermissionResult {
  disableSubmit: boolean; // 是否禁用提报（待审核和审核处理中状态时禁用）
  needWarning: boolean; // 是否需要警告提示
  warningMessage?: string; // 警告信息
}

/**
 * 根据人店关系数据判断用户角色
 * @param shopStaffRelationInfo 人店关系数据
 * @returns 用户角色
 */
export const getUserRole = (
  shopStaffRelationInfo: EspAuditTypes.ShopStaffRelation[] = [],
): UserRole => {
  console.log('shopStaffRelationInfo:', shopStaffRelationInfo);
  // 查找当前用户的角色信息
  // 注意：这里假设当前用户的staffId可以通过某种方式获取
  // 实际实现时可能需要从用户信息接口获取当前用户ID

  for (const relation of shopStaffRelationInfo) {
    if (relation.firstOperator) {
      return UserRole.OPERATOR_1;
    }
  }

  // 如果有人店关系但不是运维1，则认为是运维2
  if (shopStaffRelationInfo.length > 0) {
    return UserRole.OPERATOR_2;
  }

  return UserRole.UNKNOWN;
};

/**
 * 检查提报权限
 * @param userRole 用户角色
 * @param hasSubmitted 是否已经提交过
 * @param auditStatus 审核状态
 * @returns 权限检查结果
 */
export const checkSubmitPermission = (
  userRole: UserRole,
  hasSubmitted: boolean,
  auditStatus?: string,
): SubmitPermissionResult => {
  // 待审核和审核处理中状态时，按钮禁用
  const isAuditing = auditStatus === 'WAIT_AUDIT' || auditStatus === 'AUDIT_PROCESSING';
  // 质检状态通过后，可再次进行提报
  const isAuditPassed = auditStatus === 'AUDIT_PASS';

  switch (userRole) {
    case UserRole.OPERATOR_1:
      // 运维1：一个门店只可提报一次，但质检通过后可再次提报
      return {
        disableSubmit: isAuditing,
        needWarning: false,
      };

    case UserRole.OPERATOR_2:
      // 运维2：不限次数，但第二次之后每次提报时弹窗提示
      return {
        disableSubmit: isAuditing,
        needWarning: hasSubmitted && !isAuditPassed, // 已提交过且非通过状态才需要警告
        warningMessage: '审核资源有限，请按需、合理提交审核',
      };

    default:
      // 未知角色：允许提报但不做特殊限制
      return {
        disableSubmit: isAuditing,
        needWarning: false,
      };
  }
};
