import React, { useEffect, useState } from 'react';
import { ShopSelect } from '../shop-select';
import './index.less';
import { getBaseRequestParam, isAmapAgent, sendEvent } from '@/common/utils';
import { TaskCard } from '../task-card';
import { Button, Empty, Result, Row, message, Tabs } from 'antd';
import { TASK_DETAIL_TABS, TASK_STATUS, PAGE_STATUS } from '@/common/const';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import {
  AgentOperationDetailDTO,
  AgentOperationShopRelationDTO,
} from '@/_docplus/target/types/amap-sales-operation-client';
import { useStore } from '@/context/global-store';
import PurchaseRequestForm from './PurchaseRequestForm';
import MerchantAuditForm from './components/MerchantAuditForm';
import { ModuleSPMKey, PageSPMKey, traceExp } from '@/utils/trace';
import { MerchantScoreModal } from '../shop-select/MerchantScoreModal';
import styled from 'styled-components';

const StyledTabs = styled(Tabs)`
  .ant-tabs-nav {
    background: #fff;
    padding-left: 16px;
  }
`;

const GROUP_ORDER = [
  'ALL',
  '全部',
  '基础信息',
  '商品',
  '内容丰富度',
  '信息完整度',
  '经营推广力',
  '营销/交易表现',
  '服务',
  '附加项',
];

const filterDetailList = (taskDetailDTOList: any[]) => {
  const incompleteData = (taskDetailDTOList || []).filter(
    (item) => item.taskDetailStatus === TASK_STATUS.INCOMPLETE,
  );
  const completeData = (taskDetailDTOList || []).filter(
    (item) => item.taskDetailStatus === TASK_STATUS.COMPLETED,
  );
  return [incompleteData, completeData];
};

interface IProps {
  pid: string;
  visible: boolean;
  jumpSource: string;
  shop?: AgentOperationShopRelationDTO;
  shopList: AgentOperationShopRelationDTO[];
  data: any;
}
export const ShopInfrastructTaskDetail: React.FC<IProps> = ({
  pid,
  shopList: shops,
  data,
  visible,
  jumpSource,
  shop: defaultShop,
}) => {
  const [shopList, setShopList] = useState(shops);
  const [shop, setShop] = useState<AgentOperationShopRelationDTO>(defaultShop);

  // 添加 state 保存接口返回的数据
  const [taskData, setTaskData] = useState<any>(data || {});

  const {
    taskDetailDTOList,
    taskGroupMap,
    shopScoreLevel,
    shopTotalScore,
    merchantScoreVersion,
    nextGradeScoreGap,
    baseTotalScore,
    baseActualScore,
    shopScoreLevelStandardList,
    nextShopScoreLevel,
    merchantScoreVersionDegrade,
  } = taskData;

  const [status, setStatus] = useState<PAGE_STATUS>(
    taskDetailDTOList?.length > 0 ? PAGE_STATUS.SUCCESS : PAGE_STATUS.EMPTY,
  );
  const [activeGroup, setActiveGroup] = useState<string>('ALL');
  const [modalVisible, setModalVisible] = useState(false);
  const is33 = !isAmapAgent();

  // 获取当前选中门店的商家分
  const currentShopInfo = shopList?.find((item) => item.shopId === shop?.shopId);
  const shopQualityScore = shopTotalScore;

  // 获取分组数据
  const getGroupedTasks = () => {
    if (!taskDetailDTOList || !taskGroupMap) {
      return [];
    }

    // 获取分组顺序的索引
    const getGroupOrder = (groupKey: string, tagName: string) => {
      const keyIndex = GROUP_ORDER.indexOf(groupKey);
      const nameIndex = GROUP_ORDER.indexOf(tagName);
      if (keyIndex !== -1) return keyIndex;
      if (nameIndex !== -1) return nameIndex;
      return GROUP_ORDER.length; // 未匹配到的排在最后
    };

    // 按照指定顺序排序
    return Object.keys(taskGroupMap)
      .map((groupKey) => {
        const groupInfo = taskGroupMap[groupKey];
        const groupTasks =
          groupKey === 'ALL'
            ? taskDetailDTOList
            : taskDetailDTOList.filter((task) => {
                const taskTag = task.tag;
                return taskTag === groupKey || taskTag === groupInfo?.tagName;
              });

        const [incompleteTasks, completeTasks] = filterDetailList(groupTasks);

        return {
          key: groupKey,
          name: groupInfo.tagName || groupKey,
          groupActualScore: groupInfo.groupActualScore || 0,
          groupTotalScore: groupInfo.groupTotalScore || 0,
          achievementRate: groupInfo.achievementRate ?? null,
          incompleteTasks,
          completeTasks,
          order: getGroupOrder(groupKey, groupInfo?.tagName || ''),
        };
      })
      .sort((a, b) => a.order - b.order)
      .map(({ order, ...rest }) => rest);
  };

  const groupedTasks = getGroupedTasks();

  // 渲染任务列表的公共函数
  const renderTaskList = (incompleteTasks: any[], completeTasks: any[]) => {
    return (
      <>
        {incompleteTasks.length > 0 && (
          <>
            <div className="task-title" style={{ marginTop: 16 }}>
              未完成任务（{incompleteTasks.length}项）
            </div>
            <Row gutter={[16, 16]} style={{ marginTop: '8px' }}>
              {incompleteTasks.map((detail) => (
                <TaskCard
                  detailInfo={detail}
                  tabKey={TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK}
                  key={detail.taskName}
                  shopId={shop?.shopId}
                />
              ))}
            </Row>
          </>
        )}
        {completeTasks.length > 0 && (
          <>
            <div className="task-title" style={{ marginTop: 16 }}>
              已完成任务（{completeTasks.length}项）
            </div>
            <Row gutter={[16, 16]} style={{ marginTop: '8px' }}>
              {completeTasks.map((detail) => (
                <TaskCard
                  detailInfo={detail}
                  tabKey={TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK}
                  key={detail.taskName}
                  shopId={shop?.shopId}
                />
              ))}
            </Row>
          </>
        )}
      </>
    );
  };

  // 同步 props data 的变化到 taskData
  useEffect(() => {
    if (data) {
      setTaskData(data);
      // 当数据更新时，检查当前选中的分组是否存在，不存在则重置为第一个可用分组
      if (data.taskGroupMap) {
        const availableGroups = Object.keys(data.taskGroupMap);
        if (availableGroups.length > 0 && !availableGroups.includes(activeGroup)) {
          setActiveGroup(availableGroups[0]);
        }
      }
    }
  }, [data]);

  useEffect(() => {
    if (visible) {
      if (status === PAGE_STATUS.EMPTY) {
        fetchList(shop);
      }
      sendEvent(TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK, 'EXP');
      traceExp(PageSPMKey.首页, ModuleSPMKey['基建任务.模块曝光'], {
        shopId: shop?.shopId,
      });
    } else {
      setStatus(PAGE_STATUS.EMPTY);
    }
  }, [visible]);

  const { viewer } = useStore();
  const fetchList = (shop: AgentOperationShopRelationDTO) => {
    const { shopId } = shop || {};
    if (!shopId) {
      return;
    }
    setStatus(PAGE_STATUS.LOADING);
    const param = {
      viewOperatorId: viewer || undefined,
      taskDetailScene: TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK,
      pid,
      jumpSource,
      shopId,
      ...getBaseRequestParam(),
    };
    service
      .queryAgentOperationDetail(param)
      .then((res) => {
        const { success, data: responseData, msgInfo: resultMessage } = res || {};
        if (!success) {
          message.error(resultMessage);
          setStatus(PAGE_STATUS.ERROR);
          return;
        }
        if (!responseData) {
          setStatus(PAGE_STATUS.EMPTY);
          return;
        }
        setTaskData(responseData);
        if (responseData.taskDetailDTOList?.length > 0) {
          setStatus(PAGE_STATUS.SUCCESS);
        } else {
          setStatus(PAGE_STATUS.EMPTY);
        }
      })
      .catch((e) => {
        console.error('任务详情请求失败: ', e);
        message.error(e?.res?.resultMsg);
        setStatus(PAGE_STATUS.ERROR);
      });
  };

  const handleShopChange = (shop: AgentOperationShopRelationDTO) => {
    setShop(shop);
    setActiveGroup('ALL'); // 切换门店时重置为"全部"分组
    fetchList(shop);
  };

  const handleShopList = (value: AgentOperationShopRelationDTO[]) => {
    setShopList(value);
    if (value?.length > 0 && !shop) {
      setShop(value[0]);
      if (status === PAGE_STATUS.EMPTY) {
        fetchList(value[0]);
      }
    }
  };

  return (
    <div className="task-tab-container">
      <div className="action-container">
        <ShopSelect
          pid={pid}
          shop={shop}
          shopList={shopList}
          onShopChange={handleShopChange}
          onShopList={handleShopList}
          merchantScoreVersion={merchantScoreVersion}
          nextGradeScoreGap={nextGradeScoreGap}
          baseTotalScore={baseTotalScore}
          baseActualScore={baseActualScore}
          shopScoreLevelStandardList={shopScoreLevelStandardList}
          shopScoreLevel={shopScoreLevel}
          shopTotalScore={shopTotalScore}
          nextShopScoreLevel={nextShopScoreLevel}
          merchantScoreVersionDegrade={merchantScoreVersionDegrade}
        />
      </div>
      <PurchaseRequestForm shopId={shop?.shopId} />
      {/* 提报审核 */}
      {is33 && <MerchantAuditForm shopId={shop?.shopId} />}

      {status === PAGE_STATUS.SUCCESS && (
        <div>
          <div className="task-title" style={{ marginBottom: 16 }}>
            <span style={{ marginRight: 16, fontSize: 20, fontWeight: 500 }}>任务明细</span>
            {shop && (
              <span className="task-score">
                门店商家分：{shopQualityScore ? `${shopQualityScore}分` : '-'}
                {merchantScoreVersion === '3.1' &&
                  merchantScoreVersionDegrade !== true &&
                  shopScoreLevel &&
                  shopTotalScore && (
                    <>
                      <span
                        className="merchant-score-level"
                        onClick={() => setModalVisible(true)}
                        style={{
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          color: 'red',
                          marginLeft: 8,
                        }}
                      >
                        ({shopScoreLevel})
                      </span>
                      {shopScoreLevel !== nextShopScoreLevel &&
                        nextGradeScoreGap &&
                        nextShopScoreLevel && (
                          <span style={{ marginLeft: 8 }}>
                            差{nextGradeScoreGap}分到达{nextShopScoreLevel}
                          </span>
                        )}
                    </>
                  )}
              </span>
            )}
          </div>
          {groupedTasks.length > 0 ? (
            <StyledTabs
              className="task-group-tabs"
              activeKey={activeGroup}
              onChange={setActiveGroup}
              items={groupedTasks.map((group) => {
                const isActive = activeGroup === group.key;
                const textColor = isActive ? '#1a66ff' : '#666';
                return {
                  key: group.key,
                  label: (
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        border: '1px solid #f0f0f0',
                        padding: '4px',
                      }}
                    >
                      <div style={{ color: textColor }}>{group.name}</div>
                      <div style={{ color: textColor, fontSize: 12, marginTop: 2 }}>
                        ({group.groupActualScore}分/{group.groupTotalScore}分,达成
                        {group.achievementRate != null
                          ? `${(group.achievementRate * 100).toFixed(1)}%`
                          : '-'}
                        )
                      </div>
                    </div>
                  ),
                  children: <div>{renderTaskList(group.incompleteTasks, group.completeTasks)}</div>,
                };
              })}
            />
          ) : (
            (() => {
              const [incompleteTasks, completeTasks] = filterDetailList(taskDetailDTOList || []);
              return renderTaskList(incompleteTasks, completeTasks);
            })()
          )}
        </div>
      )}
      {status === PAGE_STATUS.EMPTY && <Empty style={{ margin: '16px' }} />}
      {status === PAGE_STATUS.ERROR && (
        <Result
          className="task-result"
          title="网络出错了"
          subTitle="网络开小差了，请检查网络连接后重试"
          extra={
            <Button
              type="primary"
              onClick={() => {
                fetchList(shop);
              }}
            >
              点击重试
            </Button>
          }
        />
      )}
      {merchantScoreVersion === '3.1' && merchantScoreVersionDegrade !== true && (
        <MerchantScoreModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          merchantScoreVersion={merchantScoreVersion || ''}
          shopScoreLevel={shopScoreLevel || ''}
          nextGradeScoreGap={nextGradeScoreGap || ''}
          baseTotalScore={baseTotalScore || ''}
          baseActualScore={baseActualScore || ''}
          shopScoreLevelStandardList={shopScoreLevelStandardList || []}
        />
      )}
    </div>
  );
};
