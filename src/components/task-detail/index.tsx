import React from 'react';
import './index.less';
import { TaskDetailContent } from './task-detail-content';
import { Drawer } from 'antd';
import { PageSPMKey, ModuleSPMKey, traceClick } from '@/utils/trace';

interface IProps {
  visible: boolean;
  pid: string;
  jumpSource: string;
  from: string;
  shopId?: string;
  shopQualityScore?: string;
  onClose: () => void;
  [key: string]: any;
}

export const TaskDetailDrawer: React.FC<IProps> = ({
  visible,
  pid,
  jumpSource,
  from,
  shopId = '',
  shopQualityScore = '',
  onClose,
  ...rest
}) => {
  const closeDrawer = () => {
    // D 区埋点：任务详情.关闭按钮
    traceClick(PageSPMKey.首页, ModuleSPMKey['任务详情.关闭按钮'], {
      pid,
      shopId,
      jumpSource,
    });
    onClose();
  };

  return (
    <Drawer
      title="任务详情"
      placement="right"
      size="large"
      width="950px"
      bodyStyle={{ height: '90%', padding: '0 15px' }}
      onClose={closeDrawer}
      open={visible}
      destroyOnClose
    >
      <TaskDetailContent
        pid={pid}
        jumpSource={jumpSource}
        from={from}
        {...rest}
        shopId={shopId}
        shopQualityScore={shopQualityScore}
      />
    </Drawer>
  );
};
