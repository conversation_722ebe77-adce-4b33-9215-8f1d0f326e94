import React from 'react';
import { Button, Modal } from 'antd';
import TableDetails from './tableList';

interface DetailsModalProps {
  isModalOpenProps: boolean;
  onCancel: () => void;
  detailInfoList?: any;
}

const DetailsModal: React.FC<DetailsModalProps> = (props) => {
  const { isModalOpenProps, onCancel, detailInfoList } = props;

  const handleCancel = () => {
    onCancel();
  };

  return (
    <>
      <Modal
        width={600}
        title={detailInfoList?.taskName}
        closable={false}
        open={isModalOpenProps}
        onCancel={handleCancel}
        footer={[
          <Button type="primary" onClick={handleCancel}>
            我知道了
          </Button>,
        ]}
      >
        <TableDetails detailInfoList={detailInfoList} />
      </Modal>
    </>
  );
};

export default DetailsModal;
