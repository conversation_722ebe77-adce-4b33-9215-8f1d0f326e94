import React from 'react';
import { Table, Tag } from 'antd';

interface TableListProps {
  detailInfoList?: any;
}

const TableDetails: React.FC<TableListProps> = (props) => {
  const { detailInfoList } = props;

  const columns = [
    {
      title: '分值',
      dataIndex: 'score',
      key: 'score',
      width: 150,
      render: (text, record) => {
        if (record.isHit === 1 && text) {
          return (
            <>
              {text}
              <Tag bordered={false} style={{ marginLeft: '3px' }} color="volcano">
                当前
              </Tag>
            </>
          );
        }
        return text ?? '-';
      },
    },
    {
      title: '评分规则',
      dataIndex: 'conditionDesc',
      key: 'conditionDesc',
      render: (text) => text || '-',
    },
  ];

  return (
    <>
      <p style={{ margin: '10px 0px' }}>{detailInfoList?.taskDesc}</p>
      <Table
        style={{ maxWidth: '600px' }}
        columns={columns}
        dataSource={detailInfoList?.scoreRules}
        rowKey={(record, index) => index}
      />
    </>
  );
};

export default TableDetails;
