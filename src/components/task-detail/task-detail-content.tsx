import React, { useEffect, useState } from 'react';
import { Spin, Tabs, message } from 'antd';
import './index.less';
import { ShopInfrastructTaskDetail } from '@/components/task-detail/shop-infrastruct-task-detail';
import { AdTaskDetail } from '@/components/task-detail/ad-task-detail';
import { ShanghutongTaskDetail } from '@/components/task-detail/shanghutong-task-detail';
import {
  SHOP_QUERY_SOURCE_ENUM,
  TASK_DETAIL_JUMP_FROM,
  TASK_DETAIL_TABS,
  TASK_DETAIL_TABS_NAME,
  TASK_STATUS_KEY,
} from '@/common/const';
import { sendEvent, getBaseRequestParam, getNewBaseListRequestParam } from '@/common/utils';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import { useStore } from '@/context/global-store';
import { ActionButtonType } from '@/constants';
import { useAction } from '../server-controller/useAction';
import { PageSP<PERSON><PERSON>ey, ModuleSPMKey, traceClick } from '@/utils/trace';

const { TabPane } = Tabs;

interface IProps {
  pid: string;
  jumpSource: string; // 门店列表或商户列表，TASK_DETAIL_JUMP_SOURCE
  from: string; // TASK_DETAIL_JUMP_FROM
  shopId?: string;
  shopQualityScore?: string;
  defaultTab?: string;
}

export const TaskDetailContent: React.FC<IProps> = ({
  pid,
  jumpSource,
  from,
  shopId = '',
  shopQualityScore = '',
  defaultTab = '',
}) => {
  const [tabKey, setTabKey] = useState(defaultTab);
  const [loading, setLoading] = useState(true);
  const [tabsData, setTabsData] = useState<any>();
  const onTabKeyChange = (key) => {
    if (!tabKey || tabKey !== key) {
      setTabKey(key);
    }
  };

  useEffect(() => {
    init();
  }, []);

  const init = () => {
    if (from === TASK_DETAIL_JUMP_FROM.SERVICE_DETAIL_SHOP_INFRASTRUCT) {
      shopInfrastructFirst();
    } else {
      shanghutongFirst();
    }
  };

  const handleTabChange = (key) => {
    onTabKeyChange(key);
    const tabName = TASK_DETAIL_TABS_NAME[key];
    if (tabName) {
      sendEvent(tabName, 'CLK');
    }
    // D 区埋点：任务详情.Tab切换
    traceClick(PageSPMKey.首页, ModuleSPMKey['任务详情.Tab切换'], {
      tabKey: key,
      tabName,
      pid,
      shopId,
    });
  };

  const handleShopInfrastructEmpty = (success: boolean, errorMessage?: string) => {
    if (!success) {
      message.error(errorMessage || '系统异常，请稍后再试～');
    }
    setTabsData([
      {
        key: TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK,
        shopList: success ? [] : undefined,
      },
      {
        key: TASK_DETAIL_TABS.AD_TASK,
      },
    ]);
    onTabKeyChange(TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK);
    setLoading(false);
  };
  const { viewer } = useStore();

  const shopInfrastructFirst = () => {
    setLoading(true);
    if (shopId) {
      queryAllTabsData(shopId);
      return;
    }
    const listParam = {
      viewOperatorId: viewer || undefined,
      pid,
      includeShopTaskProcess: false,
      ...getNewBaseListRequestParam(100, 1, TASK_STATUS_KEY.SHOP),
      ...getBaseRequestParam(),
      source: SHOP_QUERY_SOURCE_ENUM.TASK_DETAIL_SHOP_LIST,
    };
    service
      .queryAgentOperationShopList(listParam)
      .then((res) => {
        const { success, data, msgInfo } = res || {};
        const shopList = data?.dataList || [];
        if (!success || shopList.length === 0) {
          handleShopInfrastructEmpty(success, msgInfo);
          return;
        }
        const currentShopId = shopList[0].shopId || '';
        queryAllTabsData(currentShopId, shopList);
      })
      .catch((error) => {
        console.error('queryAgentOperationShopList error: ', error);
        handleShopInfrastructEmpty(false, error?.errorMessage);
      });
  };

  // 用这个代表运营2吧, 也没别的标了 //
  const taskDetailTabShow = !!useAction(ActionButtonType.门店任务详情广告任务tab)?.showButton;

  const queryAllTabsData = async (shopId: string, shopList?: any[]) => {
    const baseParams = {
      pid,
      jumpSource,
      ...getBaseRequestParam(),
    };
    const shanghutong = service.queryAgentOperationDetail({
      viewOperatorId: viewer || undefined,
      taskDetailScene: TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK,
      ...baseParams,
    });

    const shopInfrastruct = service.queryAgentOperationDetail({
      viewOperatorId: viewer || undefined,
      taskDetailScene: TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK,
      shopId,
      ...baseParams,
    });
    let shopInfrastructRes;
    let shanghutongRes;
    try {
      const allRes = await Promise.all([shopInfrastruct, shanghutong]);
      shopInfrastructRes = allRes[0];
      shanghutongRes = allRes[1];
      console.log(allRes, 'allRes');
    } catch (error) {
      console.error('queryAllTabsData error: ', error);
    }
    const { success: shopInfrastructSuccess, data: shopInfrastructData } = shopInfrastructRes || {};
    const { success: shanghutongSuccess, data: shanghutongData } = shanghutongRes || {};
    const _tabsData = [];
    _tabsData.push({
      key: TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK,
      taskData: shopInfrastructSuccess ? shopInfrastructData : null,
      shopList,
    });
    if (
      shanghutongSuccess &&
      shanghutongData?.taskDetailScene === TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK
    ) {
      _tabsData.push({
        key: TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK,
        taskData: shanghutongData,
        shopList,
      });
    }
    setTabsData([
      ..._tabsData,
      {
        key: TASK_DETAIL_TABS.AD_TASK,
      },
    ]);
    onTabKeyChange(TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK);
    setLoading(false);
  };

  const shanghutongFirst = async () => {
    setLoading(true);
    let shanghutongRes;
    try {
      const params = {
        viewOperatorId: viewer || undefined,
        taskDetailScene: TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK,
        pid,
        shopId,
        jumpSource,
        ...getBaseRequestParam(),
      };
      shanghutongRes = await service.queryAgentOperationDetail(params);
    } catch (error) {
      console.error('queryAgentOperationDetail error', error);
      message.error(error?.errorMessage || '系统异常，请稍后再试～');
    }
    const { success, data, message: resultMessage } = shanghutongRes || {};
    const { taskDetailScene } = data || {};
    if (
      success &&
      taskDetailScene === TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK &&
      taskDetailTabShow
    ) {
      setTabsData([
        {
          key: TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK,
          taskData: data,
        },
        {
          key: TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK,
        },
        {
          key: TASK_DETAIL_TABS.AD_TASK,
        },
      ]);
      onTabKeyChange(TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK);
    } else {
      if (!success) {
        message.error(resultMessage || '系统异常，请稍后再试～');
      }
      setTabsData([
        {
          key: TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK,
          taskData: success ? data : null,
        },
        {
          key: TASK_DETAIL_TABS.AD_TASK,
        },
      ]);
      onTabKeyChange(TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK);
    }
    setLoading(false);
  };

  useEffect(() => {
    // 怎么办呢，，先孤注一掷吧
    if (taskDetailTabShow) {
      onTabKeyChange(TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK);
    }
  }, [taskDetailTabShow]);

  const renderTabs = () => {
    const _tabsData = [...(tabsData || [])];
    // if (isAgent()) {
    //   // 针对服务商隐藏广告任务tab
    //   _tabsData = _tabsData.filter((item) => item.key !== TASK_DETAIL_TABS.AD_TASK);
    // }
    return _tabsData
      .filter((item) => {
        if (taskDetailTabShow) {
          return true;
        } else {
          return item.key === TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK;
        }
      })
      .map((tab: any) => {
        const { key, taskData, shopList } = tab || {};
        const optProps = {
          pid,
          visible: tabKey === key,
          tabKey: key,
          jumpSource,
        };

        let taskDetailCom;
        if (key === TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK) {
          taskDetailCom = (
            <ShanghutongTaskDetail {...optProps} data={taskData} shopList={shopList} />
          );
        } else if (key === TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK) {
          let shop;
          if (shopId) {
            shop = {
              shopId,
              shopQualityScore,
            };
          } else if (shopList?.length > 0) {
            shop = shopList[0];
          }
          taskDetailCom = (
            <ShopInfrastructTaskDetail
              {...optProps}
              data={taskData}
              shopList={shopList}
              shop={shop}
            />
          );
        } else {
          let currentShopId = shopId;
          if (!currentShopId && shopList?.length > 0) {
            currentShopId = shopList[0]?.shopId;
          }
          taskDetailCom = <AdTaskDetail {...optProps} shopId={currentShopId} />;
        }
        return (
          <TabPane tab={TASK_DETAIL_TABS_NAME[key]} key={key}>
            {taskDetailCom}
          </TabPane>
        );
      });
  };

  return (
    <div className="task-detail-drawer">
      <div className="task-detail-drawer-content">
        <Spin spinning={loading}>
          <Tabs activeKey={tabKey} onChange={handleTabChange}>
            {renderTabs()}
          </Tabs>
        </Spin>
      </div>
    </div>
  );
};
