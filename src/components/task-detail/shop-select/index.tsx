import React, { useEffect, useState } from 'react';
import './index.less';
import { Select, Tag, Tooltip, message } from 'antd';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import {
  AgentOperationShopRelationDTO,
  ShopScoreLevelStandard,
} from '@/_docplus/target/types/amap-sales-operation-client';
import { getBaseRequestParam, getNewBaseListRequestParam } from '@/common/utils';
import { debounce } from 'lodash';
import { SHOP_QUERY_SOURCE_ENUM, TASK_STATUS_KEY } from '@/common/const';
import { useStore } from '@/context/global-store';
import { PageSPMKey, ModuleSPMKey, traceClick } from '@/utils/trace';

const { Option } = Select;
interface IProps {
  pid: string;
  shop: any;
  shopList: AgentOperationShopRelationDTO[]; // 门店列表初始值，非空即为请求过，不再重复请求
  onShopChange: (shop: AgentOperationShopRelationDTO) => void;
  onShopList: (value: AgentOperationShopRelationDTO[]) => void;
  // 商家分相关信息
  merchantScoreVersion?: string;
  nextGradeScoreGap?: string;
  baseTotalScore?: string;
  baseActualScore?: string;
  shopScoreLevelStandardList?: ShopScoreLevelStandard[];
  shopScoreLevel?: string;
  shopTotalScore?: string;
  nextShopScoreLevel?: string;
  merchantScoreVersionDegrade?: boolean;
}
export const ShopSelect: React.FC<IProps> = ({
  pid,
  shop: currentShop,
  shopList: shops,
  onShopChange,
  onShopList,
  merchantScoreVersion,
  nextGradeScoreGap,
  baseTotalScore,
  baseActualScore,
  shopScoreLevelStandardList,
  shopScoreLevel,
  shopTotalScore,
  nextShopScoreLevel,
  merchantScoreVersionDegrade,
}) => {
  const [shopList, setShopList] = useState<AgentOperationShopRelationDTO[]>(shops);
  const [init, setInit] = useState(!!shops);

  useEffect(() => {
    if (!shopList) {
      queryShopList();
    }
  }, []);
  const { viewer } = useStore();

  const queryShopList = (keyword = '') => {
    const listParam = {
      viewOperatorId: viewer || undefined,
      pid,
      includeShopTaskProcess: false,
      shopName: keyword,
      ...getNewBaseListRequestParam(100, 1, TASK_STATUS_KEY.SHOP),
      ...getBaseRequestParam(),
      source: SHOP_QUERY_SOURCE_ENUM.TASK_DETAIL_SHOP_LIST,
    };
    service
      .queryAgentOperationShopList(listParam)
      .then((res) => {
        const { dataList: list } = res?.data || {};
        setShopList(list || []);
        onShopList(list || []);
        if (!res.success) {
          message.error(res?.msgInfo || '系统异常，请稍后再试～');
        }
      })
      .catch((e) => {
        console.error('queryAgentOperationShopTaskList error: ', e);
      })
      .finally(() => {
        setInit(true);
      });
  };

  const handleSearch = debounce((keyword: string) => {
    queryShopList(keyword);
  }, 500);

  const handleShopChange = (value) => {
    const selectShop = shopList.find((item) => item.shopId === value);
    if (selectShop) {
      // D 区埋点：任务详情.门店选择
      traceClick(PageSPMKey.首页, ModuleSPMKey['任务详情.门店选择'], {
        shopId: value,
        shopName: selectShop.shopName,
        pid,
      });
      onShopChange(selectShop);
    }
  };

  if (init) {
    return (
      <>
        <div className="task-score-wrap">
          <div className="shop-select">
            <div className="shop-select-label">选择门店</div>
            <Select
              showSearch
              style={{ width: '470px' }}
              filterOption={false}
              value={currentShop?.shopId}
              onChange={handleShopChange}
              onSearch={handleSearch}
              placeholder="请选择"
            >
              {shopList?.map((shop, index) => (
                <Option value={shop.shopId} key={index}>
                  <Tooltip title={shop.shopName}>
                    {shop.shopName}
                    {/* 服务端阐述boolean值过网关等场景偶尔会容易丢失值，故improveInfrastructure用字符串 */}
                    {shop?.improveInfrastructure === 'true' && (
                      <Tag style={{ marginLeft: '8px' }} color="red">
                        完善基建
                      </Tag>
                    )}
                  </Tooltip>
                </Option>
              ))}
            </Select>
          </div>
        </div>
      </>
    );
  }
  return null;
};
