import React from 'react';
import { Modal, Descriptions, Divider, Button } from 'antd';
import { ShopScoreLevelStandard } from '@/_docplus/target/types/amap-sales-operation-client';

interface IMerchantScoreModalProps {
  visible: boolean;
  onClose: () => void;
  merchantScoreVersion: string;
  shopScoreLevel: string;
  nextGradeScoreGap: string;
  baseTotalScore: string;
  baseActualScore: string;
  shopScoreLevelStandardList: ShopScoreLevelStandard[];
}

export const MerchantScoreModal: React.FC<IMerchantScoreModalProps> = ({
  visible,
  onClose,
  merchantScoreVersion,
  shopScoreLevel,
  nextGradeScoreGap,
  baseTotalScore,
  baseActualScore,
  shopScoreLevelStandardList,
}) => {
  return (
    <Modal
      title="商家分信息"
      open={visible}
      onCancel={onClose}
      footer={
        <Button type="primary" onClick={onClose}>
          知道了
        </Button>
      }
      width={550}
      centered
    >
      <Descriptions column={1} bordered size="small" labelStyle={{ width: 150 }}>
        <Descriptions.Item label="商家分版本">{merchantScoreVersion || '-'}</Descriptions.Item>
        <Descriptions.Item label="商家分等级">{shopScoreLevel || '-'}</Descriptions.Item>
        <Descriptions.Item label="距离下一个等级分值">{nextGradeScoreGap || '-'}</Descriptions.Item>
        <Descriptions.Item label="基础分总分">{baseTotalScore || '-'}</Descriptions.Item>
        <Descriptions.Item label="基础分实际得分">{baseActualScore || '-'}</Descriptions.Item>
      </Descriptions>

      <Divider orientation="left" style={{ marginTop: 20, marginBottom: 12, fontSize: 14 }}>
        商家分各等级标准
      </Divider>

      <div style={{ paddingLeft: 8 }}>
        {shopScoreLevelStandardList && shopScoreLevelStandardList.length > 0 ? (
          shopScoreLevelStandardList.map((item, index) => (
            <div
              key={index}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '8px 12px',
                marginBottom: 6,
                background: '#fafafa',
                borderRadius: 4,
              }}
            >
              <span style={{ fontWeight: 500, minWidth: 50 }}>{item.levelName}：</span>
              <span style={{ color: '#666' }}>
                【{item.minScore} {item.maxScore})
              </span>
            </div>
          ))
        ) : (
          <div style={{ textAlign: 'center', color: '#999', padding: 20 }}>暂无数据</div>
        )}
      </div>
    </Modal>
  );
};
