import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import './index.less';
import { <PERSON><PERSON>, <PERSON>ton, Card, Col, Flex, Tag, Tooltip, Popover } from 'antd';
import Link from '@/components/Link';
import { TaskDetailDTO } from '@/_docplus/target/types/alsc-kbt-intergration-toolkit-client';
import { JUMP_TYPE, TASK_DETAIL_TABS, TASK_STATUS } from '@/common/const';
import { QuestionCircleOutlined } from '@ant-design/icons';
import GuideModal, { IGuideModalProps } from '../guide-modal';
import DetailsModal from '../details-modal';
import TableDetails from '../details-modal/tableList';
import { jumpExternalUrl, sendEvent } from '@/common/utils';
import { ImageViewerModalWrapper } from '@alife/mo-image-viewer';
import { IfButtonShow } from '@/components/server-controller/useAction';
import { isAmapXy } from '@/utils';
import { Module<PERSON>MK<PERSON>, PageSPMKey, traceClick } from '@/utils/trace';

interface IProps {
  detailInfo: TaskDetailDTO;
  tabKey: string;
  shopId: string;
}
export const TaskCard: React.FC<IProps> = ({ detailInfo, tabKey, shopId }) => {
  const { taskDetailJumpList, taskName, taskDetailStatus } = detailInfo || {};
  const {
    taskIndicatorCompletedNum,
    taskIndicatorTargetNum,
    taskDesc,
    taskTip,
    taskScore,
    taskActualScore,
    sopButtons = [],
    rewardType,
  } = detailInfo || {};

  const taskDetailLabelList = detailInfo?.taskDetailLabelList || [];
  const taskProgress =
    taskIndicatorCompletedNum && taskIndicatorTargetNum
      ? `(${taskIndicatorCompletedNum}/${taskIndicatorTargetNum})`
      : '';
  // modal弹窗值
  const [ModalOpenProps, setModalOpenProps] = useState(false);

  // 根据奖励类型格式化得分展示
  const getScoreDisplay = () => {
    if (!taskScore) {
      return '';
    }

    // 如果没有 rewardType，保持原来的显示逻辑
    if (!rewardType) {
      return taskActualScore ? `${taskActualScore}分/${taskScore}分` : `${taskScore}分`;
    }

    // 分值型：xx分/XX分
    if (rewardType === 'merchant_score') {
      return taskActualScore ? `${taskActualScore}分/${taskScore}分` : `${taskScore}分`;
    }

    // 曝光型：xx曝光/XX曝光
    if (rewardType === 'exposure') {
      return taskActualScore ? `${taskActualScore}曝光/${taskScore}曝光` : `${taskScore}曝光`;
    }

    // 兜底逻辑
    return taskActualScore ? `${taskActualScore}分/${taskScore}分` : `${taskScore}分`;
  };

  // const getRewardText = (score: string, type: 'task' | 'actual') => {
  //   if (!rewardType) {
  //     // 保持原来的逻辑
  //     return type === 'task' ? `任务总分：${score}分` : `实际得分：${score}分`;
  //   }

  //   if (rewardType === 'merchant_score') {
  //     return type === 'task' ? `任务奖励：${score} 分` : `实际奖励：${score} 分`;
  //   }

  //   if (rewardType === 'exposure') {
  //     // 流量曝光类型只有任务奖励，没有实际奖励
  //     return type === 'task' ? `任务奖励：${score} 曝光` : '';
  //   }

  //   // 兜底逻辑
  //   return type === 'task' ? `任务总分：${score}分` : `实际得分：${score}分`;
  // };
  const [guideModalData, setGuideModalData] = useState<IGuideModalProps>({
    type: JUMP_TYPE.PICTURE,
    visible: false,
    jumpUrl: '',
    title: '',
    buttonText: '我知道了',
  });

  const handleBtnClick = () => {
    const jumpInfo = taskDetailJumpList?.[0];
    const { jumpType, jumpUrl } = jumpInfo || {};

    // 根据tabKey选择正确的模块埋点
    let moduleKey;
    if (tabKey === TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK) {
      moduleKey = ModuleSPMKey['基建任务.任务卡片点击'];
    } else if (tabKey === TASK_DETAIL_TABS.SHOP_SHANG_HU_TONG_TASK) {
      moduleKey = ModuleSPMKey['年费续签任务.任务卡片点击'];
    } else {
      moduleKey = ModuleSPMKey.商家分任务;
    }

    traceClick(PageSPMKey.首页, moduleKey, {
      shopId,
      taskName,
    });

    switch (jumpType) {
      case JUMP_TYPE.PICTURE:
        setGuideModalData({
          type: JUMP_TYPE.PICTURE,
          visible: true,
          jumpUrl,
          title: taskName,
          buttonText: '我知道了',
        });
        break;
      case JUMP_TYPE.TEXT:
        setGuideModalData({
          type: JUMP_TYPE.TEXT,
          visible: true,
          jumpUrl,
          title: taskName,
          buttonText: '我知道了',
        });
        break;
      case JUMP_TYPE.LINK:
        jumpExternalUrl(jumpUrl);
        break;
      default:
        break;
    }
    sendEvent(
      taskDetailStatus === TASK_STATUS.COMPLETED ? 'TASK_COMPLETED_BTN' : 'TO_DO_TASK_BTN',
      'CLK',
    );
  };

  const handleSopClick = (e, value) => {
    const { jumpUrlList = [], buttonText } = value;
    e.preventDefault();
    window.open(jumpUrlList?.[0]);
    sendEvent(
      taskDetailStatus === TASK_STATUS.COMPLETED ? 'TASK_COMPLETED_BTN' : 'TO_DO_TASK_BTN',
      'CLK',
    );
  };

  const completed = taskDetailStatus === TASK_STATUS.COMPLETED;
  const jumpInfo = taskDetailJumpList?.[0];

  return (
    <Col className="infrastruct-card" span={6}>
      <Card bordered={false}>
        {taskDetailLabelList?.length ? (
          <Flex align="center" style={{ position: 'absolute', top: 2, right: 0 }}>
            {taskDetailLabelList.map((label) => (
              <Tag color="error" style={{ marginInlineEnd: 4 }}>
                {label}
              </Tag>
            ))}
          </Flex>
        ) : null}
        <Flex align="center">
          <Popover content={<TableDetails detailInfoList={detailInfo} />} title={taskName}>
            <div
              className="infrastruct-name"
              style={{
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                cursor: 'pointer',
              }}
            >{`${taskName}${isAmapXy() ? '' : taskProgress}`}</div>
          </Popover>

          {taskTip && (
            <Tooltip title={'点击显示规则弹窗'}>
              <QuestionCircleOutlined
                className="index-icon"
                style={{ flexShrink: 0, marginLeft: 4 }}
                onClick={() => {
                  setModalOpenProps((flag) => !flag);
                }}
              />
            </Tooltip>
          )}
        </Flex>
        <span className="infrastruct-desc">{taskTip}</span>
        <div className="infrastruct-status">
          <div>
            {tabKey === TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK && (
              <div>
                {sopButtons
                  ?.filter((i) => i.client === 'PC')
                  ?.map((item) => (
                    <>
                      <IfButtonShow button={item} ext={item?.jumpTypeNew === JUMP_TYPE.LINK}>
                        <Link
                          disabled={!!item?.greyButton}
                          to=""
                          style={{
                            display: 'inline-block',
                            fontSize: 12,
                            textWrap: 'nowrap',
                          }}
                          onClick={(e) => handleSopClick(e, item)}
                        >
                          {item.buttonText}
                        </Link>
                      </IfButtonShow>
                      <IfButtonShow button={item} ext={item?.jumpTypeNew === JUMP_TYPE.PIC}>
                        <ImageViewerModalWrapper imageUrls={item?.jumpUrlList || []}>
                          <Link
                            disabled={!!item?.greyButton}
                            to=""
                            style={{
                              display: 'inline-block',
                              fontSize: 12,
                              textWrap: 'nowrap',
                            }}
                          >
                            {item.buttonText}
                          </Link>
                        </ImageViewerModalWrapper>
                      </IfButtonShow>
                    </>
                  ))}
              </div>
            )}
            <span
              className={classNames({
                'infrastruct-status-badge': tabKey === TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK,
              })}
            >
              <Badge status={completed ? 'success' : 'error'} />
              {completed ? '已完成' : '未完成'}
            </span>
          </div>
          {tabKey === TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK && (
            <div className="infrastruct-status-score">
              <div className="task-score">{getScoreDisplay()}</div>
              <IfButtonShow button={jumpInfo}>
                <Button
                  className={classNames({
                    'button-disabled': completed,
                    button: true,
                  })}
                  type="primary"
                  onClick={handleBtnClick}
                  disabled={!!jumpInfo?.greyButton}
                >
                  {completed ? '已完成' : '去完成'}
                </Button>
              </IfButtonShow>
            </div>
          )}
        </div>
        {guideModalData?.visible && (
          <GuideModal onCancel={() => setGuideModalData(undefined)} {...guideModalData} />
        )}
        <DetailsModal
          isModalOpenProps={ModalOpenProps}
          onCancel={() => setModalOpenProps(false)}
          detailInfoList={detailInfo}
        />
      </Card>
    </Col>
  );
};
