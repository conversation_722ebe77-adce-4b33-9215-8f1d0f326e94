/* eslint-disable no-nested-ternary */
import { memo, useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts';
import type { BarSeriesOption } from 'echarts';
import { Empty, Flex } from 'antd';
import styled from 'styled-components';
import classNames from 'classnames';

interface BarProps {
  data: {
    xAxis: Array<{
      value: string;
      name: string;
    }>;
    series: BarSeriesOption[];
  };
  title?: string;
  height?: string | number;
  width?: string | number;
  onItemClick?: (data: any) => void;
  barWidth?: number;
  barGap?: number;
  onScroll?: (params: { start: number; end: number }) => void;
  isPercent?: boolean;
  updated?: boolean;
  legend?: Array<{ name: string; color: string }>;
  tooltip?: echarts.TooltipComponentOption;
  allowLegendClick?: boolean;
}
const LegendWrapper = styled(Flex)<{ clickable: boolean }>`
  user-select: none;
  cursor: ${({ clickable }) => (clickable ? 'pointer' : 'default')};
  z-index: 10;
  .legend-item {
    width: 10px;
    height: 10px;
    border-radius: 2px;
  }
  &.disabled {
    opacity: 0.6;
  }
`;
const Legend = (params: {
  legends: Array<{ name: string; color: string }>;
  onLegendClick?: (name: string) => void;
  legendShowMap?: Record<string, boolean>;
  allowLegendClick?: boolean;
}) => {
  const { legends, onLegendClick, legendShowMap, allowLegendClick = true } = params;
  if (!legends?.length) return null;
  return (
    <Flex align="center" gap={20} style={{ position: 'absolute', top: 0, left: 24 }}>
      {legends.map((legend) => (
        <LegendWrapper
          key={legend.name}
          gap={5}
          align="center"
          className={classNames({
            disabled: allowLegendClick ? !legendShowMap[legend.name] : false,
            clickable: allowLegendClick,
          })}
          onClick={() => onLegendClick?.(legend.name)}
        >
          <div
            className="legend-item"
            style={{
              backgroundColor: allowLegendClick
                ? legendShowMap[legend.name]
                  ? legend.color
                  : '#f0f0f0'
                : legend.color,
            }}
          />
          {legend.name}
        </LegendWrapper>
      ))}
    </Flex>
  );
};

function BarChart(props: BarProps) {
  const {
    data,
    title,
    height = 400,
    width = 800,
    onItemClick,
    barWidth = 20,
    barGap = 120,
    onScroll,
    isPercent,
    updated,
    legend = [],
    tooltip = {},
    allowLegendClick = true,
  } = props;
  const [legendShowMap, setLegendShowMap] = useState<Record<string, boolean>>(
    legend.reduce((acc, current) => {
      acc[current.color] = true;
      return acc;
    }, {}),
  );
  const processedData = allowLegendClick
    ? data.series.filter((item) => legendShowMap[item.name])
    : data.series;
  const processedXAxis = allowLegendClick
    ? data.xAxis.filter((item) => legendShowMap[item.name])
    : data.xAxis;
  const hasData = !!(data?.xAxis?.length && data?.series?.length);
  const [oldData, setOldData] = useState(data);
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();
  const [scrollState, setScrollState] = useState({ start: 0, end: 0 });

  const updateOptions = () => {
    if (!chartRef.current) return;
    const chart = chartInstance.current!;
    const option: echarts.EChartsOption = {
      title: {
        text: title,
      },
      tooltip: {
        trigger: 'axis',
        valueFormatter(value, dataIndex) {
          if (value === undefined) return '-';
          if (isPercent) {
            return `${value}%`;
          }
          return value;
        },
        ...tooltip,
      },
      grid: {
        left: 24,
        right: 24,
        top: 50,
        containLabel: true,
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          zoomLock: true,
          brushSelect: false,
          ...calculateZoomRange(),
        },
      ],
      xAxis: {
        type: 'category',
        data: processedXAxis.map((item) => item.value),
        axisLabel: {
          interval: 0,
          rotate: 45,
          overflow: 'truncate',
          ellipsis: '...',
          width: 80,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter(value) {
            if (isPercent) {
              return `${value}%`;
            }
            return value;
          },
        },
      },
      series: [
        {
          type: 'bar',
          data: processedData as BarSeriesOption['data'],
          barMaxWidth: 50,
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
          },
          barWidth,
          barGap,
        } satisfies BarSeriesOption,
      ],
    };

    chart.setOption(option);
  };
  const handleScroll = (params) => {
    setScrollState({
      start: params.start,
      end: params.end,
    });
    onScroll?.(params);
  };
  const chartInit = () => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);
    chartInstance.current = chart;
    chart.on('click', 'series.bar', (params) => {
      onItemClick?.(params?.data);
    });
    chart.on('datazoom', handleScroll);
    updateOptions();

    return chart;
  };

  // 计算合适的缩放范围
  function calculateZoomRange() {
    // 获取容器宽度
    const containerWidth = chartRef.current?.getBoundingClientRect().width;

    // 计算单个柱子占用的总宽度（柱子宽度 + 间距）
    const barTotalWidth = barWidth * (1 + barGap / 100);

    // 计算容器能显示的柱子数量
    const visibleBars = Math.floor(containerWidth / barTotalWidth);
    const totalBars = data.xAxis.length;
    const rangeWidth = Math.min((visibleBars / totalBars) * 100, 100);
    const oldLength = oldData.xAxis.length;
    let start = scrollState.start || 0;
    if (updated) {
      start = 0;
      handleScroll({ start: 0, end: 0 });
    } else if (totalBars > oldLength && start > 0) {
      start = (oldLength / totalBars) * 100;
    }
    setOldData(data);
    return {
      start,
      end: start + rangeWidth,
    };
  }
  function handleLegendClick(name: string) {
    if (!allowLegendClick) return;
    setLegendShowMap((prev) => ({
      ...prev,
      [name]: !prev[name],
    }));
  }

  useEffect(() => {
    if (hasData) {
      const chart = chartInit();

      const handleResize = () => {
        chart.resize();
      };
      window.addEventListener('resize', handleResize);
      return () => {
        chart.dispose();
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [hasData]);

  useEffect(() => {
    updateOptions();
  }, [data, title, legendShowMap]);

  if (!hasData) {
    return <Empty style={{ height: 150, margin: '12px 0' }} />;
  }

  return (
    <div style={{ position: 'relative' }}>
      <Legend
        legends={legend}
        legendShowMap={legendShowMap}
        onLegendClick={handleLegendClick}
        allowLegendClick={allowLegendClick}
      />
      <div ref={chartRef} style={{ height, width }} />
    </div>
  );
}
export default memo(BarChart, (prevProps, nextProps) => {
  return (
    JSON.stringify(prevProps.data) === JSON.stringify(nextProps.data) &&
    prevProps.title === nextProps.title
  );
});
