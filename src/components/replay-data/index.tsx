import React from 'react';
import { IDefaultShop } from '@/pages/business-news/components/weekly-report-form/form';
import styled from 'styled-components';
import WeeklyReportForm from '@/components/business-news/components/weekly-report-form';

interface IProps {
  pid: string;
  merchantName: string;
  defaultShop?: IDefaultShop;
}
const Container = styled.div`
  .qw-material-tab {
    width: auto;
    margin-right: 4px;
    border-radius: 4px;
    padding: 0 5px;
  }
`;

export const ReplayData: React.FC<IProps> = ({ pid, merchantName, defaultShop }) => {
  return (
    <Container>
      <WeeklyReportForm
        pid={pid}
        merchantName={merchantName}
        defaultShop={defaultShop}
        hasOptGroup
      />
    </Container>
  );
};
