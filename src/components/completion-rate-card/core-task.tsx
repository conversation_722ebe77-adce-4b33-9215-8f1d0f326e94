import { Flex, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import Card from '@/components/card';
import { CompletionRateCard } from '.';
import { useMount, useRequest } from 'ahooks';
import { getTaskCompletionRate } from '@/services';
import styled from 'styled-components';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { TASK_TABPAN_ENUM } from '@/common/const';
import { isAgent } from '@/common/utils';
import { traceExp, traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';

const tipsMapping = [
  {
    codes: ['shop_keep_rate'],
    nameIncludes: null,
    text: '上个自然月门店广告现金消耗>0的门店中，本月累计现金消耗≥100的门店占比',
  },
  {
    codes: ['AD_CONTINUED_CHARGING'],
    nameIncludes: '广告首续',
    text: '近14天内「广告首续」任务下发的任务完成情况',
  },
  {
    codes: ['ad_recharge_task', 'BALANCE_WARNING_COMBINED'],
    nameIncludes: '广告续充',
    text: '近14天内下发的任务完成情况（含广告首续任务+应充预警任务）',
  },
  {
    codes: ['annual_renewal_task', 'NIAN_FEI__EXTENSION'],
    nameIncludes: '年费续签',
    text: '近90天内「年费临期续费」任务下发的任务完成情况',
  },
];

interface ICoreTaskCompletionProps {
  onCardClick?: (code: string) => void;
}

const TitleWrapper = styled.div`
  color: #999;
  font-size: 14px;
  margin: 0 0 6px 6px;
  display: flex;
  align-items: center;
`;

export const CoreTaskCompletion = (props: ICoreTaskCompletionProps) => {
  const { onCardClick } = props;

  const { data: completionRates = [] } = useRequest(
    async () => {
      const res = await getTaskCompletionRate();
      let rates = res?.rate || [];

      // 判断是否为渠道用户，只有渠道用户才显示门店次月留存率
      const isChannelUser = isAgent();
      if (!isChannelUser) {
        rates = rates.filter((item: any) => {
          const isRetentionRate = item.code === 'shop_keep_rate';
          return !isRetentionRate;
        });
      }
      return rates;
    },
    {
      onSuccess: (data) => {
        if (data && data.length > 0) {
          data.forEach((task) => {
            traceExp(PageSPMKey.首页, ModuleSPMKey['核心任务完成率.任务'], {
              name: task.name,
            });
          });
        }
      },
    },
  );

  // 点击埋点处理函数
  const handleCardClick = (code: string, name: string) => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['核心任务完成率.卡片'], {
      name,
    });
    // 门店次月留存率 - 只使用code匹配（已确认）
    if (code === 'shop_keep_rate') {
      emitter.emit(EmitterEventMap.TaskDataClick, {
        type: TASK_TABPAN_ENUM.MERCHANT,
        params: {
          filterOptRelation: true,
          warningTaskLabels: 'CONSUME_RETENTION_WARING',
        },
      });
      return;
    }
    // 广告首续任务完成率 - 更具体的放在前面，优先使用code，fallback到name
    if (code === 'AD_CONTINUED_CHARGING' || name?.includes('广告首续')) {
      emitter.emit(EmitterEventMap.TaskDataClick, {
        type: TASK_TABPAN_ENUM.MERCHANT,
        params: {
          adTaskLabels: ['AD_CONTINUED_CHARGING'],
        },
      });
      return;
    }
    // 广告续充任务完成率 - 优先使用code，fallback到name
    if (
      code === 'ad_recharge_task' ||
      code === 'BALANCE_WARNING_COMBINED' ||
      name?.includes('广告续充')
    ) {
      emitter.emit(EmitterEventMap.TaskDataClick, {
        type: TASK_TABPAN_ENUM.MERCHANT,
        params: {
          adTaskLabels: ['BALANCE_WARNING', 'AD_CONTINUED_CHARGING'],
        },
      });
      return;
    }
    // 年费续签任务完成率 - 优先使用code，fallback到name
    if (
      code === 'annual_renewal_task' ||
      code === 'NIAN_FEI__EXTENSION' ||
      name?.includes('年费续签')
    ) {
      emitter.emit(EmitterEventMap.TaskDataClick, {
        type: TASK_TABPAN_ENUM.SHOP,
        params: {
          shangHuTongExpireCondition: [
            'EXPIRE_IN_THIRTY_DAY',
            'EXPIRE_BW_THIRTY_AND_SIXTY_DAY',
            'EXPIRE_BW_SIXTY_AND_NINETY_DAY',
          ],
        },
      });
      return;
    }
    onCardClick?.(code);
  };

  // 获取卡片颜色
  const getCardColor = (item: any) => {
    const rate = Number(item.finishRate);
    // 门店次月留存率：≥80%蓝色，<80%红色
    if (item.code === 'shop_keep_rate') {
      return rate >= 80 ? '#1677ff' : '#ff4d4f';
    }
    // 其他指标默认蓝色
    return '#1677ff';
  };

  useMount(() => {
    traceExp(PageSPMKey.首页, ModuleSPMKey.核心任务完成率);
  });

  return (
    <div>
      <TitleWrapper>
        核心任务完成率
        <Tooltip title="仅展示运维小二（不含BD）名下的商户任务完成情况">
          <QuestionCircleOutlined style={{ marginLeft: 8 }} />
        </Tooltip>
      </TitleWrapper>
      <Card style={{ marginBottom: 16, cursor: 'pointer' }}>
        <Flex>
          {completionRates.map((item) => {
            const matchingTip = tipsMapping.find(
              (config) =>
                config.codes.includes(item.code) ||
                (config.nameIncludes && item.name?.includes(config.nameIncludes)),
            );
            const tips = matchingTip?.text || '';

            // 处理应作业数为0或取不到数据的情况
            const displayRate =
              item.assignmentsNum === 0 || !item.finishRate ? '-' : item.finishRate;

            return (
              <CompletionRateCard
                key={item.code}
                title={item.name}
                rate={displayRate}
                tips={tips}
                color={getCardColor(item)}
                onClick={() => handleCardClick(item.code, item.name)}
                completedNum={item.completedNum}
                assignmentsNum={item.assignmentsNum}
              />
            );
          })}
        </Flex>
      </Card>
    </div>
  );
};

export default CoreTaskCompletion;
