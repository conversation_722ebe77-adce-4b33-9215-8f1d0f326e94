import { IModalProps } from '@/hooks/useModal';
import { Modal, Tabs } from 'antd';
import Recharge from './recharge';
import RechargeHistory from './recharge-history';
import AdSwitch from './ad-switch';
import { useRequest } from 'ahooks';
import { queryAdPlan } from '@/services';
import { useEffect } from 'react';

export default function OrderModal(props: IModalProps<{ pid: string; shopId?: string }>) {
  const shopId = props.data?.shopId;
  const pid = props.data?.pid;
  const { data, run } = useRequest(
    async () => {
      return queryAdPlan({ shopId, pid });
    },
    {
      manual: true,
    },
  );
  useEffect(() => {
    if (props.data?.shopId && props.data?.pid) {
      run();
    }
  }, [props.data?.shopId, props.data?.pid]);
  return (
    <Modal
      {...props}
      title="广告代操"
      footer={null}
      maskClosable={false}
      styles={{
        body: {
          maxHeight: 500,
          overflowY: 'auto',
        },
      }}
    >
      <Tabs>
        <Tabs.TabPane tab="推广通充值下单" key="1">
          <Recharge pid={props.data?.pid} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="推广通充值记录" key="2">
          <RechargeHistory pid={props.data?.pid} />
        </Tabs.TabPane>
        {data?.advertiserId || data?.orderId ? (
          <Tabs.TabPane tab="广告计划开启" key="3">
            <AdSwitch
              refresh={run}
              orderId={data?.orderId}
              shopId={shopId}
              disabled={!data?.canOptUpdate}
              enableTime={data?.startTime}
              advertiserId={data?.advertiserId}
              campaignId={data?.campaignId}
            />
          </Tabs.TabPane>
        ) : null}
      </Tabs>
    </Modal>
  );
}
