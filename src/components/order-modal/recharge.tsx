import { But<PERSON> } from 'antd';
// import { Spin, Form, Radio, Row, Col, Input, Flex, Button, message } from 'antd';
// import ActivityTag from './components/ActivityTag';
// import { getActivityList, recharge } from '@/services';
// import { getFullTime } from '@/utils';
import { getEnv } from '@/common/utils';
import { Env } from '@/common/const';
// import { useRequest } from 'ahooks';
// import { useTimeDown } from './components/useTimeDown';

// const amountList = ['1000', '2000', '3000', '5000', '8000', '10000'];

export default function Recharge(props: { pid: string }) {
  // const [form] = Form.useForm();
  const { pid } = props;

  // 注释掉活动信息相关逻辑
  // const {
  //   data: actInfo,
  //   run,
  //   loading,
  // } = useRequest(
  //   async (amount: string = form.getFieldValue('amount')) => {
  //     if (!pid) {
  //       return;
  //     }
  //     const res = await getActivityList({
  //       pid,
  //       amount: amount ? Number(amount) * 100 : undefined,
  //     });
  //     return res;
  //   },
  //   {
  //     refreshDeps: [pid],
  //   },
  // );

  // 注释掉充值相关逻辑
  // const { loading: recharging, run: runRecharge } = useRequest(
  //   async (amount: string) => {
  //     return recharge({ amount, pid });
  //   },
  //   {
  //     onSuccess: () => {
  //       message.success('提交成功');
  //     },
  //     onError: (err) => {
  //       message.error(err.message);
  //     },
  //     manual: true,
  //   },
  // );

  // const handleRecharge = () => {
  //   form.validateFields().then(async (values) => {
  //     if (!values.amount && !values._amount) {
  //       message.error('充值金额不能为空');
  //     } else {
  //       runRecharge(values.amount || values._amount);
  //     }
  //   });
  // };

  // 注释掉活动标签相关逻辑
  // const { endTime, name } = actInfo || {};
  // const { day, hour, minute, second } = useTimeDown({
  //   deadline: getFullTime(endTime),
  // });
  // // 倒计时皆为0活动已结束 或者没有活动信息都不展示
  // const activityFinished = day === 0 && hour === 0 && minute === 0 && second === 0;

  // // 倒计时 结束时间存在且day小于30 展示倒计时
  // const showTimeDown = endTime && day < 30;
  return (
    <div style={{ textAlign: 'center', padding: '20px 0' }}>
      <div
        style={{
          fontSize: '16px',
          fontWeight: 'bold',
          marginBottom: '20px',
        }}
      >
        充值入口迁移到签约中心,请前往签约中心进行充值
      </div>
      <Button
        type="primary"
        onClick={() => {
          // 根据环境跳转到对应的签约中心
          const env = getEnv();
          const baseUrl = env === Env.Pre ? 'https://pre-xy.amap.com' : 'https://xy.amap.com';
          // 跳转到签约中心
          const url = `${baseUrl}/sale-pc/alsc-commercial-pc/v2-index?merchantPid=${pid}&sourceFrom=ad-recharge-modal`;
          window.open(url, '_blank');
        }}
      >
        去签约中心
      </Button>
    </div>
  );
}
