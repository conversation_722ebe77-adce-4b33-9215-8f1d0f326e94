import React, { SVGProps } from 'react';

export function IconFontQiWei(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      {...props}
    >
      <path
        d="M657.003 737.28c-5.12 5.12-5.12 5.12-5.12 10.24s0 5.12 5.12 10.24c25.6 25.6 46.08 61.44 51.2 97.28 5.12 25.6 30.72 40.96 56.32 40.96 25.6-5.12 46.08-25.6 46.08-51.2s-20.48-51.2-46.08-51.2c-35.84-5.12-66.56-25.6-92.16-51.2 0-10.24-10.24-10.24-15.36-5.12z"
        fill="#FB6500"
      />
      <path
        d="M872.043 655.36c-10.24 10.24-15.36 20.48-15.36 30.72-5.12 35.84-25.6 66.56-51.2 92.16-5.12 5.12-5.12 10.24-5.12 15.36s5.12 10.24 10.24 10.24 10.24 0 10.24-5.12c25.6-25.6 61.44-46.08 97.28-51.2 20.48-5.12 35.84-25.6 40.96-46.08 0-20.48-10.24-40.96-30.72-51.2-15.36-15.36-40.96-10.24-56.32 5.12z"
        fill="#0082EF"
      />
      <path
        d="M723.563 506.88c-15.36 15.36-20.48 35.84-10.24 56.32 5.12 20.48 25.6 30.72 46.08 35.84 35.84 5.12 66.56 25.6 92.16 51.2 5.12 5.12 10.24 5.12 15.36 5.12s10.24-5.12 10.24-10.24 0-10.24-5.12-10.24c-25.6-25.6-46.08-61.44-51.2-97.28-5.12-20.48-20.48-30.72-35.84-35.84-30.72-15.36-46.08-10.24-61.44 5.12z"
        fill="#2DBC00"
      />
      <path
        d="M697.963 588.8c-25.6 30.72-61.44 46.08-102.4 51.2-20.48 5.12-30.72 20.48-35.84 35.84-5.12 20.48 0 35.84 15.36 51.2s35.84 20.48 56.32 10.24c20.48-5.12 30.72-25.6 35.84-46.08 5.12-35.84 25.6-66.56 51.2-92.16 5.12-5.12 5.12-10.24 0-20.48-10.24 5.12-15.36 5.12-20.48 10.24z"
        fill="#FFCC00"
      />
      <path
        d="M375.403 128c-92.16 10.24-179.2 51.2-240.64 117.76-25.6 25.6-46.08 51.2-61.44 81.92-46.08 92.16-40.96 204.8 20.48 291.84 15.36 25.6 46.08 56.32 71.68 81.92l-10.24 92.16v20.48c0 10.24 5.12 20.48 15.36 25.6s20.48 5.12 25.6 0l25.6-15.36 81.92-40.96c40.96 10.24 76.8 15.36 117.76 15.36 51.2 0 97.28-10.24 148.48-25.6-25.6-10.24-40.96-30.72-35.84-56.32-46.08 15.36-102.4 20.48-148.48 15.36h-10.24c-20.48 0-35.84-5.12-56.32-10.24-10.24-5.12-20.48 0-30.72 5.12L221.803 768h-10.24s-5.12 0-5.12-5.12v-10.24l5.12-10.24 5.12-20.48 5.12-20.48c5.12-10.24 0-25.6-10.24-30.72-25.6-20.48-51.2-40.96-66.56-71.68-46.08-66.56-51.2-158.72-15.36-230.4 15.36-35.84 30.72-56.32 51.2-76.8 51.2-51.2 122.88-87.04 199.68-92.16 25.6-5.12 56.32-5.12 81.92 0 76.8 10.24 148.48 40.96 199.68 97.28 20.48 20.48 35.84 40.96 46.08 66.56 15.36 30.72 25.6 66.56 25.6 102.4v10.24c25.6-20.48 51.2-15.36 66.56 0v5.12c5.12-51.2-5.12-102.4-30.72-148.48-15.36-30.72-35.84-61.44-56.32-81.92-66.56-66.56-148.48-107.52-240.64-117.76-30.72-5.12-61.44-5.12-97.28-5.12z"
        fill="#3970BA"
      />
    </svg>
  );
}

export function IconTrade(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      {...props}
    >
      <path
        d="M823.9 733.4H201.2c-29.8 0-54-24.2-54-54V235.7c0-29.8 24.2-54 54-54h622.7c29.8 0 54 24.2 54 54v443.7c0 29.8-24.2 54-54 54z m-606.7-70h590.7V251.7H217.2v411.7zM658.3 846.6H366.8c-19.3 0-35-15.7-35-35s15.7-35 35-35h291.5c19.3 0 35 15.7 35 35s-15.7 35-35 35z"
        fill="#ffffff"
      />
      <path
        d="M355.8 548.3c-9 0-17.9-3.4-24.7-10.3-13.7-13.7-13.7-35.8 0-49.5L434 385.6c13.7-13.7 35.8-13.7 49.5 0l76.2 76.2 84.8-84.8c13.7-13.7 35.8-13.7 49.5 0 13.7 13.7 13.7 35.8 0 49.5L584.5 536.1c-13.7 13.7-35.8 13.7-49.5 0l-76.2-76.2-78.2 78.1c-6.9 6.9-15.8 10.3-24.8 10.3z"
        fill="#ffffff"
      />
    </svg>
  );
}

export function IconBangDan(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <defs>
        <clipPath id="master_svg0_32_21220">
          <rect x="0" y="0" width="24" height="24" rx="0" />
        </clipPath>
      </defs>
      <g clipPath="url(#master_svg0_32_21220)">
        <g>
          <path
            d="M21.239951171875,0L2.759951171875,0C1.9049561718749999,0,1.199956207275,0.69,1.199956207275,1.53L1.199956207275,19.155C1.198291891875,19.7999,1.608988171875,20.3736,2.219951171875,20.58L11.459951171875,23.895C11.804951171875,24.015,12.179951171875,24.015,12.524951171875,23.895L21.764951171875,20.58C22.379951171875,20.355,22.784951171875,19.785,22.784951171875,19.155L22.784951171875,1.53C22.799951171875,0.69,22.094951171875,0,21.239951171875,0ZM11.639951171875,4.89L14.699951171875,4.89L14.744951171875,4.605L14.099951171875,3.84L17.834951171875,3.84L17.669951171875,4.875L20.894951171875,4.875L20.669951171875,6.405L11.414951171875,6.405L11.639951171875,4.89ZM2.834951171875,17.205L4.424951171875,8.58L5.624951171875,8.58L4.049951171875,17.205L2.834951171875,17.205ZM4.499951171875,17.91L5.939961171875,8.07L4.304961171875,8.07L4.589951171875001,6.21L6.224951171875,6.21L6.494951171875,4.395L6.074951171875,3.825L9.134961171875,3.825L8.789951171875,6.21L10.394961171875,6.21L10.124951171875,8.07L8.519951171875,8.07L7.079951171875,17.91L4.499951171875,17.91ZM7.904951171875,16.095L8.699951171875,8.58L9.899961171875,8.58L9.134961171875,16.095L7.904951171875,16.095ZM20.279951171875,12.72L14.654951171875,12.72L14.474951171875,13.11L19.889951171875,13.11L19.334951171875,16.83C19.304951171875,17.085,19.184951171875,17.31,18.974951171875,17.475C18.764951171875,17.64,18.524951171875,17.73,18.269951171875,17.73L15.434951171875,17.73L16.679951171875,16.455L16.934951171875,14.595L13.814951171875,14.595L12.329951171875,17.895L9.509961171875,17.895L11.849951171875,12.72L9.854961171875,12.72L10.124951171875,10.875L13.739951171875,10.875L13.904951171875,9.84L17.114951171875,9.84L16.949951171875,10.875L20.564951171875,10.875L20.279951171875,12.72ZM20.669951171875,10.5L18.134951171875,10.5L18.254951171875,9.6L12.884951171875,9.6L12.749951171875,10.5L10.259961171875,10.5L10.619951171875,7.92L12.734951171875,7.92L12.479951171875,6.795L15.014951171875,6.795L15.284951171875,7.905L16.649951171875,7.905L17.264951171875,6.795L19.784951171875,6.795L19.199951171875,7.905L21.059951171875,7.905L20.669951171875,10.5Z"
            fill="#1A66FF"
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  );
}

export function IconGoldV(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="1em"
      height="1em"
      viewBox="0 0 32 32"
      {...props}
    >
      <defs>
        <mask id="master_svg0_32_21181" maskUnits="objectBoundingBox">
          <g>
            <rect x="0" y="0" width="32" height="32" rx="0" fill="#FFFFFF" fillOpacity="1" />
          </g>
        </mask>
        <linearGradient
          x1="0.7062952518463135"
          y1="1.0597020387649536"
          x2="0.2513723522795586"
          y2="0.34827811591508473"
          id="master_svg1_32_20863"
        >
          <stop offset="0%" stopColor="#FFEDAE" stopOpacity="1" />
          <stop offset="100%" stopColor="#FFEDAE" stopOpacity="1" />
        </linearGradient>
        <linearGradient
          x1="0.29138675332069397"
          y1="0.21451261639595032"
          x2="0.8129625585485496"
          y2="1.055122170540732"
          id="master_svg2_32_20865"
        >
          <stop offset="0%" stopColor="#F8DE7B" stopOpacity="1" />
          <stop offset="100%" stopColor="#F3C448" stopOpacity="1" />
        </linearGradient>
        <mask id="master_svg3_32_21174" maskUnits="objectBoundingBox">
          <g transform="matrix(-1,0,0,-1,64.72560924291611,62.614888191223145)">
            <path
              d="M52.437304621458054,34.90477409561157L62.36290462145806,44.52444409561157C63.343804621458055,45.47504409561157,63.840604621458056,45.95894409561157,64.09950462145805,46.52854409561157C64.32510462145805,47.02494409561157,64.42580462145806,47.57004409561157,64.39230462145805,48.11414409561157C64.35380462145805,48.73874409561157,64.06250462145806,49.36804409561157,63.485704621458055,50.60624409561157L59.699504621458054,58.73414409561157C59.38740462145805,59.40414409561157,59.22870462145805,59.74324409561157,58.98710462145806,59.98784409561157C58.77600462145806,60.20174409561157,58.519104621458055,60.36554409561157,58.236104621458054,60.46654409561157C57.91230462145805,60.58224409561157,57.537904621458054,60.58294409561157,56.79880462145805,60.58294409561157L40.652344621458056,60.58294409561157C39.91327462145805,60.58294409561157,39.53883462145805,60.58224409561157,39.215084621458054,60.46654409561157C38.93207462145806,60.36554409561157,38.67514462145805,60.20174409561157,38.46400462145805,59.98784409561157C38.222474621458055,59.74324409561157,38.06370462145805,59.40414409561157,37.751624621458056,58.73414409561157L33.965444621458055,50.60624409561157C33.388644621458056,49.36804409561157,33.09732462145806,48.73874409561157,33.05888662145805,48.11414409561157C33.02539362145805,47.57004409561157,33.12602762145805,47.02494409561157,33.35161862145805,46.52854409561157C33.610504621458055,45.95894409561157,34.10733462145805,45.47504409561157,35.08823462145806,44.52444409561157L45.01380462145805,34.90477409561157C46.317004621458054,33.64181409561157,46.985304621458056,32.99903409561157,47.73370462145805,32.76642409561157C48.378704621458056,32.56595409561157,49.07240462145805,32.56595409561157,49.717504621458055,32.76642409561157C50.46590462145805,32.99903409561157,51.13420462145805,33.64181409561157,52.437304621458054,34.90477409561157"
              fill="#FFFFFF"
              fillOpacity="1"
            />
          </g>
        </mask>
        <filter
          id="master_svg4_32_21175"
          filterUnits="objectBoundingBox"
          colorInterpolationFilters="sRGB"
          x="0"
          y="0"
          width="1"
          height="1"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="8.06437873840332" />
          <feComposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape" />
        </filter>
        <linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="master_svg5_32_20851">
          <stop offset="0%" stopColor="#573400" stopOpacity="1" />
          <stop offset="39.91650640964508%" stopColor="#C19120" stopOpacity="1" />
          <stop offset="52.92655825614929%" stopColor="#E2AF37" stopOpacity="1" />
          <stop offset="64.63560461997986%" stopColor="#C19120" stopOpacity="1" />
          <stop offset="100%" stopColor="#573400" stopOpacity="1" />
        </linearGradient>
      </defs>
      <g mask="url(#master_svg0_32_21181)">
        <g mask="url(#master_svg3_32_21174)">
          <g transform="matrix(-1,0,0,-1,64.72560924291611,62.614888191223145)">
            <path
              d="M52.437304621458054,34.90477409561157L62.36290462145806,44.52444409561157C63.343804621458055,45.47504409561157,63.840604621458056,45.95894409561157,64.09950462145805,46.52854409561157C64.32510462145805,47.02494409561157,64.42580462145806,47.57004409561157,64.39230462145805,48.11414409561157C64.35380462145805,48.73874409561157,64.06250462145806,49.36804409561157,63.485704621458055,50.60624409561157L59.699504621458054,58.73414409561157C59.38740462145805,59.40414409561157,59.22870462145805,59.74324409561157,58.98710462145806,59.98784409561157C58.77600462145806,60.20174409561157,58.519104621458055,60.36554409561157,58.236104621458054,60.46654409561157C57.91230462145805,60.58224409561157,57.537904621458054,60.58294409561157,56.79880462145805,60.58294409561157L40.652344621458056,60.58294409561157C39.91327462145805,60.58294409561157,39.53883462145805,60.58224409561157,39.215084621458054,60.46654409561157C38.93207462145806,60.36554409561157,38.67514462145805,60.20174409561157,38.46400462145805,59.98784409561157C38.222474621458055,59.74324409561157,38.06370462145805,59.40414409561157,37.751624621458056,58.73414409561157L33.965444621458055,50.60624409561157C33.388644621458056,49.36804409561157,33.09732462145806,48.73874409561157,33.05888662145805,48.11414409561157C33.02539362145805,47.57004409561157,33.12602762145805,47.02494409561157,33.35161862145805,46.52854409561157C33.610504621458055,45.95894409561157,34.10733462145805,45.47504409561157,35.08823462145806,44.52444409561157L45.01380462145805,34.90477409561157C46.317004621458054,33.64181409561157,46.985304621458056,32.99903409561157,47.73370462145805,32.76642409561157C48.378704621458056,32.56595409561157,49.07240462145805,32.56595409561157,49.717504621458055,32.76642409561157C50.46590462145805,32.99903409561157,51.13420462145805,33.64181409561157,52.437304621458054,34.90477409561157"
              fill="url(#master_svg1_32_20863)"
              fillOpacity="1"
            />
            <path
              d="M52.437304621458054,34.90477409561157L62.36290462145806,44.52444409561157C63.343804621458055,45.47504409561157,63.840604621458056,45.95894409561157,64.09950462145805,46.52854409561157C64.32510462145805,47.02494409561157,64.42580462145806,47.57004409561157,64.39230462145805,48.11414409561157C64.35380462145805,48.73874409561157,64.06250462145806,49.36804409561157,63.485704621458055,50.60624409561157L59.699504621458054,58.73414409561157C59.38740462145805,59.40414409561157,59.22870462145805,59.74324409561157,58.98710462145806,59.98784409561157C58.77600462145806,60.20174409561157,58.519104621458055,60.36554409561157,58.236104621458054,60.46654409561157C57.91230462145805,60.58224409561157,57.537904621458054,60.58294409561157,56.79880462145805,60.58294409561157L40.652344621458056,60.58294409561157C39.91327462145805,60.58294409561157,39.53883462145805,60.58224409561157,39.215084621458054,60.46654409561157C38.93207462145806,60.36554409561157,38.67514462145805,60.20174409561157,38.46400462145805,59.98784409561157C38.222474621458055,59.74324409561157,38.06370462145805,59.40414409561157,37.751624621458056,58.73414409561157L33.965444621458055,50.60624409561157C33.388644621458056,49.36804409561157,33.09732462145806,48.73874409561157,33.05888662145805,48.11414409561157C33.02539362145805,47.57004409561157,33.12602762145805,47.02494409561157,33.35161862145805,46.52854409561157C33.610504621458055,45.95894409561157,34.10733462145805,45.47504409561157,35.08823462145806,44.52444409561157L45.01380462145805,34.90477409561157C46.317004621458054,33.64181409561157,46.985304621458056,32.99903409561157,47.73370462145805,32.76642409561157C48.378704621458056,32.56595409561157,49.07240462145805,32.56595409561157,49.717504621458055,32.76642409561157C50.46590462145805,32.99903409561157,51.13420462145805,33.64181409561157,52.437304621458054,34.90477409561157ZM50.736104621458054,36.66010409561157Q49.19090462145805,35.16256409561157,48.99200462145805,35.100724095611575Q48.725604621458054,35.01793409561157,48.459204621458056,35.100724095611575Q48.26020462145805,35.16256409561157,46.71500462145805,36.66010409561157L36.78944462145805,46.279744095611576Q35.646164621458055,47.38774409561157,35.57700462145805,47.539944095611574Q35.48506462145805,47.74224409561157,35.498714621458056,47.96404409561157Q35.508984621458055,48.13084409561157,36.18127462145805,49.57404409561157L39.96745462145805,57.70194409561157Q40.092934621458056,57.97134409561157,40.17250462145805,58.135844095611574Q40.35512462145805,58.13854409561157,40.652344621458056,58.13854409561157L56.79880462145805,58.13854409561157Q57.09600462145805,58.13854409561157,57.27860462145806,58.135844095611574Q57.35820462145806,57.97134409561157,57.48370462145805,57.70194409561157L61.26990462145805,49.57404409561157Q61.94220462145805,48.13084409561157,61.95240462145806,47.96404409561157Q61.96610462145806,47.74224409561157,61.87410462145805,47.539944095611574Q61.80500462145805,47.38774409561157,60.66170462145806,46.279744095611576L50.736104621458054,36.66010409561157Z"
              fillRule="evenodd"
              fill="url(#master_svg2_32_20865)"
              fillOpacity="0.20000000298023224"
            />
          </g>
          <g filter="url(#master_svg4_32_21175)">
            <path
              d="M11.574826311826707,7.522095203399658L8.793826311826706,7.522095203399658L13.713076311826706,21.020995203399657C13.913056311826706,21.569695203399657,14.434786311826706,21.93489520339966,15.018846311826707,21.93489520339966L17.799846311826705,21.93489520339966L12.880596311826706,8.436018203399659C12.680616311826705,7.887265203399658,12.158886311826706,7.522095203399658,11.574826311826707,7.522095203399658ZM23.205826311826705,7.522095203399658L18.233816311826708,19.668595203399658L16.505766311826704,14.946045203399658L19.191326311826707,8.385381203399659C19.405026311826706,7.863214203399658,19.913226311826705,7.522095203399658,20.477526311826708,7.522095203399658L23.205826311826705,7.522095203399658Z"
              fillRule="evenodd"
              fill="url(#master_svg5_32_20851)"
              fillOpacity="1"
            />
          </g>
        </g>
      </g>
    </svg>
  );
}
export function IconSlivery(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="1em"
      height="1em"
      viewBox="0 0 48 48"
      {...props}
    >
      <defs>
        <mask id="master_svg0_4_840" maskUnits="objectBoundingBox">
          <g>
            <rect x="0" y="0" width="48" height="48" rx="0" fill="#FFFFFF" fillOpacity="1" />
          </g>
        </mask>
        <linearGradient
          x1="0.01744861900806427"
          y1="0.5406997799873352"
          x2="0.48424013216663786"
          y2="0.9479060850944737"
          id="master_svg1_1_062"
        >
          <stop offset="0%" stopColor="#E9EEF7" stopOpacity="0.699999988079071" />
          <stop offset="100%" stopColor="#DFE3EB" stopOpacity="0.6200000047683716" />
        </linearGradient>
        <mask id="master_svg2_4_842" maskUnits="objectBoundingBox">
          <g transform="matrix(-1,-1.2246468525851679e-16,1.2246468525851679e-16,-1,97.47265625,93.82085418701172)">
            <path
              d="M78.176128125,51.65518709350586L94.229928125,67.21412709350585C95.523628125,68.46792709350586,96.179028125,69.10612709350586,96.520428125,69.85742709350586C96.818028125,70.51212709350585,96.950728125,71.23112709350586,96.906528125,71.94882709350586C96.855828125,72.77252709350586,96.471628125,73.60262709350586,95.710828125,75.23582709350586L89.585528125,88.38522709350586C89.173928125,89.26882709350586,88.964528125,89.71612709350586,88.645928125,90.03882709350586C88.367428125,90.32092709350586,88.02852812500001,90.53692709350585,87.65522812500001,90.67022709350586C87.228228125,90.82272709350586,86.734428125,90.82372709350585,85.759528125,90.82372709350585L60.801428125,90.82372709350585C59.826628125,90.82372709350585,59.332728125,90.82272709350586,58.905728124999996,90.67022709350586C58.532438125,90.53692709350585,58.193558124999996,90.32092709350586,57.915078125,90.03882709350586C57.596508125,89.71612709350586,57.387088125,89.26882709350586,56.975468125,88.38522709350586L50.850148125,75.23582709350586C50.089378125,73.60262709350586,49.705134125,72.77262709350586,49.654437125,71.94882709350586C49.610260125,71.23112709350586,49.742988125,70.51212709350585,50.040538125,69.85742709350586C50.382008125,69.10612709350586,51.037308125,68.46792709350586,52.331068125,67.21412709350585L68.384828125,51.65518709350586C70.103628125,49.98938709350586,70.985128125,49.141567093505856,71.972228125,48.83476709350586C72.82302812500001,48.57035709350586,73.738028125,48.57035709350586,74.588728125,48.83476709350586C75.575928125,49.141567093505856,76.457428125,49.98938709350586,78.176128125,51.65518709350586"
              fill="#FFFFFF"
              fillOpacity="1"
            />
          </g>
        </mask>
        <filter
          id="master_svg3_4_843"
          filterUnits="objectBoundingBox"
          colorInterpolationFilters="sRGB"
          x="0"
          y="0"
          width="1"
          height="1"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur in="BackgroundImageFix" stdDeviation="10.636638641357422" />
          <feComposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape" />
        </filter>
        <linearGradient
          x1="0.5"
          y1="0.08523987233638763"
          x2="0.5"
          y2="0.8789209723472595"
          id="master_svg4_1_067"
        >
          <stop offset="5.000000074505806%" stopColor="#3862B8" stopOpacity="1" />
          <stop offset="51.428574323654175%" stopColor="#B9CCF3" stopOpacity="1" />
          <stop offset="100%" stopColor="#3862B8" stopOpacity="1" />
        </linearGradient>
      </defs>
      <g mask="url(#master_svg0_4_840)">
        <g mask="url(#master_svg2_4_842)">
          <g transform="matrix(-1,-1.2246468525851679e-16,1.2246468525851679e-16,-1,97.47265625,93.82085418701172)">
            <path
              d="M78.176128125,51.65518709350586L94.229928125,67.21412709350585C95.523628125,68.46792709350586,96.179028125,69.10612709350586,96.520428125,69.85742709350586C96.818028125,70.51212709350585,96.950728125,71.23112709350586,96.906528125,71.94882709350586C96.855828125,72.77252709350586,96.471628125,73.60262709350586,95.710828125,75.23582709350586L89.585528125,88.38522709350586C89.173928125,89.26882709350586,88.964528125,89.71612709350586,88.645928125,90.03882709350586C88.367428125,90.32092709350586,88.02852812500001,90.53692709350585,87.65522812500001,90.67022709350586C87.228228125,90.82272709350586,86.734428125,90.82372709350585,85.759528125,90.82372709350585L60.801428125,90.82372709350585C59.826628125,90.82372709350585,59.332728125,90.82272709350586,58.905728124999996,90.67022709350586C58.532438125,90.53692709350585,58.193558124999996,90.32092709350586,57.915078125,90.03882709350586C57.596508125,89.71612709350586,57.387088125,89.26882709350586,56.975468125,88.38522709350586L50.850148125,75.23582709350586C50.089378125,73.60262709350586,49.705134125,72.77262709350586,49.654437125,71.94882709350586C49.610260125,71.23112709350586,49.742988125,70.51212709350585,50.040538125,69.85742709350586C50.382008125,69.10612709350586,51.037308125,68.46792709350586,52.331068125,67.21412709350585L68.384828125,51.65518709350586C70.103628125,49.98938709350586,70.985128125,49.141567093505856,71.972228125,48.83476709350586C72.82302812500001,48.57035709350586,73.738028125,48.57035709350586,74.588728125,48.83476709350586C75.575928125,49.141567093505856,76.457428125,49.98938709350586,78.176128125,51.65518709350586"
              fill="#F2F6FD"
              fillOpacity="1"
            />
            <path
              d="M78.176128125,51.65518709350586L94.229928125,67.21412709350585C95.523628125,68.46792709350586,96.179028125,69.10612709350586,96.520428125,69.85742709350586C96.818028125,70.51212709350585,96.950728125,71.23112709350586,96.906528125,71.94882709350586C96.855828125,72.77252709350586,96.471628125,73.60262709350586,95.710828125,75.23582709350586L89.585528125,88.38522709350586C89.173928125,89.26882709350586,88.964528125,89.71612709350586,88.645928125,90.03882709350586C88.367428125,90.32092709350586,88.02852812500001,90.53692709350585,87.65522812500001,90.67022709350586C87.228228125,90.82272709350586,86.734428125,90.82372709350585,85.759528125,90.82372709350585L60.801428125,90.82372709350585C59.826628125,90.82372709350585,59.332728125,90.82272709350586,58.905728124999996,90.67022709350586C58.532438125,90.53692709350585,58.193558124999996,90.32092709350586,57.915078125,90.03882709350586C57.596508125,89.71612709350586,57.387088125,89.26882709350586,56.975468125,88.38522709350586L50.850148125,75.23582709350586C50.089378125,73.60262709350586,49.705134125,72.77262709350586,49.654437125,71.94882709350586C49.610260125,71.23112709350586,49.742988125,70.51212709350585,50.040538125,69.85742709350586C50.382008125,69.10612709350586,51.037308125,68.46792709350586,52.331068125,67.21412709350585L68.384828125,51.65518709350586C70.103628125,49.98938709350586,70.985128125,49.141567093505856,71.972228125,48.83476709350586C72.82302812500001,48.57035709350586,73.738028125,48.57035709350586,74.588728125,48.83476709350586C75.575928125,49.141567093505856,76.457428125,49.98938709350586,78.176128125,51.65518709350586ZM75.932328125,53.97039709350586Q73.894328125,51.99519709350586,73.631828125,51.91362709350586Q73.280528125,51.80442709350586,72.929128125,51.91362709350586Q72.66672812499999,51.99519709350586,70.628628125,53.97039709350586L54.574918124999996,69.52932709350586Q53.066968125,70.99072709350585,52.975738125,71.19152709350585Q52.854478125,71.45832709350586,52.872488125000004,71.75082709350586Q52.886028125,71.97082709350586,53.772758125,73.87442709350586L59.898028124999996,87.02382709350586Q60.063628125,87.37912709350586,60.168528125,87.59602709350585Q60.409528125,87.59952709350586,60.801428125,87.59952709350586L85.759528125,87.59952709350586Q86.151428125,87.59952709350586,86.39242812500001,87.59602709350585Q86.497428125,87.37912709350586,86.66292812500001,87.02382709350586L92.788228125,73.87442709350586Q93.67492812500001,71.97082709350586,93.688528125,71.75082709350586Q93.706528125,71.45832709350586,93.585228125,71.19152709350585Q93.494028125,70.99072709350585,91.98602812499999,69.52932709350586L75.932328125,53.97039709350586Z"
              fillRule="evenodd"
              fill="url(#master_svg1_1_062)"
              fillOpacity="0.699999988079071"
            />
          </g>
          <g filter="url(#master_svg3_4_843)">
            <path
              d="M17.730168359375,11.23244571685791L13.382568359375,11.23244571685791L20.821738359375,31.64624571685791C21.085498359375002,32.36994571685791,21.773648359375002,32.85164571685791,22.543998359375,32.85164571685791L26.891568359375,32.85164571685791L19.452428359375,12.43787571685791C19.188668359375,11.71409271685791,18.500518359375,11.23244571685791,17.730168359375,11.23244571685791ZM35.000168359374996,11.23244571685791L27.542168359374998,29.45194571685791L24.950068359375003,22.36804571685791L29.042268359375,12.37108571685791C29.324168359375,11.68237071685791,29.994468359375,11.23244571685791,30.738668359375,11.23244571685791L35.000168359374996,11.23244571685791Z"
              fillRule="evenodd"
              fill="url(#master_svg4_1_067)"
              fillOpacity="1"
            />
          </g>
        </g>
      </g>
    </svg>
  );
}

export function IconGoldVShadow(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="1em"
      height="1em"
      viewBox="0 0 34 34"
      {...props}
    >
      <defs>
        <mask id="master_svg0_32_07255" maskUnits="objectBoundingBox">
          <g>
            <path
              d="M0,0L26.9612,0C29.425,0,30.6848,0.00498667,31.6225,0.491038C32.4296,0.9094,33.0906,1.57044,33.509,2.37755C33.995,3.31524,34,4.575,34,7.03882L34,26.9612C34,29.425,33.995,30.6848,33.509,31.6225C33.0906,32.4296,32.4296,33.0906,31.6225,33.509C30.6848,33.995,29.425,34,26.9612,34L2.36553e-12,34L0,0Z"
              fill="#FFFFFF"
              fillOpacity="1"
            />
          </g>
        </mask>
        <filter
          id="master_svg1_32_07256"
          filterUnits="objectBoundingBox"
          colorInterpolationFilters="sRGB"
          x="-0.5246839583397888"
          y="-0.521342478389891"
          width="1.9327714814929577"
          height="2.042684956779782"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          />
          <feOffset dy="0" dx="-2.7703702449798584" />
          <feGaussianBlur stdDeviation="5.540740489959717" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.761904776096344 0 0 0 0 0.46922194957733154 0 0 0 0 0 0 0 0 0.8299999833106995 0"
          />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
        </filter>
      </defs>
      <g mask="url(#master_svg0_32_07255)">
        <g
          transform="matrix(-1,0,0,-1,46.541015625,78.357421875)"
          filter="url(#master_svg1_32_07256)"
        >
          <path
            d="M48.9619078125,41.0502709375L69.3733078125,60.832710937499996C69.8836078125,61.3272109375,70.1421078125,61.5790109375,70.2767078125,61.875310937500004C70.39410781250001,62.1335109375,70.4465078125,62.4171109375,70.4290078125,62.7002109375C70.4090078125,63.0252109375,70.2575078125,63.3526109375,69.95740781250001,63.9968109375L62.4623078125,80.0870109375C62.1917078125,80.6679109375,62.0540078125,80.9619109375,61.8446078125,81.1740109375C61.6615078125,81.3595109375,61.4387078125,81.5015109375,61.1933078125,81.58911093750001C60.9126078125,81.6894109375,60.5879078125,81.6900109375,59.9471078125,81.6900109375L34.1146078125,81.6900109375C33.4738078125,81.6900109375,33.1490878125,81.6894109375,32.8683578125,81.58911093750001C32.6229678125,81.5015109375,32.400177812500004,81.3595109375,32.217107812500004,81.1740109375C32.0076778125,80.9619109375,31.8700078125,80.6679109375,31.5994078125,80.0870109375L25.6661278125,67.34971093749999C24.8039378125,65.4988109375,24.3684678125,64.5580109375,24.3110078125,63.6245109375C24.2609458125,62.8110109375,24.4113778125,61.996210937499995,24.7485878125,61.254210937500005C25.1355778125,60.402710937500004,25.8782478125,59.6795109375,27.3444878125,58.2584109375L45.0998078125,41.0502709375C45.7777078125,40.3932009375,46.1254078125,40.0587839375,46.514807812499996,39.9377669375C46.850407812499995,39.8334689375,47.211307812499996,39.8334689375,47.546907812499995,39.9377669375C47.9362078125,40.0587839375,48.283907812500004,40.3932009375,48.9619078125,41.0502709375"
            fill="#FFFFFF"
            fillOpacity="0.009999999776482582"
          />
        </g>
      </g>
    </svg>
  );
}

export function IconDoctor(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="1em"
      height="1em"
      viewBox="0 0 52 52"
      {...props}
    >
      <defs>
        <linearGradient x1="0.5" y1="1" x2="0.5" y2="0" id="master_svg0_33_02148">
          <stop offset="0%" stopColor="#FEF6F1" stopOpacity="1" />
          <stop offset="100%" stopColor="#FFE0C9" stopOpacity="1" />
        </linearGradient>
      </defs>
      <g>
        <g>
          <rect
            x="0"
            y="0"
            width="52"
            height="52"
            rx="16"
            fill="url(#master_svg0_33_02148)"
            fillOpacity="1"
          />
        </g>
        <g>
          <g>
            <g />
          </g>
          <g>
            <g>
              <path
                d="M13.20009,32.538C13.67693,32.8197,14.00023,33.3378,14.00023,33.9373C14.00023,34.504400000000004,13.7125,35.003299999999996,13.27709,35.2941L14.79991,38L10,38L11.52282,35.2941C11.08786,35.003299999999996,10.800135000000001,34.504400000000004,10.800135000000001,33.9373C10.800135000000001,33.3378,11.12388,32.8197,11.60027,32.538L11.60027,21.74971L11.60117,21.74971L11.60027,21.74926L26.8001,12L42,21.74971L26.8001,31.4999L13.20009,22.7759L13.20009,32.538ZM25.755699999999997,32.923L26.6154,33.4653L27.4751,32.923L37.076899999999995,26.857599999999998L37.076899999999995,31.392L26.6154,38L16.15384,31.392L16.15384,26.8572L25.755699999999997,32.923Z"
                fillRule="evenodd"
                fill="#F38638"
                fillOpacity="1"
              />
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
}

export function IconCirclePK(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="1em"
      height="1em"
      viewBox="0 0 51 51"
      {...props}
    >
      <g>
        <g>
          <ellipse cx="25.5" cy="25.5" rx="25.5" ry="25.5" fill="#FFFFFF" fillOpacity="1" />
        </g>
        <g>
          <g>
            <path
              d="M12.540521755065917,28.140124176025388L16.29359175506592,28.140124176025388C20.62683175506592,28.24852417602539,23.06573175506592,26.35298417602539,23.610331755065918,22.453644176025392Q24.079931755065918,19.87246417602539,23.008331755065917,18.46291417602539Q21.719131755065916,16.76712417602539,18.19933175506592,16.76712417602539L10.611581755065918,16.76712417602539L7.684931755065918,34.23292417602539L11.519591755065917,34.23292417602539L12.540521755065917,28.140124176025388ZM12.962271755065917,25.62182417602539L14.037651755065918,19.20417417602539L17.056431755065915,19.20417417602539C19.241231755065918,19.15002417602539,20.12033175506592,20.23316417602539,19.69393175506592,22.45362417602539C19.439631755065918,24.61991417602539,18.202031755065917,25.67598417602539,15.981051755065918,25.62182417602539L12.962271755065917,25.62182417602539Z"
              fillRule="evenodd"
              fill="#FB6D32"
              fillOpacity="1"
            />
          </g>
          <g>
            <path
              d="M27.732054644470214,34.23292417602539L23.753424644470215,34.23292417602539L26.737434644470216,16.76712417602539L30.716064644470215,16.76712417602539L29.300394644470217,25.05320417602539L38.17602464447022,16.76712417602539L43.31502464447021,16.76712417602539L34.84082464447022,24.15960417602539L40.82842464447022,34.23292417602539L36.186624644470214,34.23292417602539L31.813254644470213,26.84042417602539L28.495404644470213,29.76492417602539L27.732054644470214,34.23292417602539Z"
              fill="#1A87FF"
              fillOpacity="1"
            />
          </g>
        </g>
      </g>
    </svg>
  );
}

export function IconMerchantStar() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      version="1.1"
      width="23.08544921875"
      height="24"
      viewBox="0 0 23.08544921875 24"
    >
      <g>
        <g>
          <g />
          <g>
            <path
              d="M14.290855783233642,20.090857551574707C13.690255783233642,16.391757551574706,11.464585783233643,14.067877551574707,7.915525783233643,13.432867551574708C7.892092783233642,13.428677551574706,7.869920783233643,13.425307551574708,7.846760783233643,13.419797551574707C7.596384783233643,13.360277551574708,7.420345783233643,13.124247551574708,7.420345783233643,12.857137551574706C7.420345783233643,12.589337551574708,7.595861783233643,12.358717551574706,7.839212783233642,12.296447551574708C7.8673317832336425,12.289247551574707,7.8950937832336425,12.285097551574708,7.923660783233642,12.279967551574707C11.440525783233642,11.647787551574707,13.656185783233642,9.355587551574708,14.276615783233643,5.709111551574707C14.286445783233642,5.651348551574707,14.294075783233643,5.591919551574707,14.312385783233642,5.536260551574707C14.388345783233643,5.305345551574707,14.599515783233642,5.142857551574707,14.842505783233642,5.142857551574707C15.101575783233642,5.142857551574707,15.324455783233642,5.327546551574707,15.382755783233643,5.582942551574707C15.388735783233642,5.609113551574707,15.392305783233642,5.634550551574707,15.396695783233643,5.6610335515747074C16.00455578323364,9.328067551574707,18.21654578323364,11.635737551574707,21.73644578323364,12.276117551574707C21.779745783233643,12.283987551574707,21.823045783233642,12.289997551574707,21.865145783233643,12.302727551574707C22.099145783233645,12.373487551574707,22.26104578323364,12.601027551574706,22.26104578323364,12.857137551574706C22.26104578323364,13.124947551574706,22.08554578323364,13.355567551574707,21.84214578323364,13.417847551574708C21.81414578323364,13.425017551574706,21.787445783233643,13.428987551574707,21.758945783233642,13.434097551574707C18.232645783233643,14.067527551574708,16.01424578323364,16.369657551574708,15.400165783233643,20.03245755157471C15.393395783233643,20.072757551574707,15.388485783233643,20.111157551574706,15.378105783233643,20.150757551574706C15.312985783233643,20.398957551574707,15.091825783233642,20.571457551574706,14.842505783233642,20.571457551574706C14.570945783233643,20.571457551574706,14.339135783233644,20.368457551574707,14.294985783233642,20.093857551574708C14.294815783233641,20.092857551574706,14.293965783233642,20.092357551574707,14.292915783233642,20.092357551574707C14.291875783233642,20.092357551574707,14.291025783233643,20.091857551574705,14.290855783233642,20.090857551574707"
              fill="#1A66FF"
              fillOpacity="1"
            />
          </g>
          <g style={{ opacity: 0.5 }}>
            <path
              d="M5.404842917785644,11.679685850524903C5.0044529177856445,9.213595850524904,3.5206729177856446,7.664305850524903,1.1546029177856445,7.2409558505249025C1.1389809177856445,7.2381658505249025,1.1241999177856445,7.235915850524902,1.1087599177856444,7.232245850524903C0.9418429177856446,7.192565850524902,0.8244829177856445,7.035215850524902,0.8244829177856445,6.857145850524902C0.8244829177856445,6.684085850524903,0.9344189177856446,6.534325850524902,1.0889139177856446,6.4874858505249025C1.1166019177856445,6.4790858505249025,1.1448979177856446,6.475125850524902,1.1733629177856446,6.469945850524902C3.5015929177856444,6.046665850524902,4.971632917785644,4.529395850524902,5.390722917785644,2.1185638505249025C5.400752917785645,2.0608798505249024,5.407012917785645,2.003051850524902,5.429412917785645,1.9489588505249023C5.487032917785645,1.8098224505249023,5.620452917785644,1.7142858505249023,5.772592917785644,1.7142858505249023C5.941052917785645,1.7142858505249023,6.086562917785645,1.8314248505249022,6.129692917785644,1.9951898505249024C6.136572917785645,2.0213378505249024,6.139742917785645,2.0460748505249025,6.144212917785644,2.0727428505249024C6.551312917785644,4.502655850524903,8.017372917785645,6.034535850524902,10.347762917785644,6.465975850524902C10.390952917785645,6.473975850524902,10.433942917785645,6.479205850524902,10.475102917785645,6.494515850524903C10.619862917785644,6.548385850524903,10.718272917785644,6.694075850524903,10.718272917785644,6.857145850524902C10.718272917785644,7.030195850524902,10.608342917785645,7.179965850524902,10.453842917785645,7.226805850524903C10.426162917785645,7.235195850524902,10.397862917785645,7.239165850524902,10.369402917785644,7.2443358505249025C8.032832917785644,7.669135850524903,6.560612917785645,9.195765850524904,6.147582917785645,11.621625850524902C6.1407129177856445,11.661965850524902,6.136382917785644,11.700385850524903,6.1238929177856445,11.739385850524902C6.074302917785644,11.894085850524903,5.932092917785645,11.999985850524903,5.772592917785644,11.999985850524903C5.591512917785645,11.999985850524903,5.436952917785645,11.864685850524902,5.407562917785644,11.681545850524902C5.4074429177856445,11.680855850524903,5.406892917785645,11.680615850524902,5.406192917785645,11.680615850524902C5.405502917785644,11.680615850524902,5.404952917785645,11.680375850524902,5.404842917785644,11.679685850524903"
              fill="#1A66FF"
              fillOpacity="1"
            />
          </g>
          <g style={{ opacity: 0.1 }}>
            <path
              d="M6.351511671142578,21.215098298950195C6.084611671142579,19.570998298950194,5.095411671142578,18.538108298950196,3.518011671142578,18.255878298950194C3.5075966711425783,18.254008298950197,3.497742671142578,18.252518298950196,3.487449671142578,18.250068298950197C3.376171471142578,18.223618298950196,3.297931671142578,18.118718298950196,3.297931671142578,17.999998298950196C3.297931671142578,17.889818298950196,3.364780371142578,17.793798298950197,3.4605116711425783,17.758178298950195C3.487628671142578,17.748098298950197,3.516491671142578,17.744488298950195,3.544940671142578,17.739218298950195C5.078771671142578,17.455248298950195,6.0509516711425775,16.456208298950195,6.3366116711425775,14.871923298950195C6.3470016711425785,14.814303298950195,6.351921671142579,14.753918298950195,6.380011671142578,14.702547298950195C6.423111671142578,14.623720998950196,6.504771671142578,14.571428298950195,6.596671671142579,14.571428298950195C6.704441671142578,14.571428298950195,6.798121671142578,14.643342198950196,6.830921671142578,14.745628298950196C6.839181671142578,14.771376298950194,6.842311671142578,14.798171298950196,6.8468616711425785,14.824826298950196C7.1203216711425785,16.428718298950194,8.088521671142578,17.442738298950196,9.62466167114258,17.735068298950196C9.667861671142578,17.743288298950194,9.712671671142578,17.747848298950196,9.752231671142578,17.767038298950194C9.837381671142579,17.808358298950196,9.893791671142578,17.899198298950196,9.893791671142578,17.999998298950196C9.893791671142578,18.110418298950194,9.826661671142578,18.206618298950197,9.730601671142578,18.242048298950195C9.703371671142577,18.252088298950195,9.674221671142579,18.255688298950197,9.645681671142578,18.260978298950196C8.103621671142578,18.546868298950194,7.129491671142578,19.555748298950196,6.850231671142578,21.155598298950196C6.8432016711425785,21.195908298950194,6.839121671142578,21.237428298950196,6.823371671142578,21.275188298950194C6.784991671142578,21.367168298950197,6.695841671142578,21.428568298950196,6.596671671142579,21.428568298950196C6.4759216711425776,21.428568298950196,6.372861671142578,21.338298298950196,6.353301671142578,21.216188298950193C6.353231671142578,21.215728298950197,6.352871671142578,21.215648298950196,6.352411671142578,21.215648298950196C6.351941671142578,21.215648298950196,6.351591671142578,21.215558298950196,6.351511671142578,21.215098298950195"
              fill="#1A66FF"
              fillOpacity="1"
            />
          </g>
        </g>
      </g>
    </svg>
  );
}
