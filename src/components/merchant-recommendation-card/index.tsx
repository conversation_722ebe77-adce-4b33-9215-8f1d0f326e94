import React, { useState } from 'react';
import { Card, Tag, Tooltip, message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import TaskCountdown from '@/pages/index/components/todo-list/TaskCountdown';
import { RECOMMEND_TAG_TYPE } from '@/common/const';

// 样式常量定义
const cardStyle = {
  borderRadius: 6,
  height: 180,
  flexBasis: '270px',
  flexGrow: 0,
  flexShrink: 0,
  width: 270,
  background: 'linear-gradient(180deg, rgba(255, 232, 234, 0.4) 0%, rgba(255, 255, 255, 0) 20%)',
};

const cardBodyStyle = {
  padding: '4px 8px 8px 8px',
};

const countdownContainerStyle = {
  height: 28,
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  zIndex: 5,
  marginBottom: 4,
};

const merchantNameContainerStyle = {
  display: 'flex',
  alignItems: 'center',
  marginBottom: 8,
  gap: 8,
};

const merchantNameStyle = {
  fontFamily: 'PingFang SC',
  fontSize: 16,
  fontWeight: 500,
  lineHeight: '24px',
  letterSpacing: 'normal',
  color: 'rgba(0, 0, 0, 0.85)',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '100%',
};

const getTagStyle = (type: string) => {
  const baseStyle = {
    padding: '1px 8px',
    gap: 3,
    zIndex: 4,
    borderRadius: 2,
    border: 'none',
  };

  switch (type) {
    case RECOMMEND_TAG_TYPE.SUPERVISOR_URGE:
      return {
        ...baseStyle,
        background: '#fff1f0',
        color: '#f5222d',
      };
    case RECOMMEND_TAG_TYPE.SMART_RECOMMEND:
      return {
        ...baseStyle,
        background: '#fff7e6',
        color: '#fa8c16',
      };
    default:
      return baseStyle;
  }
};

const pidContainerStyle = {
  fontSize: 14,
  color: '#8c8c8c',
  marginBottom: 8,
  paddingBottom: 8,
  borderBottom: '1px solid #f0f0f0',
  textAlign: 'left' as const,
  display: 'flex',
  alignItems: 'center',
  gap: 8,
};

const taskInfoStyle = {
  fontSize: 14,
  color: '#595959',
  marginBottom: 8,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  textAlign: 'left' as const,
};

const revenueInfoStyle = {
  background: 'linear-gradient(270deg, #edf9f3 0%, #eef8fe 100%)',
  borderRadius: 10,
  padding: '6px 8px',
  fontSize: 10,
  display: 'inline-block',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '100%',
  textAlign: 'left' as const,
};

export interface IMerchantRecommendationCardProps {
  /** 倒计时时间戳 */
  countdownTime?: number;
  /** 商户名称 */
  merchantName: string;
  /** 推荐标签类型 */
  recommendTagType?: RECOMMEND_TAG_TYPE;
  /** 商户PID */
  pid: string;
  /** 任务信息 */
  taskInfo: string;
  /** 门店数量 */
  storeCount?: number;
  /** 预估收益 */
  estimatedRevenue?: string;
  /** 点击事件 */
  onClick?: () => void;
}

const MerchantRecommendationCard: React.FC<IMerchantRecommendationCardProps> = ({
  countdownTime,
  merchantName,
  recommendTagType,
  pid,
  taskInfo,
  storeCount,
  estimatedRevenue,
  onClick,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getRecommendTagText = (type: string) => {
    switch (type) {
      case RECOMMEND_TAG_TYPE.SUPERVISOR_URGE:
        return '主管催办';
      case RECOMMEND_TAG_TYPE.SUPERVISOR_ASSIGN:
        return '主管下达';
      case RECOMMEND_TAG_TYPE.SMART_RECOMMEND:
        return '智能推荐';
      default:
        return '';
    }
  };

  const handleCopyPid = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard
      .writeText(pid)
      .then(() => {
        message.success('PID已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败，请重试');
      });
  };

  return (
    <Card hoverable onClick={onClick} style={cardStyle} bodyStyle={cardBodyStyle}>
      {countdownTime && (
        <div style={countdownContainerStyle}>
          <TaskCountdown
            expireTime={countdownTime}
            style={{
              background: 'transparent',
              padding: 0,
              borderRadius: 0,
              color: 'inherit',
            }}
            timeNumberColor="#ff4d4f"
          />
        </div>
      )}

      <div style={merchantNameContainerStyle}>
        <Tooltip title={merchantName}>
          <div style={merchantNameStyle}>{merchantName}</div>
        </Tooltip>
        {recommendTagType && (
          <Tag style={getTagStyle(recommendTagType)}>{getRecommendTagText(recommendTagType)}</Tag>
        )}
      </div>

      <div style={pidContainerStyle}>
        <span>PID: {pid}</span>
        <CopyOutlined
          style={{
            cursor: 'pointer',
            fontSize: 14,
            color: isHovered ? '#1890ff' : 'inherit',
            transition: 'color 0.3s',
          }}
          onClick={handleCopyPid}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        />
      </div>

      <div style={taskInfoStyle}>
        <Tooltip title={taskInfo}>
          <span
            style={{
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              flex: 1,
              marginRight: 8,
              textAlign: 'left',
            }}
          >
            {taskInfo}
          </span>
        </Tooltip>
        {Number(storeCount) > 0 && <span style={{ textAlign: 'right' }}>{storeCount}个门店</span>}
      </div>

      {estimatedRevenue && <div style={revenueInfoStyle}>{estimatedRevenue}</div>}
    </Card>
  );
};

export default MerchantRecommendationCard;
