import React from 'react';
import { Typography } from 'antd';

const { Title } = Typography;

interface PageTitleProps {
  title: string;
  level?: 1 | 2 | 3 | 4 | 5;
  style?: React.CSSProperties;
}

export default function PageTitle({ title, level = 5, style = { margin: 12 } }: PageTitleProps) {
  return (
    <Title
      level={level}
      style={{
        ...style,
        fontWeight: 600,
      }}
    >
      {title}
    </Title>
  );
}
