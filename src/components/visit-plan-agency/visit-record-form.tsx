import { C33_CALL_ENUM } from '@/common/const';
import { eventEmitter, useEventHelper } from '@/common/event';
import { ComponentLoader } from '@/components/component-loader';
import { Button, Row, message } from 'antd';
import React, { useRef, useState } from 'react';
import './index.less';
import { callStatusChangeKey, echoWorkbench } from '@/common/echo';

export const VisitRecordFormEventShow = 'VisitRecordFormEventShow';

export const showVisitRecordFormPanel = (param: {
  replenish?: boolean;
  targetId?: string;
  targetType?: string;
  callRecordId?: string;
  contactScene?: string;
  /**
   * 收起时是否需要刷新
   */
  needFefresh?: { fefresh: (params: any) => void; params: any };
}) => {
  eventEmitter.emit(VisitRecordFormEventShow, param);
};

export const VisitRecordForm = ({
  handlePackUp,
  defaultInputInfo,
}: {
  handlePackUp: () => void;
  defaultInputInfo?: any;
}) => {
  const [visible, setVisible] = useState<boolean>(false);
  const statusInfoRef = useRef<any>();
  const [statusInfo, setStatusInfo] = useState<{
    status: 'RINGING' | 'CONNECTED' | 'NOT_CONNECTED';
    errorMsg: string;
  }>();
  const [options, setOptions] = useState<{
    callReplenishFlag?: boolean;
    targetId?: string;
    targetType?: string;
    callRecordId?: string;
    bizScene?: string;
    contactScene?: string;
  }>();
  const [fefreshParams, setFefreshParams] = useState<{
    fefresh: (params: any) => void;
    params: any;
  }>();

  useEventHelper(
    VisitRecordFormEventShow,
    (params: {
      replenish?: boolean;
      targetId?: string;
      targetType?: string;
      callRecordId?: string;
      contactScene?: string;
      /**
       * 收起时是否需要刷新
       */
      needFefresh?: { fefresh: (params: any) => void; params: any };
    }) => {
      const { targetId, callRecordId, targetType, contactScene, replenish, needFefresh } =
        params || {};
      if (needFefresh) {
        setFefreshParams(needFefresh);
      } else {
        setFefreshParams(undefined);
      }
      if (visible) {
        message.error('请先填写好或者关闭当前未填写的拜访');
      }
      if (callRecordId) {
        const nextOptions: typeof options = {
          callRecordId,
          bizScene: C33_CALL_ENUM.C33_AGENT_OUT_CALL,
          contactScene,
        };
        if (statusInfoRef.current?.callRecordId === callRecordId) {
          setStatusInfo(statusInfoRef.current);
        } else {
          // 没有通话
          setStatusInfo(undefined);
        }
        setOptions(nextOptions);
        setVisible(true);
      } else if (targetId && targetType) {
        setOptions({
          targetId,
          targetType,
          callReplenishFlag: replenish,
          bizScene: replenish ? C33_CALL_ENUM.C33_AGENT_OUT_CALL : C33_CALL_ENUM.C33_PC_DEFAULT,
          contactScene,
        });
        setStatusInfo(undefined);
        setVisible(true);
      }
    },
  );

  useEventHelper(
    callStatusChangeKey,
    (status: 'RINGING' | 'CONNECTED' | 'NOT_CONNECTED') => {
      setStatusInfo({
        status,
        errorMsg: undefined,
        callRecordId: echoWorkbench?.echo?.recordId,
      });
    },
    echoWorkbench,
  );
  return visible ? (
    <div className="visit-record-container">
      <Row className="visit-record-header" type="flex" justify="end">
        <Button
          onClick={() => {
            setVisible(false);
            handlePackUp();
            if (fefreshParams) {
              fefreshParams?.fefresh?.(fefreshParams?.params);
            }
          }}
          type="link"
        >
          收起
        </Button>
      </Row>
      <ComponentLoader appName="kb-visit" componentName="create-visit-record">
        {(Comp) => (
          <Comp
            {...options}
            sourceFrom="XY_PC_AGENT_VISIT_RECORD"
            statusInfo={statusInfo}
            defaultInputInfo={defaultInputInfo}
            afterCreate={() => {
              setVisible(false);
              handlePackUp();
              if (fefreshParams) {
                fefreshParams?.fefresh?.(fefreshParams?.params);
              }
            }}
          />
        )}
      </ComponentLoader>
    </div>
  ) : null;
};
