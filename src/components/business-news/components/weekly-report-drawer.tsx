import { useReportGray } from '@/hooks/useReportGray';
import { WeeklyReportDrawer as WeeklyReportDrawerNew } from '@/pages/business-news/components/weekly-report-drawer';
import { WeeklyReportDrawer as WeeklyReportDrawerOld } from '@/pages/business-news-old/components/weekly-report-drawer';

export default function WeeklyReportDrawer(props: any) {
  const { isGrayHit, hasReady } = useReportGray();
  if (!hasReady) {
    return null;
  }
  if (isGrayHit) {
    return <WeeklyReportDrawerNew {...props} />;
  }
  return <WeeklyReportDrawerOld {...props} />;
}
