import { useReportGray } from '@/hooks/useReportGray';
import WeeklyReportFormFormNew from '@/pages/business-news/components/weekly-report-form/form';
import WeeklyReportFormFormOld from '@/pages/business-news-old/components/weekly-report-form/form';

export default function WeeklyReportFormForm(props: any) {
  const { isGrayHit, hasReady } = useReportGray();
  if (!hasReady) {
    return null;
  }
  if (isGrayHit) {
    return <WeeklyReportFormFormNew {...props} />;
  }
  return <WeeklyReportFormFormOld {...props} />;
}
