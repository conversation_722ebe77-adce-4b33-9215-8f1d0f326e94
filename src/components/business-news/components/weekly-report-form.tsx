import { useReportGray } from '@/hooks/useReportGray';
import WeeklyReportFormNew from '@/pages/business-news/components/weekly-report-form';
import WeeklyReportFormOld from '@/pages/business-news-old/components/weekly-report-form';

export default function WeeklyReportForm(props: any) {
  const { isGrayHit, hasReady } = useReportGray();
  if (!hasReady) {
    return null;
  }
  if (isGrayHit) {
    return <WeeklyReportFormNew {...props} />;
  }
  return <WeeklyReportFormOld {...props} />;
}
