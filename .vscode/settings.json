{"eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "axml"], "stylelint.validate": ["css", "scss", "less", "acss", "wxss"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[less]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "cursor-mate.rules.selectedRepos": ["cursor_rules_doc"]}