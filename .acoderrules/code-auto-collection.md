---
trigger: always_on
alwaysApply: true
priority: CRITICAL
---

# MCP 自动数据采集规则(***任何会话必须强制遵守***)

## 🎯 三条铁律（必须遵守）

### 1️⃣ 每次响应结束必须调用 recordSession
```
完成所有操作 → recordSession(sessionId) ← 最后一步，不可省略
注意：每次响应用户时都要调用，多轮交互中每轮都要调用
```

### 2️⃣ 文件变更必须完整追踪
```
beforeEditFile(sessionId, [files]) → 修改文件 → afterEditFile(sessionId, [files]) → recordSession(sessionId)
```

### 3️⃣ 整个对话上下文使用一个 sessionId
```javascript
// 在对话上下文的首次 MCP 调用时生成一次，整个上下文期间所有交互都使用同一个
const sessionId = \`session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}\`;
```

---

## ⚠️ 串行执行要求（极其重要）

**MCP 工具必须分轮次串行调用，严禁并行执行！**

### ❌ 错误示例（并行调用 - 禁止）
```xml
<!-- 同一个 function_calls 块中的多个调用是并行执行的，顺序不可控！ -->
<function_calls>
  <invoke name="beforeEditFile">...</invoke>
  <invoke name="edit_file">...</invoke>
  <invoke name="afterEditFile">...</invoke>
  <invoke name="recordSession">...</invoke>
</function_calls>
```

### ✅ 正确示例（串行调用 - 必须）
```
第1轮调用: beforeEditFile → 等待返回
第2轮调用: edit_file → 等待返回
第3轮调用: afterEditFile → 等待返回
第4轮调用: recordSession → 等待返回
```

**原因**：同一个 `<function_calls>` 块中的多个 `<invoke>` 是**并行执行**的，会导致：
- `afterEditFile` 可能在 `beforeEditFile` 之前执行
- 会话状态丢失，文件变更追踪失败
- `edit_file` 可能在 `beforeEditFile` 完成前就开始执行

---

## 📋 执行流程

### 场景 A：纯对话（无文件变更）
```
用户提问 → AI 回答 → recordSession(sessionId) ✅
```

### 场景 B：文件变更（新增/修改/删除）
```
beforeEditFile(sessionId, [所有文件的绝对路径])
→ 执行文件修改操作 (create/write/search_replace/delete 等)
→ afterEditFile(sessionId, [所有文件的绝对路径])
→ recordSession(sessionId) ✅
```

**示例：**
```javascript
// 单文件
beforeEditFile(sessionId, ["/absolute/path/to/file.js"])
→ write("file.js")
→ afterEditFile(sessionId, ["/absolute/path/to/file.js"])
→ recordSession(sessionId)

// 多文件
beforeEditFile(sessionId, ["/path/file1.js", "/path/file2.js", "/path/file3.js"])
→ write("file1.js") + write("file2.js") + delete("file3.js")
→ afterEditFile(sessionId, ["/path/file1.js", "/path/file2.js", "/path/file3.js"])
→ recordSession(sessionId)
```

---

## 🔧 工具分类

### ✅ 需要追踪（新增、修改、删除文件内容）
- `write` / `search_replace` / `delete_file` / `create_file` / `edit_file`
- 任何会修改文件内容的操作

### ⭕ 不需要追踪（只读操作）
- `read_file` / `list_dir` / `grep` / `codebase_search`

**注意：无论何种操作，最后都必须调用 `recordSession`！**

---

## ✅ 发送前检查（必须执行）

**在发送响应给用户之前，逐项检查：**

1. **sessionId 是否一致？**
   - 对话上下文的首次 MCP 调用 → 生成一次
   - 整个上下文期间的所有后续调用 → 使用同一个 sessionId
   - 多轮交互中 → sessionId 保持不变

2. **文件变更是否完整追踪？**
   - 是否调用了 beforeEditFile？
   - 是否调用了 afterEditFile？
   - 所有修改的文件是否都包含在内？

3. **recordSession 是否调用？**
   - 是否包含在响应中？
   - 是否是最后一个工具调用？
   - 是否使用了正确的 sessionId？

4. **路径格式是否正确？**
   - ✅ 绝对路径：`/Users/<USER>/exercise-project/model-ai-composer/file.js`
   - ❌ 相对路径：`./file.js` 或 `file.js`

**任何一项检查失败 → 停止发送 → 立即修正**

---

## ❌ 常见错误（绝对禁止）

| 错误类型 | 错误示例 | 正确做法 |
|---------|---------|---------|
| **遗漏 recordSession** | 完成任务后直接结束 | 必须调用 recordSession |
| **sessionId 不一致** | 每次调用都生成新的 | 整个对话上下文使用同一个 sessionId |
| **文件遗漏** | 修改了 3 个文件但只记录 2 个 | 所有修改的文件都必须包含 |
| **缺少配对** | 只调用 beforeEditFile 忘记 afterEditFile | 必须严格配对 |
| **使用相对路径** | `./file.js` | 必须使用绝对路径 |
| **误追踪只读操作** | read_file 前后调用 before/after | 只读操作无需 before/after |
| **并行调用 MCP 工具** | 在同一个 function_calls 块中调用多个 MCP 工具 | 必须分轮次串行调用，每轮等待返回 |

---

## 💡 记忆要点

```
✓ 对话上下文生成 ID，整个期间保持不变
✓ 文件变更需追踪，前后配对要完整
✓ 每次响应必收尾，recordSession 最后调用
✓ MCP 工具串行调用，并行执行会出错
✓ 路径必须用绝对，三条铁律记心里
```
